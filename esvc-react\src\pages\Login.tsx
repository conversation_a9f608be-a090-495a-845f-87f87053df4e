import React, { useState } from 'react';

const Login: React.FC = () => {
  const [activeTab, setActiveTab] = useState<'login' | 'signup'>('login');

  return (
    <main className="w-full bg-neutral-900 min-h-screen">
      <div className="container mx-auto px-4 py-20">
        <div className="max-w-md mx-auto bg-neutral-800 rounded-lg p-8">
          <div className="flex mb-6">
            <button
              className={`flex-1 py-2 px-4 text-center rounded-l-lg ${
                activeTab === 'login'
                  ? 'bg-[#bf4129] text-white'
                  : 'bg-neutral-700 text-neutral-300'
              }`}
              onClick={() => setActiveTab('login')}
            >
              Login
            </button>
            <button
              className={`flex-1 py-2 px-4 text-center rounded-r-lg ${
                activeTab === 'signup'
                  ? 'bg-[#bf4129] text-white'
                  : 'bg-neutral-700 text-neutral-300'
              }`}
              onClick={() => setActiveTab('signup')}
            >
              Sign Up
            </button>
          </div>

          {activeTab === 'login' ? (
            <form className="space-y-4">
              <div>
                <label className="block text-neutral-300 text-sm font-medium mb-2">
                  Email
                </label>
                <input
                  type="email"
                  className="w-full px-3 py-2 bg-neutral-700 border border-neutral-600 rounded-md text-white focus:outline-none focus:border-[#bf4129]"
                  placeholder="Enter your email"
                />
              </div>
              <div>
                <label className="block text-neutral-300 text-sm font-medium mb-2">
                  Password
                </label>
                <input
                  type="password"
                  className="w-full px-3 py-2 bg-neutral-700 border border-neutral-600 rounded-md text-white focus:outline-none focus:border-[#bf4129]"
                  placeholder="Enter your password"
                />
              </div>
              <button
                type="submit"
                className="w-full bg-[#bf4129] hover:bg-[#a83a25] text-white py-2 px-4 rounded-md font-semibold transition-colors"
              >
                Login
              </button>
            </form>
          ) : (
            <form className="space-y-4">
              <div>
                <label className="block text-neutral-300 text-sm font-medium mb-2">
                  Full Name
                </label>
                <input
                  type="text"
                  className="w-full px-3 py-2 bg-neutral-700 border border-neutral-600 rounded-md text-white focus:outline-none focus:border-[#bf4129]"
                  placeholder="Enter your full name"
                />
              </div>
              <div>
                <label className="block text-neutral-300 text-sm font-medium mb-2">
                  Email
                </label>
                <input
                  type="email"
                  className="w-full px-3 py-2 bg-neutral-700 border border-neutral-600 rounded-md text-white focus:outline-none focus:border-[#bf4129]"
                  placeholder="Enter your email"
                />
              </div>
              <div>
                <label className="block text-neutral-300 text-sm font-medium mb-2">
                  Password
                </label>
                <input
                  type="password"
                  className="w-full px-3 py-2 bg-neutral-700 border border-neutral-600 rounded-md text-white focus:outline-none focus:border-[#bf4129]"
                  placeholder="Create a password"
                />
              </div>
              <div>
                <label className="block text-neutral-300 text-sm font-medium mb-2">
                  Confirm Password
                </label>
                <input
                  type="password"
                  className="w-full px-3 py-2 bg-neutral-700 border border-neutral-600 rounded-md text-white focus:outline-none focus:border-[#bf4129]"
                  placeholder="Confirm your password"
                />
              </div>
              <button
                type="submit"
                className="w-full bg-[#bf4129] hover:bg-[#a83a25] text-white py-2 px-4 rounded-md font-semibold transition-colors"
              >
                Sign Up
              </button>
            </form>
          )}
        </div>
      </div>
    </main>
  );
};

export default Login;
