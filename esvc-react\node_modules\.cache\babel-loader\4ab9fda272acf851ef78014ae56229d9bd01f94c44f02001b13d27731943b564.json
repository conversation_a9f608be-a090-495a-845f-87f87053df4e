{"ast": null, "code": "/**\n * react-router v7.7.1\n *\n * Copyright (c) Remix Software Inc.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE.md file in the root directory of this source tree.\n *\n * @license MIT\n */\nimport { ENABLE_DEV_WARNINGS, ErrorResponseImpl, FrameworkContext, NO_BODY_STATUS_CODES, Outlet, RSCRouterContext, RemixErrorBoundary, RouterProvider, SINGLE_FETCH_REDIRECT_STATUS, SingleFetchRedirectSymbol, StaticRouterProvider, StreamTransfer, convertRoutesToDataRoutes, createBrowserHistory, createMemoryRouter, createRequestInit, createRouter, createServerRoutes, createStaticHandler, createStaticRouter, decodeViaTurboStream, encode, getManifestPath, getSingleFetchDataStrategyImpl, getStaticContextFromError, invariant, isDataWithResponseInit, isMutationMethod, isRedirectResponse, isRedirectStatusCode, isResponse, isRouteErrorResponse, matchRoutes, noActionDefinedError, redirect, redirectDocument, replace, shouldHydrateRouteLoader, singleFetchUrl, stripBasename, stripIndexParam, unstable_RouterContextProvider, unstable_createContext, useRouteError, warnOnce, withComponentProps, withErrorBoundaryProps, withHydrateFallbackProps } from \"./chunk-C37GKA54.mjs\";\n\n// lib/dom/ssr/server.tsx\nimport * as React from \"react\";\nfunction ServerRouter({\n  context,\n  url,\n  nonce\n}) {\n  if (typeof url === \"string\") {\n    url = new URL(url);\n  }\n  let {\n    manifest,\n    routeModules,\n    criticalCss,\n    serverHandoffString\n  } = context;\n  let routes = createServerRoutes(manifest.routes, routeModules, context.future, context.isSpaMode);\n  context.staticHandlerContext.loaderData = {\n    ...context.staticHandlerContext.loaderData\n  };\n  for (let match of context.staticHandlerContext.matches) {\n    let routeId = match.route.id;\n    let route = routeModules[routeId];\n    let manifestRoute = context.manifest.routes[routeId];\n    if (route && manifestRoute && shouldHydrateRouteLoader(routeId, route.clientLoader, manifestRoute.hasLoader, context.isSpaMode) && (route.HydrateFallback || !manifestRoute.hasLoader)) {\n      delete context.staticHandlerContext.loaderData[routeId];\n    }\n  }\n  let router = createStaticRouter(routes, context.staticHandlerContext);\n  return /* @__PURE__ */React.createElement(React.Fragment, null, /* @__PURE__ */React.createElement(FrameworkContext.Provider, {\n    value: {\n      manifest,\n      routeModules,\n      criticalCss,\n      serverHandoffString,\n      future: context.future,\n      ssr: context.ssr,\n      isSpaMode: context.isSpaMode,\n      routeDiscovery: context.routeDiscovery,\n      serializeError: context.serializeError,\n      renderMeta: context.renderMeta\n    }\n  }, /* @__PURE__ */React.createElement(RemixErrorBoundary, {\n    location: router.state.location\n  }, /* @__PURE__ */React.createElement(StaticRouterProvider, {\n    router,\n    context: context.staticHandlerContext,\n    hydrate: false\n  }))), context.serverHandoffStream ? /* @__PURE__ */React.createElement(React.Suspense, null, /* @__PURE__ */React.createElement(StreamTransfer, {\n    context,\n    identifier: 0,\n    reader: context.serverHandoffStream.getReader(),\n    textDecoder: new TextDecoder(),\n    nonce\n  })) : null);\n}\n\n// lib/dom/ssr/routes-test-stub.tsx\nimport * as React2 from \"react\";\nfunction createRoutesStub(routes, _context) {\n  return function RoutesTestStub({\n    initialEntries,\n    initialIndex,\n    hydrationData,\n    future\n  }) {\n    let routerRef = React2.useRef();\n    let frameworkContextRef = React2.useRef();\n    if (routerRef.current == null) {\n      frameworkContextRef.current = {\n        future: {\n          unstable_subResourceIntegrity: future?.unstable_subResourceIntegrity === true,\n          unstable_middleware: future?.unstable_middleware === true\n        },\n        manifest: {\n          routes: {},\n          entry: {\n            imports: [],\n            module: \"\"\n          },\n          url: \"\",\n          version: \"\"\n        },\n        routeModules: {},\n        ssr: false,\n        isSpaMode: false,\n        routeDiscovery: {\n          mode: \"lazy\",\n          manifestPath: \"/__manifest\"\n        }\n      };\n      let patched = processRoutes(\n      // @ts-expect-error `StubRouteObject` is stricter about `loader`/`action`\n      // types compared to `AgnosticRouteObject`\n      convertRoutesToDataRoutes(routes, r => r), _context !== void 0 ? _context : future?.unstable_middleware ? new unstable_RouterContextProvider() : {}, frameworkContextRef.current.manifest, frameworkContextRef.current.routeModules);\n      routerRef.current = createMemoryRouter(patched, {\n        initialEntries,\n        initialIndex,\n        hydrationData\n      });\n    }\n    return /* @__PURE__ */React2.createElement(FrameworkContext.Provider, {\n      value: frameworkContextRef.current\n    }, /* @__PURE__ */React2.createElement(RouterProvider, {\n      router: routerRef.current\n    }));\n  };\n}\nfunction processRoutes(routes, context, manifest, routeModules, parentId) {\n  return routes.map(route => {\n    if (!route.id) {\n      throw new Error(\"Expected a route.id in react-router processRoutes() function\");\n    }\n    let newRoute = {\n      id: route.id,\n      path: route.path,\n      index: route.index,\n      Component: route.Component ? withComponentProps(route.Component) : void 0,\n      HydrateFallback: route.HydrateFallback ? withHydrateFallbackProps(route.HydrateFallback) : void 0,\n      ErrorBoundary: route.ErrorBoundary ? withErrorBoundaryProps(route.ErrorBoundary) : void 0,\n      action: route.action ? args => route.action({\n        ...args,\n        context\n      }) : void 0,\n      loader: route.loader ? args => route.loader({\n        ...args,\n        context\n      }) : void 0,\n      handle: route.handle,\n      shouldRevalidate: route.shouldRevalidate\n    };\n    let entryRoute = {\n      id: route.id,\n      path: route.path,\n      index: route.index,\n      parentId,\n      hasAction: route.action != null,\n      hasLoader: route.loader != null,\n      // When testing routes, you should be stubbing loader/action/middleware,\n      // not trying to re-implement the full loader/clientLoader/SSR/hydration\n      // flow. That is better tested via E2E tests.\n      hasClientAction: false,\n      hasClientLoader: false,\n      hasClientMiddleware: false,\n      hasErrorBoundary: route.ErrorBoundary != null,\n      // any need for these?\n      module: \"build/stub-path-to-module.js\",\n      clientActionModule: void 0,\n      clientLoaderModule: void 0,\n      clientMiddlewareModule: void 0,\n      hydrateFallbackModule: void 0\n    };\n    manifest.routes[newRoute.id] = entryRoute;\n    routeModules[route.id] = {\n      default: newRoute.Component || Outlet,\n      ErrorBoundary: newRoute.ErrorBoundary || void 0,\n      handle: route.handle,\n      links: route.links,\n      meta: route.meta,\n      shouldRevalidate: route.shouldRevalidate\n    };\n    if (route.children) {\n      newRoute.children = processRoutes(route.children, context, manifest, routeModules, newRoute.id);\n    }\n    return newRoute;\n  });\n}\n\n// lib/server-runtime/cookies.ts\nimport { parse, serialize } from \"cookie\";\n\n// lib/server-runtime/crypto.ts\nvar encoder = /* @__PURE__ */new TextEncoder();\nvar sign = async (value, secret) => {\n  let data2 = encoder.encode(value);\n  let key = await createKey(secret, [\"sign\"]);\n  let signature = await crypto.subtle.sign(\"HMAC\", key, data2);\n  let hash = btoa(String.fromCharCode(...new Uint8Array(signature))).replace(/=+$/, \"\");\n  return value + \".\" + hash;\n};\nvar unsign = async (cookie, secret) => {\n  let index = cookie.lastIndexOf(\".\");\n  let value = cookie.slice(0, index);\n  let hash = cookie.slice(index + 1);\n  let data2 = encoder.encode(value);\n  let key = await createKey(secret, [\"verify\"]);\n  try {\n    let signature = byteStringToUint8Array(atob(hash));\n    let valid = await crypto.subtle.verify(\"HMAC\", key, signature, data2);\n    return valid ? value : false;\n  } catch (error) {\n    return false;\n  }\n};\nvar createKey = async (secret, usages) => crypto.subtle.importKey(\"raw\", encoder.encode(secret), {\n  name: \"HMAC\",\n  hash: \"SHA-256\"\n}, false, usages);\nfunction byteStringToUint8Array(byteString) {\n  let array = new Uint8Array(byteString.length);\n  for (let i = 0; i < byteString.length; i++) {\n    array[i] = byteString.charCodeAt(i);\n  }\n  return array;\n}\n\n// lib/server-runtime/cookies.ts\nvar createCookie = (name, cookieOptions = {}) => {\n  let {\n    secrets = [],\n    ...options\n  } = {\n    path: \"/\",\n    sameSite: \"lax\",\n    ...cookieOptions\n  };\n  warnOnceAboutExpiresCookie(name, options.expires);\n  return {\n    get name() {\n      return name;\n    },\n    get isSigned() {\n      return secrets.length > 0;\n    },\n    get expires() {\n      return typeof options.maxAge !== \"undefined\" ? new Date(Date.now() + options.maxAge * 1e3) : options.expires;\n    },\n    async parse(cookieHeader, parseOptions) {\n      if (!cookieHeader) return null;\n      let cookies = parse(cookieHeader, {\n        ...options,\n        ...parseOptions\n      });\n      if (name in cookies) {\n        let value = cookies[name];\n        if (typeof value === \"string\" && value !== \"\") {\n          let decoded = await decodeCookieValue(value, secrets);\n          return decoded;\n        } else {\n          return \"\";\n        }\n      } else {\n        return null;\n      }\n    },\n    async serialize(value, serializeOptions) {\n      return serialize(name, value === \"\" ? \"\" : await encodeCookieValue(value, secrets), {\n        ...options,\n        ...serializeOptions\n      });\n    }\n  };\n};\nvar isCookie = object => {\n  return object != null && typeof object.name === \"string\" && typeof object.isSigned === \"boolean\" && typeof object.parse === \"function\" && typeof object.serialize === \"function\";\n};\nasync function encodeCookieValue(value, secrets) {\n  let encoded = encodeData(value);\n  if (secrets.length > 0) {\n    encoded = await sign(encoded, secrets[0]);\n  }\n  return encoded;\n}\nasync function decodeCookieValue(value, secrets) {\n  if (secrets.length > 0) {\n    for (let secret of secrets) {\n      let unsignedValue = await unsign(value, secret);\n      if (unsignedValue !== false) {\n        return decodeData(unsignedValue);\n      }\n    }\n    return null;\n  }\n  return decodeData(value);\n}\nfunction encodeData(value) {\n  return btoa(myUnescape(encodeURIComponent(JSON.stringify(value))));\n}\nfunction decodeData(value) {\n  try {\n    return JSON.parse(decodeURIComponent(myEscape(atob(value))));\n  } catch (error) {\n    return {};\n  }\n}\nfunction myEscape(value) {\n  let str = value.toString();\n  let result = \"\";\n  let index = 0;\n  let chr, code;\n  while (index < str.length) {\n    chr = str.charAt(index++);\n    if (/[\\w*+\\-./@]/.exec(chr)) {\n      result += chr;\n    } else {\n      code = chr.charCodeAt(0);\n      if (code < 256) {\n        result += \"%\" + hex(code, 2);\n      } else {\n        result += \"%u\" + hex(code, 4).toUpperCase();\n      }\n    }\n  }\n  return result;\n}\nfunction hex(code, length) {\n  let result = code.toString(16);\n  while (result.length < length) result = \"0\" + result;\n  return result;\n}\nfunction myUnescape(value) {\n  let str = value.toString();\n  let result = \"\";\n  let index = 0;\n  let chr, part;\n  while (index < str.length) {\n    chr = str.charAt(index++);\n    if (chr === \"%\") {\n      if (str.charAt(index) === \"u\") {\n        part = str.slice(index + 1, index + 5);\n        if (/^[\\da-f]{4}$/i.exec(part)) {\n          result += String.fromCharCode(parseInt(part, 16));\n          index += 5;\n          continue;\n        }\n      } else {\n        part = str.slice(index, index + 2);\n        if (/^[\\da-f]{2}$/i.exec(part)) {\n          result += String.fromCharCode(parseInt(part, 16));\n          index += 2;\n          continue;\n        }\n      }\n    }\n    result += chr;\n  }\n  return result;\n}\nfunction warnOnceAboutExpiresCookie(name, expires) {\n  warnOnce(!expires, `The \"${name}\" cookie has an \"expires\" property set. This will cause the expires value to not be updated when the session is committed. Instead, you should set the expires value when serializing the cookie. You can use \\`commitSession(session, { expires })\\` if using a session storage object, or \\`cookie.serialize(\"value\", { expires })\\` if you're using the cookie directly.`);\n}\n\n// lib/server-runtime/entry.ts\nfunction createEntryRouteModules(manifest) {\n  return Object.keys(manifest).reduce((memo, routeId) => {\n    let route = manifest[routeId];\n    if (route) {\n      memo[routeId] = route.module;\n    }\n    return memo;\n  }, {});\n}\n\n// lib/server-runtime/mode.ts\nvar ServerMode = /* @__PURE__ */(ServerMode2 => {\n  ServerMode2[\"Development\"] = \"development\";\n  ServerMode2[\"Production\"] = \"production\";\n  ServerMode2[\"Test\"] = \"test\";\n  return ServerMode2;\n})(ServerMode || {});\nfunction isServerMode(value) {\n  return value === \"development\" /* Development */ || value === \"production\" /* Production */ || value === \"test\" /* Test */;\n}\n\n// lib/server-runtime/errors.ts\nfunction sanitizeError(error, serverMode) {\n  if (error instanceof Error && serverMode !== \"development\" /* Development */) {\n    let sanitized = new Error(\"Unexpected Server Error\");\n    sanitized.stack = void 0;\n    return sanitized;\n  }\n  return error;\n}\nfunction sanitizeErrors(errors, serverMode) {\n  return Object.entries(errors).reduce((acc, [routeId, error]) => {\n    return Object.assign(acc, {\n      [routeId]: sanitizeError(error, serverMode)\n    });\n  }, {});\n}\nfunction serializeError(error, serverMode) {\n  let sanitized = sanitizeError(error, serverMode);\n  return {\n    message: sanitized.message,\n    stack: sanitized.stack\n  };\n}\nfunction serializeErrors(errors, serverMode) {\n  if (!errors) return null;\n  let entries = Object.entries(errors);\n  let serialized = {};\n  for (let [key, val] of entries) {\n    if (isRouteErrorResponse(val)) {\n      serialized[key] = {\n        ...val,\n        __type: \"RouteErrorResponse\"\n      };\n    } else if (val instanceof Error) {\n      let sanitized = sanitizeError(val, serverMode);\n      serialized[key] = {\n        message: sanitized.message,\n        stack: sanitized.stack,\n        __type: \"Error\",\n        // If this is a subclass (i.e., ReferenceError), send up the type so we\n        // can re-create the same type during hydration.  This will only apply\n        // in dev mode since all production errors are sanitized to normal\n        // Error instances\n        ...(sanitized.name !== \"Error\" ? {\n          __subType: sanitized.name\n        } : {})\n      };\n    } else {\n      serialized[key] = val;\n    }\n  }\n  return serialized;\n}\n\n// lib/server-runtime/routeMatching.ts\nfunction matchServerRoutes(routes, pathname, basename) {\n  let matches = matchRoutes(routes, pathname, basename);\n  if (!matches) return null;\n  return matches.map(match => ({\n    params: match.params,\n    pathname: match.pathname,\n    route: match.route\n  }));\n}\n\n// lib/server-runtime/data.ts\nasync function callRouteHandler(handler, args) {\n  let result = await handler({\n    request: stripRoutesParam(stripIndexParam2(args.request)),\n    params: args.params,\n    context: args.context\n  });\n  if (isDataWithResponseInit(result) && result.init && result.init.status && isRedirectStatusCode(result.init.status)) {\n    throw new Response(null, result.init);\n  }\n  return result;\n}\nfunction stripIndexParam2(request) {\n  let url = new URL(request.url);\n  let indexValues = url.searchParams.getAll(\"index\");\n  url.searchParams.delete(\"index\");\n  let indexValuesToKeep = [];\n  for (let indexValue of indexValues) {\n    if (indexValue) {\n      indexValuesToKeep.push(indexValue);\n    }\n  }\n  for (let toKeep of indexValuesToKeep) {\n    url.searchParams.append(\"index\", toKeep);\n  }\n  let init = {\n    method: request.method,\n    body: request.body,\n    headers: request.headers,\n    signal: request.signal\n  };\n  if (init.body) {\n    init.duplex = \"half\";\n  }\n  return new Request(url.href, init);\n}\nfunction stripRoutesParam(request) {\n  let url = new URL(request.url);\n  url.searchParams.delete(\"_routes\");\n  let init = {\n    method: request.method,\n    body: request.body,\n    headers: request.headers,\n    signal: request.signal\n  };\n  if (init.body) {\n    init.duplex = \"half\";\n  }\n  return new Request(url.href, init);\n}\n\n// lib/server-runtime/invariant.ts\nfunction invariant2(value, message) {\n  if (value === false || value === null || typeof value === \"undefined\") {\n    console.error(\"The following error is a bug in React Router; please open an issue! https://github.com/remix-run/react-router/issues/new/choose\");\n    throw new Error(message);\n  }\n}\n\n// lib/server-runtime/dev.ts\nvar globalDevServerHooksKey = \"__reactRouterDevServerHooks\";\nfunction setDevServerHooks(devServerHooks) {\n  globalThis[globalDevServerHooksKey] = devServerHooks;\n}\nfunction getDevServerHooks() {\n  return globalThis[globalDevServerHooksKey];\n}\nfunction getBuildTimeHeader(request, headerName) {\n  if (typeof process !== \"undefined\") {\n    try {\n      if (process.env?.IS_RR_BUILD_REQUEST === \"yes\") {\n        return request.headers.get(headerName);\n      }\n    } catch (e) {}\n  }\n  return null;\n}\n\n// lib/server-runtime/routes.ts\nfunction groupRoutesByParentId(manifest) {\n  let routes = {};\n  Object.values(manifest).forEach(route => {\n    if (route) {\n      let parentId = route.parentId || \"\";\n      if (!routes[parentId]) {\n        routes[parentId] = [];\n      }\n      routes[parentId].push(route);\n    }\n  });\n  return routes;\n}\nfunction createRoutes(manifest, parentId = \"\", routesByParentId = groupRoutesByParentId(manifest)) {\n  return (routesByParentId[parentId] || []).map(route => ({\n    ...route,\n    children: createRoutes(manifest, route.id, routesByParentId)\n  }));\n}\nfunction createStaticHandlerDataRoutes(manifest, future, parentId = \"\", routesByParentId = groupRoutesByParentId(manifest)) {\n  return (routesByParentId[parentId] || []).map(route => {\n    let commonRoute = {\n      // Always include root due to default boundaries\n      hasErrorBoundary: route.id === \"root\" || route.module.ErrorBoundary != null,\n      id: route.id,\n      path: route.path,\n      unstable_middleware: route.module.unstable_middleware,\n      // Need to use RR's version in the param typed here to permit the optional\n      // context even though we know it'll always be provided in remix\n      loader: route.module.loader ? async args => {\n        let preRenderedData = getBuildTimeHeader(args.request, \"X-React-Router-Prerender-Data\");\n        if (preRenderedData != null) {\n          let encoded = preRenderedData ? decodeURI(preRenderedData) : preRenderedData;\n          invariant2(encoded, \"Missing prerendered data for route\");\n          let uint8array = new TextEncoder().encode(encoded);\n          let stream = new ReadableStream({\n            start(controller) {\n              controller.enqueue(uint8array);\n              controller.close();\n            }\n          });\n          let decoded = await decodeViaTurboStream(stream, global);\n          let data2 = decoded.value;\n          if (data2 && SingleFetchRedirectSymbol in data2) {\n            let result = data2[SingleFetchRedirectSymbol];\n            let init = {\n              status: result.status\n            };\n            if (result.reload) {\n              throw redirectDocument(result.redirect, init);\n            } else if (result.replace) {\n              throw replace(result.redirect, init);\n            } else {\n              throw redirect(result.redirect, init);\n            }\n          } else {\n            invariant2(data2 && route.id in data2, \"Unable to decode prerendered data\");\n            let result = data2[route.id];\n            invariant2(\"data\" in result, \"Unable to process prerendered data\");\n            return result.data;\n          }\n        }\n        let val = await callRouteHandler(route.module.loader, args);\n        return val;\n      } : void 0,\n      action: route.module.action ? args => callRouteHandler(route.module.action, args) : void 0,\n      handle: route.module.handle\n    };\n    return route.index ? {\n      index: true,\n      ...commonRoute\n    } : {\n      caseSensitive: route.caseSensitive,\n      children: createStaticHandlerDataRoutes(manifest, future, route.id, routesByParentId),\n      ...commonRoute\n    };\n  });\n}\n\n// lib/server-runtime/markup.ts\nvar ESCAPE_LOOKUP = {\n  \"&\": \"\\\\u0026\",\n  \">\": \"\\\\u003e\",\n  \"<\": \"\\\\u003c\",\n  \"\\u2028\": \"\\\\u2028\",\n  \"\\u2029\": \"\\\\u2029\"\n};\nvar ESCAPE_REGEX = /[&><\\u2028\\u2029]/g;\nfunction escapeHtml(html) {\n  return html.replace(ESCAPE_REGEX, match => ESCAPE_LOOKUP[match]);\n}\n\n// lib/server-runtime/serverHandoff.ts\nfunction createServerHandoffString(serverHandoff) {\n  return escapeHtml(JSON.stringify(serverHandoff));\n}\n\n// lib/server-runtime/headers.ts\nimport { splitCookiesString } from \"set-cookie-parser\";\nfunction getDocumentHeaders(context, build) {\n  return getDocumentHeadersImpl(context, m => {\n    let route = build.routes[m.route.id];\n    invariant2(route, `Route with id \"${m.route.id}\" not found in build`);\n    return route.module.headers;\n  });\n}\nfunction getDocumentHeadersImpl(context, getRouteHeadersFn) {\n  let boundaryIdx = context.errors ? context.matches.findIndex(m => context.errors[m.route.id]) : -1;\n  let matches = boundaryIdx >= 0 ? context.matches.slice(0, boundaryIdx + 1) : context.matches;\n  let errorHeaders;\n  if (boundaryIdx >= 0) {\n    let {\n      actionHeaders,\n      actionData,\n      loaderHeaders,\n      loaderData\n    } = context;\n    context.matches.slice(boundaryIdx).some(match => {\n      let id = match.route.id;\n      if (actionHeaders[id] && (!actionData || !actionData.hasOwnProperty(id))) {\n        errorHeaders = actionHeaders[id];\n      } else if (loaderHeaders[id] && !loaderData.hasOwnProperty(id)) {\n        errorHeaders = loaderHeaders[id];\n      }\n      return errorHeaders != null;\n    });\n  }\n  return matches.reduce((parentHeaders, match, idx) => {\n    let {\n      id\n    } = match.route;\n    let loaderHeaders = context.loaderHeaders[id] || new Headers();\n    let actionHeaders = context.actionHeaders[id] || new Headers();\n    let includeErrorHeaders = errorHeaders != null && idx === matches.length - 1;\n    let includeErrorCookies = includeErrorHeaders && errorHeaders !== loaderHeaders && errorHeaders !== actionHeaders;\n    let headersFn = getRouteHeadersFn(match);\n    if (headersFn == null) {\n      let headers2 = new Headers(parentHeaders);\n      if (includeErrorCookies) {\n        prependCookies(errorHeaders, headers2);\n      }\n      prependCookies(actionHeaders, headers2);\n      prependCookies(loaderHeaders, headers2);\n      return headers2;\n    }\n    let headers = new Headers(typeof headersFn === \"function\" ? headersFn({\n      loaderHeaders,\n      parentHeaders,\n      actionHeaders,\n      errorHeaders: includeErrorHeaders ? errorHeaders : void 0\n    }) : headersFn);\n    if (includeErrorCookies) {\n      prependCookies(errorHeaders, headers);\n    }\n    prependCookies(actionHeaders, headers);\n    prependCookies(loaderHeaders, headers);\n    prependCookies(parentHeaders, headers);\n    return headers;\n  }, new Headers());\n}\nfunction prependCookies(parentHeaders, childHeaders) {\n  let parentSetCookieString = parentHeaders.get(\"Set-Cookie\");\n  if (parentSetCookieString) {\n    let cookies = splitCookiesString(parentSetCookieString);\n    let childCookies = new Set(childHeaders.getSetCookie());\n    cookies.forEach(cookie => {\n      if (!childCookies.has(cookie)) {\n        childHeaders.append(\"Set-Cookie\", cookie);\n      }\n    });\n  }\n}\n\n// lib/server-runtime/single-fetch.ts\nvar SERVER_NO_BODY_STATUS_CODES = /* @__PURE__ */new Set([...NO_BODY_STATUS_CODES, 304]);\nasync function singleFetchAction(build, serverMode, staticHandler, request, handlerUrl, loadContext, handleError) {\n  try {\n    let respond2 = function (context) {\n      let headers = getDocumentHeaders(context, build);\n      if (isRedirectStatusCode(context.statusCode) && headers.has(\"Location\")) {\n        return generateSingleFetchResponse(request, build, serverMode, {\n          result: getSingleFetchRedirect(context.statusCode, headers, build.basename),\n          headers,\n          status: SINGLE_FETCH_REDIRECT_STATUS\n        });\n      }\n      if (context.errors) {\n        Object.values(context.errors).forEach(err => {\n          if (!isRouteErrorResponse(err) || err.error) {\n            handleError(err);\n          }\n        });\n        context.errors = sanitizeErrors(context.errors, serverMode);\n      }\n      let singleFetchResult;\n      if (context.errors) {\n        singleFetchResult = {\n          error: Object.values(context.errors)[0]\n        };\n      } else {\n        singleFetchResult = {\n          data: Object.values(context.actionData || {})[0]\n        };\n      }\n      return generateSingleFetchResponse(request, build, serverMode, {\n        result: singleFetchResult,\n        headers,\n        status: context.statusCode\n      });\n    };\n    var respond = respond2;\n    let handlerRequest = new Request(handlerUrl, {\n      method: request.method,\n      body: request.body,\n      headers: request.headers,\n      signal: request.signal,\n      ...(request.body ? {\n        duplex: \"half\"\n      } : void 0)\n    });\n    let result = await staticHandler.query(handlerRequest, {\n      requestContext: loadContext,\n      skipLoaderErrorBubbling: true,\n      skipRevalidation: true,\n      unstable_respond: respond2\n    });\n    if (!isResponse(result)) {\n      result = respond2(result);\n    }\n    if (isRedirectResponse(result)) {\n      return generateSingleFetchResponse(request, build, serverMode, {\n        result: getSingleFetchRedirect(result.status, result.headers, build.basename),\n        headers: result.headers,\n        status: SINGLE_FETCH_REDIRECT_STATUS\n      });\n    }\n    return result;\n  } catch (error) {\n    handleError(error);\n    return generateSingleFetchResponse(request, build, serverMode, {\n      result: {\n        error\n      },\n      headers: new Headers(),\n      status: 500\n    });\n  }\n}\nasync function singleFetchLoaders(build, serverMode, staticHandler, request, handlerUrl, loadContext, handleError) {\n  try {\n    let respond2 = function (context) {\n      let headers = getDocumentHeaders(context, build);\n      if (isRedirectStatusCode(context.statusCode) && headers.has(\"Location\")) {\n        return generateSingleFetchResponse(request, build, serverMode, {\n          result: {\n            [SingleFetchRedirectSymbol]: getSingleFetchRedirect(context.statusCode, headers, build.basename)\n          },\n          headers,\n          status: SINGLE_FETCH_REDIRECT_STATUS\n        });\n      }\n      if (context.errors) {\n        Object.values(context.errors).forEach(err => {\n          if (!isRouteErrorResponse(err) || err.error) {\n            handleError(err);\n          }\n        });\n        context.errors = sanitizeErrors(context.errors, serverMode);\n      }\n      let results = {};\n      let loadedMatches = new Set(context.matches.filter(m => loadRouteIds ? loadRouteIds.has(m.route.id) : m.route.loader != null).map(m => m.route.id));\n      if (context.errors) {\n        for (let [id, error] of Object.entries(context.errors)) {\n          results[id] = {\n            error\n          };\n        }\n      }\n      for (let [id, data2] of Object.entries(context.loaderData)) {\n        if (!(id in results) && loadedMatches.has(id)) {\n          results[id] = {\n            data: data2\n          };\n        }\n      }\n      return generateSingleFetchResponse(request, build, serverMode, {\n        result: results,\n        headers,\n        status: context.statusCode\n      });\n    };\n    var respond = respond2;\n    let handlerRequest = new Request(handlerUrl, {\n      headers: request.headers,\n      signal: request.signal\n    });\n    let routesParam = new URL(request.url).searchParams.get(\"_routes\");\n    let loadRouteIds = routesParam ? new Set(routesParam.split(\",\")) : null;\n    let result = await staticHandler.query(handlerRequest, {\n      requestContext: loadContext,\n      filterMatchesToLoad: m => !loadRouteIds || loadRouteIds.has(m.route.id),\n      skipLoaderErrorBubbling: true,\n      unstable_respond: respond2\n    });\n    if (!isResponse(result)) {\n      result = respond2(result);\n    }\n    if (isRedirectResponse(result)) {\n      return generateSingleFetchResponse(request, build, serverMode, {\n        result: {\n          [SingleFetchRedirectSymbol]: getSingleFetchRedirect(result.status, result.headers, build.basename)\n        },\n        headers: result.headers,\n        status: SINGLE_FETCH_REDIRECT_STATUS\n      });\n    }\n    return result;\n  } catch (error) {\n    handleError(error);\n    return generateSingleFetchResponse(request, build, serverMode, {\n      result: {\n        root: {\n          error\n        }\n      },\n      headers: new Headers(),\n      status: 500\n    });\n  }\n}\nfunction generateSingleFetchResponse(request, build, serverMode, {\n  result,\n  headers,\n  status\n}) {\n  let resultHeaders = new Headers(headers);\n  resultHeaders.set(\"X-Remix-Response\", \"yes\");\n  if (SERVER_NO_BODY_STATUS_CODES.has(status)) {\n    return new Response(null, {\n      status,\n      headers: resultHeaders\n    });\n  }\n  resultHeaders.set(\"Content-Type\", \"text/x-script\");\n  resultHeaders.delete(\"Content-Length\");\n  return new Response(encodeViaTurboStream(result, request.signal, build.entry.module.streamTimeout, serverMode), {\n    status: status || 200,\n    headers: resultHeaders\n  });\n}\nfunction getSingleFetchRedirect(status, headers, basename) {\n  let redirect2 = headers.get(\"Location\");\n  if (basename) {\n    redirect2 = stripBasename(redirect2, basename) || redirect2;\n  }\n  return {\n    redirect: redirect2,\n    status,\n    revalidate:\n    // Technically X-Remix-Revalidate isn't needed here - that was an implementation\n    // detail of ?_data requests as our way to tell the front end to revalidate when\n    // we didn't have a response body to include that information in.\n    // With single fetch, we tell the front end via this revalidate boolean field.\n    // However, we're respecting it for now because it may be something folks have\n    // used in their own responses\n    // TODO(v3): Consider removing or making this official public API\n    headers.has(\"X-Remix-Revalidate\") || headers.has(\"Set-Cookie\"),\n    reload: headers.has(\"X-Remix-Reload-Document\"),\n    replace: headers.has(\"X-Remix-Replace\")\n  };\n}\nfunction encodeViaTurboStream(data2, requestSignal, streamTimeout, serverMode) {\n  let controller = new AbortController();\n  let timeoutId = setTimeout(() => controller.abort(new Error(\"Server Timeout\")), typeof streamTimeout === \"number\" ? streamTimeout : 4950);\n  requestSignal.addEventListener(\"abort\", () => clearTimeout(timeoutId));\n  return encode(data2, {\n    signal: controller.signal,\n    plugins: [value => {\n      if (value instanceof Error) {\n        let {\n          name,\n          message,\n          stack\n        } = serverMode === \"production\" /* Production */ ? sanitizeError(value, serverMode) : value;\n        return [\"SanitizedError\", name, message, stack];\n      }\n      if (value instanceof ErrorResponseImpl) {\n        let {\n          data: data3,\n          status,\n          statusText\n        } = value;\n        return [\"ErrorResponse\", data3, status, statusText];\n      }\n      if (value && typeof value === \"object\" && SingleFetchRedirectSymbol in value) {\n        return [\"SingleFetchRedirect\", value[SingleFetchRedirectSymbol]];\n      }\n    }],\n    postPlugins: [value => {\n      if (!value) return;\n      if (typeof value !== \"object\") return;\n      return [\"SingleFetchClassInstance\", Object.fromEntries(Object.entries(value))];\n    }, () => [\"SingleFetchFallback\"]]\n  });\n}\n\n// lib/server-runtime/server.ts\nfunction derive(build, mode) {\n  let routes = createRoutes(build.routes);\n  let dataRoutes = createStaticHandlerDataRoutes(build.routes, build.future);\n  let serverMode = isServerMode(mode) ? mode : \"production\" /* Production */;\n  let staticHandler = createStaticHandler(dataRoutes, {\n    basename: build.basename\n  });\n  let errorHandler = build.entry.module.handleError || ((error, {\n    request\n  }) => {\n    if (serverMode !== \"test\" /* Test */ && !request.signal.aborted) {\n      console.error(\n      // @ts-expect-error This is \"private\" from users but intended for internal use\n      isRouteErrorResponse(error) && error.error ? error.error : error);\n    }\n  });\n  return {\n    routes,\n    dataRoutes,\n    serverMode,\n    staticHandler,\n    errorHandler\n  };\n}\nvar createRequestHandler = (build, mode) => {\n  let _build;\n  let routes;\n  let serverMode;\n  let staticHandler;\n  let errorHandler;\n  return async function requestHandler(request, initialContext) {\n    _build = typeof build === \"function\" ? await build() : build;\n    if (typeof build === \"function\") {\n      let derived = derive(_build, mode);\n      routes = derived.routes;\n      serverMode = derived.serverMode;\n      staticHandler = derived.staticHandler;\n      errorHandler = derived.errorHandler;\n    } else if (!routes || !serverMode || !staticHandler || !errorHandler) {\n      let derived = derive(_build, mode);\n      routes = derived.routes;\n      serverMode = derived.serverMode;\n      staticHandler = derived.staticHandler;\n      errorHandler = derived.errorHandler;\n    }\n    let params = {};\n    let loadContext;\n    let handleError = error => {\n      if (mode === \"development\" /* Development */) {\n        getDevServerHooks()?.processRequestError?.(error);\n      }\n      errorHandler(error, {\n        context: loadContext,\n        params,\n        request\n      });\n    };\n    if (_build.future.unstable_middleware) {\n      if (initialContext == null) {\n        loadContext = new unstable_RouterContextProvider();\n      } else {\n        try {\n          loadContext = new unstable_RouterContextProvider(initialContext);\n        } catch (e) {\n          let error = new Error(`Unable to create initial \\`unstable_RouterContextProvider\\` instance. Please confirm you are returning an instance of \\`Map<unstable_routerContext, unknown>\\` from your \\`getLoadContext\\` function.\n\nError: ${e instanceof Error ? e.toString() : e}`);\n          handleError(error);\n          return returnLastResortErrorResponse(error, serverMode);\n        }\n      }\n    } else {\n      loadContext = initialContext || {};\n    }\n    let url = new URL(request.url);\n    let normalizedBasename = _build.basename || \"/\";\n    let normalizedPath = url.pathname;\n    if (stripBasename(normalizedPath, normalizedBasename) === \"/_root.data\") {\n      normalizedPath = normalizedBasename;\n    } else if (normalizedPath.endsWith(\".data\")) {\n      normalizedPath = normalizedPath.replace(/\\.data$/, \"\");\n    }\n    if (stripBasename(normalizedPath, normalizedBasename) !== \"/\" && normalizedPath.endsWith(\"/\")) {\n      normalizedPath = normalizedPath.slice(0, -1);\n    }\n    let isSpaMode = getBuildTimeHeader(request, \"X-React-Router-SPA-Mode\") === \"yes\";\n    if (!_build.ssr) {\n      let decodedPath = decodeURI(normalizedPath);\n      if (_build.prerender.length === 0) {\n        isSpaMode = true;\n      } else if (!_build.prerender.includes(decodedPath) && !_build.prerender.includes(decodedPath + \"/\")) {\n        if (url.pathname.endsWith(\".data\")) {\n          errorHandler(new ErrorResponseImpl(404, \"Not Found\", `Refusing to SSR the path \\`${decodedPath}\\` because \\`ssr:false\\` is set and the path is not included in the \\`prerender\\` config, so in production the path will be a 404.`), {\n            context: loadContext,\n            params,\n            request\n          });\n          return new Response(\"Not Found\", {\n            status: 404,\n            statusText: \"Not Found\"\n          });\n        } else {\n          isSpaMode = true;\n        }\n      }\n    }\n    let manifestUrl = getManifestPath(_build.routeDiscovery.manifestPath, normalizedBasename);\n    if (url.pathname === manifestUrl) {\n      try {\n        let res = await handleManifestRequest(_build, routes, url);\n        return res;\n      } catch (e) {\n        handleError(e);\n        return new Response(\"Unknown Server Error\", {\n          status: 500\n        });\n      }\n    }\n    let matches = matchServerRoutes(routes, normalizedPath, _build.basename);\n    if (matches && matches.length > 0) {\n      Object.assign(params, matches[0].params);\n    }\n    let response;\n    if (url.pathname.endsWith(\".data\")) {\n      let handlerUrl = new URL(request.url);\n      handlerUrl.pathname = normalizedPath;\n      let singleFetchMatches = matchServerRoutes(routes, handlerUrl.pathname, _build.basename);\n      response = await handleSingleFetchRequest(serverMode, _build, staticHandler, request, handlerUrl, loadContext, handleError);\n      if (_build.entry.module.handleDataRequest) {\n        response = await _build.entry.module.handleDataRequest(response, {\n          context: loadContext,\n          params: singleFetchMatches ? singleFetchMatches[0].params : {},\n          request\n        });\n        if (isRedirectResponse(response)) {\n          let result = getSingleFetchRedirect(response.status, response.headers, _build.basename);\n          if (request.method === \"GET\") {\n            result = {\n              [SingleFetchRedirectSymbol]: result\n            };\n          }\n          let headers = new Headers(response.headers);\n          headers.set(\"Content-Type\", \"text/x-script\");\n          return new Response(encodeViaTurboStream(result, request.signal, _build.entry.module.streamTimeout, serverMode), {\n            status: SINGLE_FETCH_REDIRECT_STATUS,\n            headers\n          });\n        }\n      }\n    } else if (!isSpaMode && matches && matches[matches.length - 1].route.module.default == null && matches[matches.length - 1].route.module.ErrorBoundary == null) {\n      response = await handleResourceRequest(serverMode, _build, staticHandler, matches.slice(-1)[0].route.id, request, loadContext, handleError);\n    } else {\n      let {\n        pathname\n      } = url;\n      let criticalCss = void 0;\n      if (_build.unstable_getCriticalCss) {\n        criticalCss = await _build.unstable_getCriticalCss({\n          pathname\n        });\n      } else if (mode === \"development\" /* Development */ && getDevServerHooks()?.getCriticalCss) {\n        criticalCss = await getDevServerHooks()?.getCriticalCss?.(pathname);\n      }\n      response = await handleDocumentRequest(serverMode, _build, staticHandler, request, loadContext, handleError, isSpaMode, criticalCss);\n    }\n    if (request.method === \"HEAD\") {\n      return new Response(null, {\n        headers: response.headers,\n        status: response.status,\n        statusText: response.statusText\n      });\n    }\n    return response;\n  };\n};\nasync function handleManifestRequest(build, routes, url) {\n  if (build.assets.version !== url.searchParams.get(\"version\")) {\n    return new Response(null, {\n      status: 204,\n      headers: {\n        \"X-Remix-Reload-Document\": \"true\"\n      }\n    });\n  }\n  let patches = {};\n  if (url.searchParams.has(\"p\")) {\n    let paths = /* @__PURE__ */new Set();\n    url.searchParams.getAll(\"p\").forEach(path => {\n      if (!path.startsWith(\"/\")) {\n        path = `/${path}`;\n      }\n      let segments = path.split(\"/\").slice(1);\n      segments.forEach((_, i) => {\n        let partialPath = segments.slice(0, i + 1).join(\"/\");\n        paths.add(`/${partialPath}`);\n      });\n    });\n    for (let path of paths) {\n      let matches = matchServerRoutes(routes, path, build.basename);\n      if (matches) {\n        for (let match of matches) {\n          let routeId = match.route.id;\n          let route = build.assets.routes[routeId];\n          if (route) {\n            patches[routeId] = route;\n          }\n        }\n      }\n    }\n    return Response.json(patches, {\n      headers: {\n        \"Cache-Control\": \"public, max-age=31536000, immutable\"\n      }\n    });\n  }\n  return new Response(\"Invalid Request\", {\n    status: 400\n  });\n}\nasync function handleSingleFetchRequest(serverMode, build, staticHandler, request, handlerUrl, loadContext, handleError) {\n  let response = request.method !== \"GET\" ? await singleFetchAction(build, serverMode, staticHandler, request, handlerUrl, loadContext, handleError) : await singleFetchLoaders(build, serverMode, staticHandler, request, handlerUrl, loadContext, handleError);\n  return response;\n}\nasync function handleDocumentRequest(serverMode, build, staticHandler, request, loadContext, handleError, isSpaMode, criticalCss) {\n  try {\n    let response = await staticHandler.query(request, {\n      requestContext: loadContext,\n      unstable_respond: build.future.unstable_middleware ? ctx => renderHtml(ctx, isSpaMode) : void 0\n    });\n    return isResponse(response) ? response : renderHtml(response, isSpaMode);\n  } catch (error) {\n    handleError(error);\n    return new Response(null, {\n      status: 500\n    });\n  }\n  async function renderHtml(context, isSpaMode2) {\n    if (isResponse(context)) {\n      return context;\n    }\n    let headers = getDocumentHeaders(context, build);\n    if (SERVER_NO_BODY_STATUS_CODES.has(context.statusCode)) {\n      return new Response(null, {\n        status: context.statusCode,\n        headers\n      });\n    }\n    if (context.errors) {\n      Object.values(context.errors).forEach(err => {\n        if (!isRouteErrorResponse(err) || err.error) {\n          handleError(err);\n        }\n      });\n      context.errors = sanitizeErrors(context.errors, serverMode);\n    }\n    let state = {\n      loaderData: context.loaderData,\n      actionData: context.actionData,\n      errors: serializeErrors(context.errors, serverMode)\n    };\n    let baseServerHandoff = {\n      basename: build.basename,\n      future: build.future,\n      routeDiscovery: build.routeDiscovery,\n      ssr: build.ssr,\n      isSpaMode: isSpaMode2\n    };\n    let entryContext = {\n      manifest: build.assets,\n      routeModules: createEntryRouteModules(build.routes),\n      staticHandlerContext: context,\n      criticalCss,\n      serverHandoffString: createServerHandoffString({\n        ...baseServerHandoff,\n        criticalCss\n      }),\n      serverHandoffStream: encodeViaTurboStream(state, request.signal, build.entry.module.streamTimeout, serverMode),\n      renderMeta: {},\n      future: build.future,\n      ssr: build.ssr,\n      routeDiscovery: build.routeDiscovery,\n      isSpaMode: isSpaMode2,\n      serializeError: err => serializeError(err, serverMode)\n    };\n    let handleDocumentRequestFunction = build.entry.module.default;\n    try {\n      return await handleDocumentRequestFunction(request, context.statusCode, headers, entryContext, loadContext);\n    } catch (error) {\n      handleError(error);\n      let errorForSecondRender = error;\n      if (isResponse(error)) {\n        try {\n          let data2 = await unwrapResponse(error);\n          errorForSecondRender = new ErrorResponseImpl(error.status, error.statusText, data2);\n        } catch (e) {}\n      }\n      context = getStaticContextFromError(staticHandler.dataRoutes, context, errorForSecondRender);\n      if (context.errors) {\n        context.errors = sanitizeErrors(context.errors, serverMode);\n      }\n      let state2 = {\n        loaderData: context.loaderData,\n        actionData: context.actionData,\n        errors: serializeErrors(context.errors, serverMode)\n      };\n      entryContext = {\n        ...entryContext,\n        staticHandlerContext: context,\n        serverHandoffString: createServerHandoffString(baseServerHandoff),\n        serverHandoffStream: encodeViaTurboStream(state2, request.signal, build.entry.module.streamTimeout, serverMode),\n        renderMeta: {}\n      };\n      try {\n        return await handleDocumentRequestFunction(request, context.statusCode, headers, entryContext, loadContext);\n      } catch (error2) {\n        handleError(error2);\n        return returnLastResortErrorResponse(error2, serverMode);\n      }\n    }\n  }\n}\nasync function handleResourceRequest(serverMode, build, staticHandler, routeId, request, loadContext, handleError) {\n  try {\n    let response = await staticHandler.queryRoute(request, {\n      routeId,\n      requestContext: loadContext,\n      unstable_respond: build.future.unstable_middleware ? ctx => ctx : void 0\n    });\n    if (isResponse(response)) {\n      return response;\n    }\n    if (typeof response === \"string\") {\n      return new Response(response);\n    }\n    return Response.json(response);\n  } catch (error) {\n    if (isResponse(error)) {\n      error.headers.set(\"X-Remix-Catch\", \"yes\");\n      return error;\n    }\n    if (isRouteErrorResponse(error)) {\n      if (error) {\n        handleError(error);\n      }\n      return errorResponseToJson(error, serverMode);\n    }\n    if (error instanceof Error && error.message === \"Expected a response from queryRoute\") {\n      let newError = new Error(\"Expected a Response to be returned from resource route handler\");\n      handleError(newError);\n      return returnLastResortErrorResponse(newError, serverMode);\n    }\n    handleError(error);\n    return returnLastResortErrorResponse(error, serverMode);\n  }\n}\nfunction errorResponseToJson(errorResponse, serverMode) {\n  return Response.json(serializeError(\n  // @ts-expect-error This is \"private\" from users but intended for internal use\n  errorResponse.error || new Error(\"Unexpected Server Error\"), serverMode), {\n    status: errorResponse.status,\n    statusText: errorResponse.statusText,\n    headers: {\n      \"X-Remix-Error\": \"yes\"\n    }\n  });\n}\nfunction returnLastResortErrorResponse(error, serverMode) {\n  let message = \"Unexpected Server Error\";\n  if (serverMode !== \"production\" /* Production */) {\n    message += `\n\n${String(error)}`;\n  }\n  return new Response(message, {\n    status: 500,\n    headers: {\n      \"Content-Type\": \"text/plain\"\n    }\n  });\n}\nfunction unwrapResponse(response) {\n  let contentType = response.headers.get(\"Content-Type\");\n  return contentType && /\\bapplication\\/json\\b/.test(contentType) ? response.body == null ? null : response.json() : response.text();\n}\n\n// lib/server-runtime/sessions.ts\nfunction flash(name) {\n  return `__flash_${name}__`;\n}\nvar createSession = (initialData = {}, id = \"\") => {\n  let map = new Map(Object.entries(initialData));\n  return {\n    get id() {\n      return id;\n    },\n    get data() {\n      return Object.fromEntries(map);\n    },\n    has(name) {\n      return map.has(name) || map.has(flash(name));\n    },\n    get(name) {\n      if (map.has(name)) return map.get(name);\n      let flashName = flash(name);\n      if (map.has(flashName)) {\n        let value = map.get(flashName);\n        map.delete(flashName);\n        return value;\n      }\n      return void 0;\n    },\n    set(name, value) {\n      map.set(name, value);\n    },\n    flash(name, value) {\n      map.set(flash(name), value);\n    },\n    unset(name) {\n      map.delete(name);\n    }\n  };\n};\nvar isSession = object => {\n  return object != null && typeof object.id === \"string\" && typeof object.data !== \"undefined\" && typeof object.has === \"function\" && typeof object.get === \"function\" && typeof object.set === \"function\" && typeof object.flash === \"function\" && typeof object.unset === \"function\";\n};\nfunction createSessionStorage({\n  cookie: cookieArg,\n  createData,\n  readData,\n  updateData,\n  deleteData\n}) {\n  let cookie = isCookie(cookieArg) ? cookieArg : createCookie(cookieArg?.name || \"__session\", cookieArg);\n  warnOnceAboutSigningSessionCookie(cookie);\n  return {\n    async getSession(cookieHeader, options) {\n      let id = cookieHeader && (await cookie.parse(cookieHeader, options));\n      let data2 = id && (await readData(id));\n      return createSession(data2 || {}, id || \"\");\n    },\n    async commitSession(session, options) {\n      let {\n        id,\n        data: data2\n      } = session;\n      let expires = options?.maxAge != null ? new Date(Date.now() + options.maxAge * 1e3) : options?.expires != null ? options.expires : cookie.expires;\n      if (id) {\n        await updateData(id, data2, expires);\n      } else {\n        id = await createData(data2, expires);\n      }\n      return cookie.serialize(id, options);\n    },\n    async destroySession(session, options) {\n      await deleteData(session.id);\n      return cookie.serialize(\"\", {\n        ...options,\n        maxAge: void 0,\n        expires: /* @__PURE__ */new Date(0)\n      });\n    }\n  };\n}\nfunction warnOnceAboutSigningSessionCookie(cookie) {\n  warnOnce(cookie.isSigned, `The \"${cookie.name}\" cookie is not signed, but session cookies should be signed to prevent tampering on the client before they are sent back to the server. See https://reactrouter.com/explanation/sessions-and-cookies#signing-cookies for more information.`);\n}\n\n// lib/server-runtime/sessions/cookieStorage.ts\nfunction createCookieSessionStorage({\n  cookie: cookieArg\n} = {}) {\n  let cookie = isCookie(cookieArg) ? cookieArg : createCookie(cookieArg?.name || \"__session\", cookieArg);\n  warnOnceAboutSigningSessionCookie(cookie);\n  return {\n    async getSession(cookieHeader, options) {\n      return createSession(cookieHeader && (await cookie.parse(cookieHeader, options)) || {});\n    },\n    async commitSession(session, options) {\n      let serializedCookie = await cookie.serialize(session.data, options);\n      if (serializedCookie.length > 4096) {\n        throw new Error(\"Cookie length will exceed browser maximum. Length: \" + serializedCookie.length);\n      }\n      return serializedCookie;\n    },\n    async destroySession(_session, options) {\n      return cookie.serialize(\"\", {\n        ...options,\n        maxAge: void 0,\n        expires: /* @__PURE__ */new Date(0)\n      });\n    }\n  };\n}\n\n// lib/server-runtime/sessions/memoryStorage.ts\nfunction createMemorySessionStorage({\n  cookie\n} = {}) {\n  let map = /* @__PURE__ */new Map();\n  return createSessionStorage({\n    cookie,\n    async createData(data2, expires) {\n      let id = Math.random().toString(36).substring(2, 10);\n      map.set(id, {\n        data: data2,\n        expires\n      });\n      return id;\n    },\n    async readData(id) {\n      if (map.has(id)) {\n        let {\n          data: data2,\n          expires\n        } = map.get(id);\n        if (!expires || expires > /* @__PURE__ */new Date()) {\n          return data2;\n        }\n        if (expires) map.delete(id);\n      }\n      return null;\n    },\n    async updateData(id, data2, expires) {\n      map.set(id, {\n        data: data2,\n        expires\n      });\n    },\n    async deleteData(id) {\n      map.delete(id);\n    }\n  });\n}\n\n// lib/href.ts\nfunction href(path, ...args) {\n  let params = args[0];\n  return path.split(\"/\").map(segment => {\n    if (segment === \"*\") {\n      return params ? params[\"*\"] : void 0;\n    }\n    const match = segment.match(/^:([\\w-]+)(\\?)?/);\n    if (!match) return segment;\n    const param = match[1];\n    const value = params ? params[param] : void 0;\n    const isRequired = match[2] === void 0;\n    if (isRequired && value === void 0) {\n      throw Error(`Path '${path}' requires param '${param}' but it was not provided`);\n    }\n    return value;\n  }).filter(segment => segment !== void 0).join(\"/\");\n}\n\n// lib/rsc/browser.tsx\nimport * as React4 from \"react\";\nimport * as ReactDOM from \"react-dom\";\n\n// lib/dom/ssr/hydration.tsx\nfunction getHydrationData(state, routes, getRouteInfo, location2, basename, isSpaMode) {\n  let hydrationData = {\n    ...state,\n    loaderData: {\n      ...state.loaderData\n    }\n  };\n  let initialMatches = matchRoutes(routes, location2, basename);\n  if (initialMatches) {\n    for (let match of initialMatches) {\n      let routeId = match.route.id;\n      let routeInfo = getRouteInfo(routeId);\n      if (shouldHydrateRouteLoader(routeId, routeInfo.clientLoader, routeInfo.hasLoader, isSpaMode) && (routeInfo.hasHydrateFallback || !routeInfo.hasLoader)) {\n        delete hydrationData.loaderData[routeId];\n      } else if (!routeInfo.hasLoader) {\n        hydrationData.loaderData[routeId] = null;\n      }\n    }\n  }\n  return hydrationData;\n}\n\n// lib/rsc/errorBoundaries.tsx\nimport React3 from \"react\";\nvar RSCRouterGlobalErrorBoundary = class extends React3.Component {\n  constructor(props) {\n    super(props);\n    this.state = {\n      error: null,\n      location: props.location\n    };\n  }\n  static getDerivedStateFromError(error) {\n    return {\n      error\n    };\n  }\n  static getDerivedStateFromProps(props, state) {\n    if (state.location !== props.location) {\n      return {\n        error: null,\n        location: props.location\n      };\n    }\n    return {\n      error: state.error,\n      location: state.location\n    };\n  }\n  render() {\n    if (this.state.error) {\n      return /* @__PURE__ */React3.createElement(RSCDefaultRootErrorBoundaryImpl, {\n        error: this.state.error,\n        renderAppShell: true\n      });\n    } else {\n      return this.props.children;\n    }\n  }\n};\nfunction ErrorWrapper({\n  renderAppShell,\n  title,\n  children\n}) {\n  if (!renderAppShell) {\n    return children;\n  }\n  return /* @__PURE__ */React3.createElement(\"html\", {\n    lang: \"en\"\n  }, /* @__PURE__ */React3.createElement(\"head\", null, /* @__PURE__ */React3.createElement(\"meta\", {\n    charSet: \"utf-8\"\n  }), /* @__PURE__ */React3.createElement(\"meta\", {\n    name: \"viewport\",\n    content: \"width=device-width,initial-scale=1,viewport-fit=cover\"\n  }), /* @__PURE__ */React3.createElement(\"title\", null, title)), /* @__PURE__ */React3.createElement(\"body\", null, /* @__PURE__ */React3.createElement(\"main\", {\n    style: {\n      fontFamily: \"system-ui, sans-serif\",\n      padding: \"2rem\"\n    }\n  }, children)));\n}\nfunction RSCDefaultRootErrorBoundaryImpl({\n  error,\n  renderAppShell\n}) {\n  console.error(error);\n  let heyDeveloper = /* @__PURE__ */React3.createElement(\"script\", {\n    dangerouslySetInnerHTML: {\n      __html: `\n        console.log(\n          \"\\u{1F4BF} Hey developer \\u{1F44B}. You can provide a way better UX than this when your app throws errors. Check out https://reactrouter.com/how-to/error-boundary for more information.\"\n        );\n      `\n    }\n  });\n  if (isRouteErrorResponse(error)) {\n    return /* @__PURE__ */React3.createElement(ErrorWrapper, {\n      renderAppShell,\n      title: \"Unhandled Thrown Response!\"\n    }, /* @__PURE__ */React3.createElement(\"h1\", {\n      style: {\n        fontSize: \"24px\"\n      }\n    }, error.status, \" \", error.statusText), ENABLE_DEV_WARNINGS ? heyDeveloper : null);\n  }\n  let errorInstance;\n  if (error instanceof Error) {\n    errorInstance = error;\n  } else {\n    let errorString = error == null ? \"Unknown Error\" : typeof error === \"object\" && \"toString\" in error ? error.toString() : JSON.stringify(error);\n    errorInstance = new Error(errorString);\n  }\n  return /* @__PURE__ */React3.createElement(ErrorWrapper, {\n    renderAppShell,\n    title: \"Application Error!\"\n  }, /* @__PURE__ */React3.createElement(\"h1\", {\n    style: {\n      fontSize: \"24px\"\n    }\n  }, \"Application Error\"), /* @__PURE__ */React3.createElement(\"pre\", {\n    style: {\n      padding: \"2rem\",\n      background: \"hsla(10, 50%, 50%, 0.1)\",\n      color: \"red\",\n      overflow: \"auto\"\n    }\n  }, errorInstance.stack), heyDeveloper);\n}\nfunction RSCDefaultRootErrorBoundary({\n  hasRootLayout\n}) {\n  let error = useRouteError();\n  if (hasRootLayout === void 0) {\n    throw new Error(\"Missing 'hasRootLayout' prop\");\n  }\n  return /* @__PURE__ */React3.createElement(RSCDefaultRootErrorBoundaryImpl, {\n    renderAppShell: !hasRootLayout,\n    error\n  });\n}\n\n// lib/rsc/browser.tsx\nfunction createCallServer({\n  createFromReadableStream,\n  createTemporaryReferenceSet,\n  encodeReply,\n  fetch: fetchImplementation = fetch\n}) {\n  const globalVar = window;\n  let landedActionId = 0;\n  return async (id, args) => {\n    let actionId = globalVar.__routerActionID = (globalVar.__routerActionID ?? (globalVar.__routerActionID = 0)) + 1;\n    const temporaryReferences = createTemporaryReferenceSet();\n    const response = await fetchImplementation(new Request(location.href, {\n      body: await encodeReply(args, {\n        temporaryReferences\n      }),\n      method: \"POST\",\n      headers: {\n        Accept: \"text/x-component\",\n        \"rsc-action-id\": id\n      }\n    }));\n    if (!response.body) {\n      throw new Error(\"No response body\");\n    }\n    const payload = await createFromReadableStream(response.body, {\n      temporaryReferences\n    });\n    if (payload.type === \"redirect\") {\n      if (payload.reload) {\n        window.location.href = payload.location;\n        return;\n      }\n      globalVar.__router.navigate(payload.location, {\n        replace: payload.replace\n      });\n      return payload.actionResult;\n    }\n    if (payload.type !== \"action\") {\n      throw new Error(\"Unexpected payload type\");\n    }\n    if (payload.rerender) {\n      React4.startTransition(\n      // @ts-expect-error - We have old react types that don't know this can be async\n      async () => {\n        const rerender = await payload.rerender;\n        if (!rerender) return;\n        if (landedActionId < actionId && globalVar.__routerActionID <= actionId) {\n          landedActionId = actionId;\n          if (rerender.type === \"redirect\") {\n            if (rerender.reload) {\n              window.location.href = rerender.location;\n              return;\n            }\n            globalVar.__router.navigate(rerender.location, {\n              replace: rerender.replace\n            });\n            return;\n          }\n          let lastMatch;\n          for (const match of rerender.matches) {\n            globalVar.__router.patchRoutes(lastMatch?.id ?? null, [createRouteFromServerManifest(match)], true);\n            lastMatch = match;\n          }\n          window.__router._internalSetStateDoNotUseOrYouWillBreakYourApp({});\n          React4.startTransition(() => {\n            window.__router._internalSetStateDoNotUseOrYouWillBreakYourApp({\n              loaderData: Object.assign({}, globalVar.__router.state.loaderData, rerender.loaderData),\n              errors: rerender.errors ? Object.assign({}, globalVar.__router.state.errors, rerender.errors) : null\n            });\n          });\n        }\n      });\n    }\n    return payload.actionResult;\n  };\n}\nfunction createRouterFromPayload({\n  fetchImplementation,\n  createFromReadableStream,\n  unstable_getContext,\n  payload\n}) {\n  const globalVar = window;\n  if (globalVar.__router) return globalVar.__router;\n  if (payload.type !== \"render\") throw new Error(\"Invalid payload type\");\n  let patches = /* @__PURE__ */new Map();\n  payload.patches?.forEach(patch => {\n    invariant(patch.parentId, \"Invalid patch parentId\");\n    if (!patches.has(patch.parentId)) {\n      patches.set(patch.parentId, []);\n    }\n    patches.get(patch.parentId)?.push(patch);\n  });\n  let routes = payload.matches.reduceRight((previous, match) => {\n    const route = createRouteFromServerManifest(match, payload);\n    if (previous.length > 0) {\n      route.children = previous;\n      let childrenToPatch = patches.get(match.id);\n      if (childrenToPatch) {\n        route.children.push(...childrenToPatch.map(r => createRouteFromServerManifest(r)));\n      }\n    }\n    return [route];\n  }, []);\n  globalVar.__router = createRouter({\n    routes,\n    unstable_getContext,\n    basename: payload.basename,\n    history: createBrowserHistory(),\n    hydrationData: getHydrationData({\n      loaderData: payload.loaderData,\n      actionData: payload.actionData,\n      errors: payload.errors\n    }, routes, routeId => {\n      let match = payload.matches.find(m => m.id === routeId);\n      invariant(match, \"Route not found in payload\");\n      return {\n        clientLoader: match.clientLoader,\n        hasLoader: match.hasLoader,\n        hasHydrateFallback: match.hydrateFallbackElement != null\n      };\n    }, payload.location, void 0, false),\n    async patchRoutesOnNavigation({\n      path,\n      signal\n    }) {\n      if (discoveredPaths.has(path)) {\n        return;\n      }\n      await fetchAndApplyManifestPatches([path], createFromReadableStream, fetchImplementation, signal);\n    },\n    // FIXME: Pass `build.ssr` into this function\n    dataStrategy: getRSCSingleFetchDataStrategy(() => globalVar.__router, true, payload.basename, createFromReadableStream, fetchImplementation)\n  });\n  if (globalVar.__router.state.initialized) {\n    globalVar.__routerInitialized = true;\n    globalVar.__router.initialize();\n  } else {\n    globalVar.__routerInitialized = false;\n  }\n  let lastLoaderData = void 0;\n  globalVar.__router.subscribe(({\n    loaderData,\n    actionData\n  }) => {\n    if (lastLoaderData !== loaderData) {\n      globalVar.__routerActionID = (globalVar.__routerActionID ?? (globalVar.__routerActionID = 0)) + 1;\n    }\n  });\n  return globalVar.__router;\n}\nvar renderedRoutesContext = unstable_createContext();\nfunction getRSCSingleFetchDataStrategy(getRouter, ssr, basename, createFromReadableStream, fetchImplementation) {\n  let dataStrategy = getSingleFetchDataStrategyImpl(getRouter, match => {\n    let M = match;\n    return {\n      hasLoader: M.route.hasLoader,\n      hasClientLoader: M.route.hasClientLoader,\n      hasComponent: M.route.hasComponent,\n      hasAction: M.route.hasAction,\n      hasClientAction: M.route.hasClientAction,\n      hasShouldRevalidate: M.route.hasShouldRevalidate\n    };\n  },\n  // pass map into fetchAndDecode so it can add payloads\n  getFetchAndDecodeViaRSC(createFromReadableStream, fetchImplementation), ssr, basename,\n  // If the route has a component but we don't have an element, we need to hit\n  // the server loader flow regardless of whether the client loader calls\n  // `serverLoader` or not, otherwise we'll have nothing to render.\n  match => {\n    let M = match;\n    return M.route.hasComponent && !M.route.element;\n  });\n  return async args => args.unstable_runClientMiddleware(async () => {\n    let context = args.context;\n    context.set(renderedRoutesContext, []);\n    let results = await dataStrategy(args);\n    const renderedRoutesById = /* @__PURE__ */new Map();\n    for (const route of context.get(renderedRoutesContext)) {\n      if (!renderedRoutesById.has(route.id)) {\n        renderedRoutesById.set(route.id, []);\n      }\n      renderedRoutesById.get(route.id).push(route);\n    }\n    for (const match of args.matches) {\n      const renderedRoutes = renderedRoutesById.get(match.route.id);\n      if (renderedRoutes) {\n        for (const rendered of renderedRoutes) {\n          window.__router.patchRoutes(rendered.parentId ?? null, [createRouteFromServerManifest(rendered)], true);\n        }\n      }\n    }\n    return results;\n  });\n}\nfunction getFetchAndDecodeViaRSC(createFromReadableStream, fetchImplementation) {\n  return async (args, basename, targetRoutes) => {\n    let {\n      request,\n      context\n    } = args;\n    let url = singleFetchUrl(request.url, basename, \"rsc\");\n    if (request.method === \"GET\") {\n      url = stripIndexParam(url);\n      if (targetRoutes) {\n        url.searchParams.set(\"_routes\", targetRoutes.join(\",\"));\n      }\n    }\n    let res = await fetchImplementation(new Request(url, await createRequestInit(request)));\n    if (res.status === 404 && !res.headers.has(\"X-Remix-Response\")) {\n      throw new ErrorResponseImpl(404, \"Not Found\", true);\n    }\n    invariant(res.body, \"No response body to decode\");\n    try {\n      const payload = await createFromReadableStream(res.body, {\n        temporaryReferences: void 0\n      });\n      if (payload.type === \"redirect\") {\n        return {\n          status: res.status,\n          data: {\n            redirect: {\n              redirect: payload.location,\n              reload: payload.reload,\n              replace: payload.replace,\n              revalidate: false,\n              status: payload.status\n            }\n          }\n        };\n      }\n      if (payload.type !== \"render\") {\n        throw new Error(\"Unexpected payload type\");\n      }\n      context.get(renderedRoutesContext).push(...payload.matches);\n      let results = {\n        routes: {}\n      };\n      const dataKey = isMutationMethod(request.method) ? \"actionData\" : \"loaderData\";\n      for (let [routeId, data2] of Object.entries(payload[dataKey] || {})) {\n        results.routes[routeId] = {\n          data: data2\n        };\n      }\n      if (payload.errors) {\n        for (let [routeId, error] of Object.entries(payload.errors)) {\n          results.routes[routeId] = {\n            error\n          };\n        }\n      }\n      return {\n        status: res.status,\n        data: results\n      };\n    } catch (e) {\n      throw new Error(\"Unable to decode RSC response\");\n    }\n  };\n}\nfunction RSCHydratedRouter({\n  createFromReadableStream,\n  fetch: fetchImplementation = fetch,\n  payload,\n  routeDiscovery = \"eager\",\n  unstable_getContext\n}) {\n  if (payload.type !== \"render\") throw new Error(\"Invalid payload type\");\n  let router = React4.useMemo(() => createRouterFromPayload({\n    payload,\n    fetchImplementation,\n    unstable_getContext,\n    createFromReadableStream\n  }), [createFromReadableStream, payload, fetchImplementation, unstable_getContext]);\n  React4.useLayoutEffect(() => {\n    const globalVar = window;\n    if (!globalVar.__routerInitialized) {\n      globalVar.__routerInitialized = true;\n      globalVar.__router.initialize();\n    }\n  }, []);\n  let [location2, setLocation] = React4.useState(router.state.location);\n  React4.useLayoutEffect(() => router.subscribe(newState => {\n    if (newState.location !== location2) {\n      setLocation(newState.location);\n    }\n  }), [router, location2]);\n  React4.useEffect(() => {\n    if (routeDiscovery === \"lazy\" ||\n    // @ts-expect-error - TS doesn't know about this yet\n    window.navigator?.connection?.saveData === true) {\n      return;\n    }\n    function registerElement(el) {\n      let path = el.tagName === \"FORM\" ? el.getAttribute(\"action\") : el.getAttribute(\"href\");\n      if (!path) {\n        return;\n      }\n      let pathname = el.tagName === \"A\" ? el.pathname : new URL(path, window.location.origin).pathname;\n      if (!discoveredPaths.has(pathname)) {\n        nextPaths.add(pathname);\n      }\n    }\n    async function fetchPatches() {\n      document.querySelectorAll(\"a[data-discover], form[data-discover]\").forEach(registerElement);\n      let paths = Array.from(nextPaths.keys()).filter(path => {\n        if (discoveredPaths.has(path)) {\n          nextPaths.delete(path);\n          return false;\n        }\n        return true;\n      });\n      if (paths.length === 0) {\n        return;\n      }\n      try {\n        await fetchAndApplyManifestPatches(paths, createFromReadableStream, fetchImplementation);\n      } catch (e) {\n        console.error(\"Failed to fetch manifest patches\", e);\n      }\n    }\n    let debouncedFetchPatches = debounce(fetchPatches, 100);\n    fetchPatches();\n    let observer = new MutationObserver(() => debouncedFetchPatches());\n    observer.observe(document.documentElement, {\n      subtree: true,\n      childList: true,\n      attributes: true,\n      attributeFilter: [\"data-discover\", \"href\", \"action\"]\n    });\n  }, [routeDiscovery, createFromReadableStream, fetchImplementation]);\n  const frameworkContext = {\n    future: {\n      // These flags have no runtime impact so can always be false.  If we add\n      // flags that drive runtime behavior they'll need to be proxied through.\n      unstable_middleware: false,\n      unstable_subResourceIntegrity: false\n    },\n    isSpaMode: true,\n    ssr: true,\n    criticalCss: \"\",\n    manifest: {\n      routes: {},\n      version: \"1\",\n      url: \"\",\n      entry: {\n        module: \"\",\n        imports: []\n      }\n    },\n    routeDiscovery: {\n      mode: \"lazy\",\n      manifestPath: \"/__manifest\"\n    },\n    routeModules: {}\n  };\n  return /* @__PURE__ */React4.createElement(RSCRouterContext.Provider, {\n    value: true\n  }, /* @__PURE__ */React4.createElement(RSCRouterGlobalErrorBoundary, {\n    location: location2\n  }, /* @__PURE__ */React4.createElement(FrameworkContext.Provider, {\n    value: frameworkContext\n  }, /* @__PURE__ */React4.createElement(RouterProvider, {\n    router,\n    flushSync: ReactDOM.flushSync\n  }))));\n}\nfunction createRouteFromServerManifest(match, payload) {\n  let hasInitialData = payload && match.id in payload.loaderData;\n  let initialData = payload?.loaderData[match.id];\n  let hasInitialError = payload?.errors && match.id in payload.errors;\n  let initialError = payload?.errors?.[match.id];\n  let isHydrationRequest = match.clientLoader?.hydrate === true || !match.hasLoader ||\n  // If the route has a component but we don't have an element, we need to hit\n  // the server loader flow regardless of whether the client loader calls\n  // `serverLoader` or not, otherwise we'll have nothing to render.\n  match.hasComponent && !match.element;\n  let dataRoute = {\n    id: match.id,\n    element: match.element,\n    errorElement: match.errorElement,\n    handle: match.handle,\n    hasErrorBoundary: match.hasErrorBoundary,\n    hydrateFallbackElement: match.hydrateFallbackElement,\n    index: match.index,\n    loader: match.clientLoader ? async (args, singleFetch) => {\n      try {\n        let result = await match.clientLoader({\n          ...args,\n          serverLoader: () => {\n            preventInvalidServerHandlerCall(\"loader\", match.id, match.hasLoader);\n            if (isHydrationRequest) {\n              if (hasInitialData) {\n                return initialData;\n              }\n              if (hasInitialError) {\n                throw initialError;\n              }\n            }\n            return callSingleFetch(singleFetch);\n          }\n        });\n        return result;\n      } finally {\n        isHydrationRequest = false;\n      }\n    } :\n    // We always make the call in this RSC world since even if we don't\n    // have a `loader` we may need to get the `element` implementation\n    (_, singleFetch) => callSingleFetch(singleFetch),\n    action: match.clientAction ? (args, singleFetch) => match.clientAction({\n      ...args,\n      serverAction: async () => {\n        preventInvalidServerHandlerCall(\"action\", match.id, match.hasLoader);\n        return await callSingleFetch(singleFetch);\n      }\n    }) : match.hasAction ? (_, singleFetch) => callSingleFetch(singleFetch) : () => {\n      throw noActionDefinedError(\"action\", match.id);\n    },\n    path: match.path,\n    shouldRevalidate: match.shouldRevalidate,\n    // We always have a \"loader\" in this RSC world since even if we don't\n    // have a `loader` we may need to get the `element` implementation\n    hasLoader: true,\n    hasClientLoader: match.clientLoader != null,\n    hasAction: match.hasAction,\n    hasClientAction: match.clientAction != null,\n    hasShouldRevalidate: match.shouldRevalidate != null\n  };\n  if (typeof dataRoute.loader === \"function\") {\n    dataRoute.loader.hydrate = shouldHydrateRouteLoader(match.id, match.clientLoader, match.hasLoader, false);\n  }\n  return dataRoute;\n}\nfunction callSingleFetch(singleFetch) {\n  invariant(typeof singleFetch === \"function\", \"Invalid singleFetch parameter\");\n  return singleFetch();\n}\nfunction preventInvalidServerHandlerCall(type, routeId, hasHandler) {\n  if (!hasHandler) {\n    let fn = type === \"action\" ? \"serverAction()\" : \"serverLoader()\";\n    let msg = `You are trying to call ${fn} on a route that does not have a server ${type} (routeId: \"${routeId}\")`;\n    console.error(msg);\n    throw new ErrorResponseImpl(400, \"Bad Request\", new Error(msg), true);\n  }\n}\nvar nextPaths = /* @__PURE__ */new Set();\nvar discoveredPathsMaxSize = 1e3;\nvar discoveredPaths = /* @__PURE__ */new Set();\nvar URL_LIMIT = 7680;\nfunction getManifestUrl(paths) {\n  if (paths.length === 0) {\n    return null;\n  }\n  if (paths.length === 1) {\n    return new URL(`${paths[0]}.manifest`, window.location.origin);\n  }\n  const globalVar = window;\n  let basename = (globalVar.__router.basename ?? \"\").replace(/^\\/|\\/$/g, \"\");\n  let url = new URL(`${basename}/.manifest`, window.location.origin);\n  paths.sort().forEach(path => url.searchParams.append(\"p\", path));\n  return url;\n}\nasync function fetchAndApplyManifestPatches(paths, createFromReadableStream, fetchImplementation, signal) {\n  let url = getManifestUrl(paths);\n  if (url == null) {\n    return;\n  }\n  if (url.toString().length > URL_LIMIT) {\n    nextPaths.clear();\n    return;\n  }\n  let response = await fetchImplementation(new Request(url, {\n    signal\n  }));\n  if (!response.body || response.status < 200 || response.status >= 300) {\n    throw new Error(\"Unable to fetch new route matches from the server\");\n  }\n  let payload = await createFromReadableStream(response.body, {\n    temporaryReferences: void 0\n  });\n  if (payload.type !== \"manifest\") {\n    throw new Error(\"Failed to patch routes\");\n  }\n  paths.forEach(p => addToFifoQueue(p, discoveredPaths));\n  payload.patches.forEach(p => {\n    window.__router.patchRoutes(p.parentId ?? null, [createRouteFromServerManifest(p)]);\n  });\n}\nfunction addToFifoQueue(path, queue) {\n  if (queue.size >= discoveredPathsMaxSize) {\n    let first = queue.values().next().value;\n    queue.delete(first);\n  }\n  queue.add(path);\n}\nfunction debounce(callback, wait) {\n  let timeoutId;\n  return (...args) => {\n    window.clearTimeout(timeoutId);\n    timeoutId = window.setTimeout(() => callback(...args), wait);\n  };\n}\n\n// lib/rsc/server.ssr.tsx\nimport * as React5 from \"react\";\n\n// lib/rsc/html-stream/server.ts\nvar encoder2 = new TextEncoder();\nvar trailer = \"</body></html>\";\nfunction injectRSCPayload(rscStream) {\n  let decoder = new TextDecoder();\n  let resolveFlightDataPromise;\n  let flightDataPromise = new Promise(resolve => resolveFlightDataPromise = resolve);\n  let startedRSC = false;\n  let buffered = [];\n  let timeout = null;\n  function flushBufferedChunks(controller) {\n    for (let chunk of buffered) {\n      let buf = decoder.decode(chunk, {\n        stream: true\n      });\n      if (buf.endsWith(trailer)) {\n        buf = buf.slice(0, -trailer.length);\n      }\n      controller.enqueue(encoder2.encode(buf));\n    }\n    buffered.length = 0;\n    timeout = null;\n  }\n  return new TransformStream({\n    transform(chunk, controller) {\n      buffered.push(chunk);\n      if (timeout) {\n        return;\n      }\n      timeout = setTimeout(async () => {\n        flushBufferedChunks(controller);\n        if (!startedRSC) {\n          startedRSC = true;\n          writeRSCStream(rscStream, controller).catch(err => controller.error(err)).then(resolveFlightDataPromise);\n        }\n      }, 0);\n    },\n    async flush(controller) {\n      await flightDataPromise;\n      if (timeout) {\n        clearTimeout(timeout);\n        flushBufferedChunks(controller);\n      }\n      controller.enqueue(encoder2.encode(\"</body></html>\"));\n    }\n  });\n}\nasync function writeRSCStream(rscStream, controller) {\n  let decoder = new TextDecoder(\"utf-8\", {\n    fatal: true\n  });\n  const reader = rscStream.getReader();\n  try {\n    let read;\n    while ((read = await reader.read()) && !read.done) {\n      const chunk = read.value;\n      try {\n        writeChunk(JSON.stringify(decoder.decode(chunk, {\n          stream: true\n        })), controller);\n      } catch (err) {\n        let base64 = JSON.stringify(btoa(String.fromCodePoint(...chunk)));\n        writeChunk(`Uint8Array.from(atob(${base64}), m => m.codePointAt(0))`, controller);\n      }\n    }\n  } finally {\n    reader.releaseLock();\n  }\n  let remaining = decoder.decode();\n  if (remaining.length) {\n    writeChunk(JSON.stringify(remaining), controller);\n  }\n}\nfunction writeChunk(chunk, controller) {\n  controller.enqueue(encoder2.encode(`<script>${escapeScript(`(self.__FLIGHT_DATA||=[]).push(${chunk})`)}</script>`));\n}\nfunction escapeScript(script) {\n  return script.replace(/<!--/g, \"<\\\\!--\").replace(/<\\/(script)/gi, \"</\\\\$1\");\n}\n\n// lib/rsc/server.ssr.tsx\nasync function routeRSCServerRequest({\n  request,\n  fetchServer,\n  createFromReadableStream,\n  renderHTML,\n  hydrate = true\n}) {\n  const url = new URL(request.url);\n  const isDataRequest = isReactServerRequest(url);\n  const respondWithRSCPayload = isDataRequest || isManifestRequest(url) || request.headers.has(\"rsc-action-id\");\n  const serverResponse = await fetchServer(request);\n  if (respondWithRSCPayload || serverResponse.headers.get(\"React-Router-Resource\") === \"true\") {\n    return serverResponse;\n  }\n  if (!serverResponse.body) {\n    throw new Error(\"Missing body in server response\");\n  }\n  let serverResponseB = null;\n  if (hydrate) {\n    serverResponseB = serverResponse.clone();\n  }\n  const body = serverResponse.body;\n  let payloadPromise;\n  const getPayload = async () => {\n    if (payloadPromise) return payloadPromise;\n    payloadPromise = createFromReadableStream(body);\n    return payloadPromise;\n  };\n  try {\n    const html = await renderHTML(getPayload);\n    const headers = new Headers(serverResponse.headers);\n    headers.set(\"Content-Type\", \"text/html\");\n    if (!hydrate) {\n      return new Response(html, {\n        status: serverResponse.status,\n        headers\n      });\n    }\n    if (!serverResponseB?.body) {\n      throw new Error(\"Failed to clone server response\");\n    }\n    const body2 = html.pipeThrough(injectRSCPayload(serverResponseB.body));\n    return new Response(body2, {\n      status: serverResponse.status,\n      headers\n    });\n  } catch (reason) {\n    if (reason instanceof Response) {\n      return reason;\n    }\n    throw reason;\n  }\n}\nfunction RSCStaticRouter({\n  getPayload\n}) {\n  const payload = React5.use(getPayload());\n  if (payload.type === \"redirect\") {\n    throw new Response(null, {\n      status: payload.status,\n      headers: {\n        Location: payload.location\n      }\n    });\n  }\n  if (payload.type !== \"render\") return null;\n  let patchedLoaderData = {\n    ...payload.loaderData\n  };\n  for (const match of payload.matches) {\n    if (shouldHydrateRouteLoader(match.id, match.clientLoader, match.hasLoader, false) && (match.hydrateFallbackElement || !match.hasLoader)) {\n      delete patchedLoaderData[match.id];\n    }\n  }\n  const context = {\n    actionData: payload.actionData,\n    actionHeaders: {},\n    basename: payload.basename,\n    errors: payload.errors,\n    loaderData: patchedLoaderData,\n    loaderHeaders: {},\n    location: payload.location,\n    statusCode: 200,\n    matches: payload.matches.map(match => ({\n      params: match.params,\n      pathname: match.pathname,\n      pathnameBase: match.pathnameBase,\n      route: {\n        id: match.id,\n        action: match.hasAction || !!match.clientAction,\n        handle: match.handle,\n        hasErrorBoundary: match.hasErrorBoundary,\n        loader: match.hasLoader || !!match.clientLoader,\n        index: match.index,\n        path: match.path,\n        shouldRevalidate: match.shouldRevalidate\n      }\n    }))\n  };\n  const router = createStaticRouter(payload.matches.reduceRight((previous, match) => {\n    const route = {\n      id: match.id,\n      action: match.hasAction || !!match.clientAction,\n      element: match.element,\n      errorElement: match.errorElement,\n      handle: match.handle,\n      hasErrorBoundary: !!match.errorElement,\n      hydrateFallbackElement: match.hydrateFallbackElement,\n      index: match.index,\n      loader: match.hasLoader || !!match.clientLoader,\n      path: match.path,\n      shouldRevalidate: match.shouldRevalidate\n    };\n    if (previous.length > 0) {\n      route.children = previous;\n    }\n    return [route];\n  }, []), context);\n  const frameworkContext = {\n    future: {\n      // These flags have no runtime impact so can always be false.  If we add\n      // flags that drive runtime behavior they'll need to be proxied through.\n      unstable_middleware: false,\n      unstable_subResourceIntegrity: false\n    },\n    isSpaMode: false,\n    ssr: true,\n    criticalCss: \"\",\n    manifest: {\n      routes: {},\n      version: \"1\",\n      url: \"\",\n      entry: {\n        module: \"\",\n        imports: []\n      }\n    },\n    routeDiscovery: {\n      mode: \"lazy\",\n      manifestPath: \"/__manifest\"\n    },\n    routeModules: {}\n  };\n  return /* @__PURE__ */React5.createElement(RSCRouterContext.Provider, {\n    value: true\n  }, /* @__PURE__ */React5.createElement(RSCRouterGlobalErrorBoundary, {\n    location: payload.location\n  }, /* @__PURE__ */React5.createElement(FrameworkContext.Provider, {\n    value: frameworkContext\n  }, /* @__PURE__ */React5.createElement(StaticRouterProvider, {\n    context,\n    router,\n    hydrate: false,\n    nonce: payload.nonce\n  }))));\n}\nfunction isReactServerRequest(url) {\n  return url.pathname.endsWith(\".rsc\");\n}\nfunction isManifestRequest(url) {\n  return url.pathname.endsWith(\".manifest\");\n}\n\n// lib/rsc/html-stream/browser.ts\nfunction getRSCStream() {\n  let encoder3 = new TextEncoder();\n  let streamController = null;\n  let rscStream = new ReadableStream({\n    start(controller) {\n      if (typeof window === \"undefined\") {\n        return;\n      }\n      let handleChunk = chunk => {\n        if (typeof chunk === \"string\") {\n          controller.enqueue(encoder3.encode(chunk));\n        } else {\n          controller.enqueue(chunk);\n        }\n      };\n      window.__FLIGHT_DATA || (window.__FLIGHT_DATA = []);\n      window.__FLIGHT_DATA.forEach(handleChunk);\n      window.__FLIGHT_DATA.push = chunk => {\n        handleChunk(chunk);\n        return 0;\n      };\n      streamController = controller;\n    }\n  });\n  if (typeof document !== \"undefined\" && document.readyState === \"loading\") {\n    document.addEventListener(\"DOMContentLoaded\", () => {\n      streamController?.close();\n    });\n  } else {\n    streamController?.close();\n  }\n  return rscStream;\n}\n\n// lib/dom/ssr/errors.ts\nfunction deserializeErrors(errors) {\n  if (!errors) return null;\n  let entries = Object.entries(errors);\n  let serialized = {};\n  for (let [key, val] of entries) {\n    if (val && val.__type === \"RouteErrorResponse\") {\n      serialized[key] = new ErrorResponseImpl(val.status, val.statusText, val.data, val.internal === true);\n    } else if (val && val.__type === \"Error\") {\n      if (val.__subType) {\n        let ErrorConstructor = window[val.__subType];\n        if (typeof ErrorConstructor === \"function\") {\n          try {\n            let error = new ErrorConstructor(val.message);\n            error.stack = val.stack;\n            serialized[key] = error;\n          } catch (e) {}\n        }\n      }\n      if (serialized[key] == null) {\n        let error = new Error(val.message);\n        error.stack = val.stack;\n        serialized[key] = error;\n      }\n    } else {\n      serialized[key] = val;\n    }\n  }\n  return serialized;\n}\nexport { ServerRouter, createRoutesStub, createCookie, isCookie, ServerMode, setDevServerHooks, createRequestHandler, createSession, isSession, createSessionStorage, createCookieSessionStorage, createMemorySessionStorage, href, getHydrationData, RSCDefaultRootErrorBoundary, createCallServer, RSCHydratedRouter, routeRSCServerRequest, RSCStaticRouter, getRSCStream, deserializeErrors };", "map": {"version": 3, "names": ["ENABLE_DEV_WARNINGS", "ErrorResponseImpl", "FrameworkContext", "NO_BODY_STATUS_CODES", "Outlet", "RSCRouterContext", "RemixErrorBoundary", "RouterProvider", "SINGLE_FETCH_REDIRECT_STATUS", "SingleFetchRedirectSymbol", "StaticRouterProvider", "StreamTransfer", "convertRoutesToDataRoutes", "createBrowserHistory", "createMemoryRouter", "createRequestInit", "createRouter", "createServerRoutes", "createStaticHandler", "createStaticRouter", "decodeViaTurboStream", "encode", "getManifestPath", "getSingleFetchDataStrategyImpl", "getStaticContextFromError", "invariant", "isDataWithResponseInit", "isMutationMethod", "isRedirectResponse", "isRedirectStatusCode", "isResponse", "isRouteErrorResponse", "matchRoutes", "noActionDefinedError", "redirect", "redirectDocument", "replace", "shouldHydrateRouteLoader", "singleFetchUrl", "stripBasename", "stripIndexParam", "unstable_RouterContextProvider", "unstable_createContext", "useRouteError", "warnOnce", "withComponentProps", "withErrorBoundaryProps", "withHydrateFallbackProps", "React", "ServerRouter", "context", "url", "nonce", "URL", "manifest", "routeModules", "criticalCss", "serverHandoffString", "routes", "future", "isSpaMode", "staticHandlerContext", "loaderData", "match", "matches", "routeId", "route", "id", "manifestRoute", "clientLoader", "<PERSON><PERSON><PERSON><PERSON>", "HydrateFallback", "router", "createElement", "Fragment", "Provider", "value", "ssr", "routeDiscovery", "serializeError", "renderMeta", "location", "state", "hydrate", "serverHandoffStream", "Suspense", "identifier", "reader", "<PERSON><PERSON><PERSON><PERSON>", "textDecoder", "TextDecoder", "React2", "createRoutesStub", "_context", "RoutesTestStub", "initialEntries", "initialIndex", "hydrationData", "routerRef", "useRef", "frameworkContextRef", "current", "unstable_subResourceIntegrity", "unstable_middleware", "entry", "imports", "module", "version", "mode", "manifestPath", "patched", "processRoutes", "r", "parentId", "map", "Error", "newRoute", "path", "index", "Component", "Error<PERSON>ou<PERSON><PERSON>", "action", "args", "loader", "handle", "shouldRevalidate", "entryRoute", "hasAction", "hasClientAction", "hasClientLoader", "hasClientMiddleware", "hasErrorBou<PERSON>ry", "clientActionModule", "clientLoaderModule", "clientMiddlewareModule", "hydrateFallbackModule", "default", "links", "meta", "children", "parse", "serialize", "encoder", "TextEncoder", "sign", "secret", "data2", "key", "create<PERSON><PERSON>", "signature", "crypto", "subtle", "hash", "btoa", "String", "fromCharCode", "Uint8Array", "unsign", "cookie", "lastIndexOf", "slice", "byteStringToUint8Array", "atob", "valid", "verify", "error", "usages", "importKey", "name", "byteString", "array", "length", "i", "charCodeAt", "createCookie", "cookieOptions", "secrets", "options", "sameSite", "warnOnceAboutExpiresCookie", "expires", "isSigned", "maxAge", "Date", "now", "cookieHeader", "parseOptions", "cookies", "decoded", "decodeCookieValue", "serializeOptions", "encodeCookieValue", "<PERSON><PERSON><PERSON><PERSON>", "object", "encoded", "encodeData", "unsignedValue", "decodeData", "myUnescape", "encodeURIComponent", "JSON", "stringify", "decodeURIComponent", "myEscape", "str", "toString", "result", "chr", "code", "char<PERSON>t", "exec", "hex", "toUpperCase", "part", "parseInt", "createEntryRouteModules", "Object", "keys", "reduce", "memo", "ServerMode", "ServerMode2", "isServerMode", "sanitizeError", "serverMode", "sanitized", "stack", "sanitizeErrors", "errors", "entries", "acc", "assign", "message", "serializeErrors", "serialized", "val", "__type", "__subType", "matchServerRoutes", "pathname", "basename", "params", "callRouteHandler", "handler", "request", "stripRoutesParam", "stripIndexParam2", "init", "status", "Response", "indexValues", "searchParams", "getAll", "delete", "indexValuesToKeep", "indexValue", "push", "<PERSON><PERSON><PERSON>", "append", "method", "body", "headers", "signal", "duplex", "Request", "href", "invariant2", "console", "globalDevServerHooksKey", "setDevServerHooks", "devServerHooks", "globalThis", "getDevServerHooks", "getBuildTimeHeader", "headerName", "process", "env", "IS_RR_BUILD_REQUEST", "get", "e", "groupRoutesByParentId", "values", "for<PERSON>ach", "createRoutes", "routesByParentId", "createStaticHandlerDataRoutes", "commonRoute", "preRenderedData", "decodeURI", "uint8array", "stream", "ReadableStream", "start", "controller", "enqueue", "close", "global", "reload", "data", "caseSensitive", "ESCAPE_LOOKUP", "ESCAPE_REGEX", "escapeHtml", "html", "createServerHandoffString", "serverHandoff", "splitCookiesString", "getDocumentHeaders", "build", "getDocumentHeadersImpl", "m", "getRouteHeadersFn", "boundaryIdx", "findIndex", "errorHeaders", "actionHeaders", "actionData", "loaderHeaders", "some", "hasOwnProperty", "parentHeaders", "idx", "Headers", "includeErrorHeaders", "includeErrorCookies", "headersFn", "headers2", "prependCookies", "childHeaders", "parentSetCookieString", "childCookies", "Set", "getSetCookie", "has", "SERVER_NO_BODY_STATUS_CODES", "singleFetchAction", "static<PERSON><PERSON><PERSON>", "handlerUrl", "loadContext", "handleError", "respond2", "statusCode", "generateSingleFetchResponse", "getSingleFetchRedirect", "err", "singleFetchResult", "respond", "handlerRequest", "query", "requestContext", "skipL<PERSON>derError<PERSON><PERSON>bling", "skipRevalidation", "unstable_respond", "singleFetchLoaders", "results", "loadedMatches", "filter", "loadRouteIds", "routesParam", "split", "filterMatchesToLoad", "root", "resultHeaders", "set", "encodeViaTurboStream", "streamTimeout", "redirect2", "revalidate", "requestSignal", "AbortController", "timeoutId", "setTimeout", "abort", "addEventListener", "clearTimeout", "plugins", "data3", "statusText", "postPlugins", "fromEntries", "derive", "dataRoutes", "<PERSON><PERSON><PERSON><PERSON>", "aborted", "createRequestHandler", "_build", "requestHandler", "initialContext", "derived", "processRequestError", "returnLastResortErrorResponse", "normalizedBasename", "normalizedPath", "endsWith", "decodedPath", "prerender", "includes", "manifestUrl", "res", "handleManifestRequest", "response", "singleFetchMatches", "handleSingleFetchRequest", "handleDataRequest", "handleResourceRequest", "unstable_getCriticalCss", "getCriticalCss", "handleDocumentRequest", "assets", "patches", "paths", "startsWith", "segments", "_", "partialPath", "join", "add", "json", "ctx", "renderHtml", "isSpaMode2", "baseServerHandoff", "entryContext", "handleDocumentRequestFunction", "errorForSecondRender", "unwrapResponse", "state2", "error2", "queryRoute", "errorResponseToJson", "newError", "errorResponse", "contentType", "test", "text", "flash", "createSession", "initialData", "Map", "flashName", "unset", "isSession", "createSessionStorage", "cookieArg", "createData", "readData", "updateData", "deleteData", "warnOnceAboutSigningSessionCookie", "getSession", "commitSession", "session", "destroySession", "createCookieSessionStorage", "serializedCookie", "_session", "createMemorySessionStorage", "Math", "random", "substring", "segment", "param", "isRequired", "React4", "ReactDOM", "getHydrationData", "getRouteInfo", "location2", "initialMatches", "routeInfo", "hasHydrateFallback", "React3", "RSCRouterGlobalErrorBoundary", "constructor", "props", "getDerivedStateFromError", "getDerivedStateFromProps", "render", "RSCDefaultRootErrorBoundaryImpl", "renderAppShell", "ErrorWrapper", "title", "lang", "charSet", "content", "style", "fontFamily", "padding", "heyDeveloper", "dangerouslySetInnerHTML", "__html", "fontSize", "errorInstance", "errorString", "background", "color", "overflow", "RSCDefaultRootErrorBoundary", "hasRootLayout", "createCallServer", "createFromReadableStream", "createTemporaryReferenceSet", "encodeReply", "fetch", "fetchImplementation", "globalVar", "window", "landedActionId", "actionId", "__routerActionID", "temporaryReferences", "Accept", "payload", "type", "__router", "navigate", "actionResult", "rerender", "startTransition", "lastMatch", "patchRoutes", "createRouteFromServerManifest", "_internalSetStateDoNotUseOrYouWillBreakYourApp", "createRouterFromPayload", "unstable_getContext", "patch", "reduceRight", "previous", "childrenToPatch", "history", "find", "hydrateFallbackElement", "patchRoutesOnNavigation", "discoveredPaths", "fetchAndApplyManifestPatches", "dataStrategy", "getRSCSingleFetchDataStrategy", "initialized", "__routerInitialized", "initialize", "lastLoaderData", "subscribe", "renderedRoutesContext", "getRouter", "M", "hasComponent", "hasShouldRevalidate", "getFetchAndDecodeViaRSC", "element", "unstable_runClientMiddleware", "renderedRoutesById", "renderedRoutes", "rendered", "targetRoutes", "dataKey", "RSCHydratedRouter", "useMemo", "useLayoutEffect", "setLocation", "useState", "newState", "useEffect", "navigator", "connection", "saveData", "registerElement", "el", "tagName", "getAttribute", "origin", "nextPaths", "fetchPatches", "document", "querySelectorAll", "Array", "from", "debouncedFetchPatches", "debounce", "observer", "MutationObserver", "observe", "documentElement", "subtree", "childList", "attributes", "attributeFilter", "frameworkContext", "flushSync", "hasInitialData", "hasInitialError", "initialError", "isHydrationRequest", "dataRoute", "errorElement", "singleFetch", "serverLoader", "preventInvalidServerHandlerCall", "callSingleFetch", "clientAction", "serverAction", "<PERSON><PERSON><PERSON><PERSON>", "fn", "msg", "discoveredPathsMaxSize", "URL_LIMIT", "getManifestUrl", "sort", "clear", "p", "addToFifoQueue", "queue", "size", "first", "next", "callback", "wait", "React5", "encoder2", "trailer", "injectRSCPayload", "rscStream", "decoder", "resolveFlightDataPromise", "flightDataPromise", "Promise", "resolve", "startedRSC", "buffered", "timeout", "flushBufferedChunks", "chunk", "buf", "decode", "TransformStream", "transform", "writeRSCStream", "catch", "then", "flush", "fatal", "read", "done", "writeChunk", "base64", "fromCodePoint", "releaseLock", "remaining", "escapeScript", "script", "routeRSCServerRequest", "fetchServer", "renderHTML", "isDataRequest", "isReactServerRequest", "respondWithRSCPayload", "isManifestRequest", "serverResponse", "serverResponseB", "clone", "payloadPromise", "getPayload", "body2", "pipeThrough", "reason", "RSCStatic<PERSON><PERSON><PERSON>", "use", "Location", "patchedLoaderData", "pathnameBase", "getRSCStream", "encoder3", "streamController", "handleChunk", "__FLIGHT_DATA", "readyState", "deserializeErrors", "internal", "ErrorConstructor"], "sources": ["C:/My Web Sites/esvc/esvc-react/node_modules/react-router/dist/development/chunk-KIUJAIYX.mjs"], "sourcesContent": ["/**\n * react-router v7.7.1\n *\n * Copyright (c) Remix Software Inc.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE.md file in the root directory of this source tree.\n *\n * @license MIT\n */\nimport {\n  ENABLE_DEV_WARNINGS,\n  ErrorResponseImpl,\n  FrameworkContext,\n  NO_BODY_STATUS_CODES,\n  Outlet,\n  RSCRouterContext,\n  RemixErrorBoundary,\n  RouterProvider,\n  SINGLE_FETCH_REDIRECT_STATUS,\n  SingleFetchRedirectSymbol,\n  StaticRouterProvider,\n  StreamTransfer,\n  convertRoutesToDataRoutes,\n  createBrowserHistory,\n  createMemoryRouter,\n  createRequestInit,\n  createRouter,\n  createServerRoutes,\n  createStaticHandler,\n  createStaticRouter,\n  decodeViaTurboStream,\n  encode,\n  getManifestPath,\n  getSingleFetchDataStrategyImpl,\n  getStaticContextFromError,\n  invariant,\n  isDataWithResponseInit,\n  isMutationMethod,\n  isRedirectResponse,\n  isRedirectStatusCode,\n  isResponse,\n  isRouteErrorResponse,\n  matchRoutes,\n  noActionDefinedError,\n  redirect,\n  redirectDocument,\n  replace,\n  shouldHydrateRouteLoader,\n  singleFetchUrl,\n  stripBasename,\n  stripIndexParam,\n  unstable_RouterContextProvider,\n  unstable_createContext,\n  useRouteError,\n  warnOnce,\n  withComponentProps,\n  withErrorBoundaryProps,\n  withHydrateFallbackProps\n} from \"./chunk-C37GKA54.mjs\";\n\n// lib/dom/ssr/server.tsx\nimport * as React from \"react\";\nfunction ServerRouter({\n  context,\n  url,\n  nonce\n}) {\n  if (typeof url === \"string\") {\n    url = new URL(url);\n  }\n  let { manifest, routeModules, criticalCss, serverHandoffString } = context;\n  let routes = createServerRoutes(\n    manifest.routes,\n    routeModules,\n    context.future,\n    context.isSpaMode\n  );\n  context.staticHandlerContext.loaderData = {\n    ...context.staticHandlerContext.loaderData\n  };\n  for (let match of context.staticHandlerContext.matches) {\n    let routeId = match.route.id;\n    let route = routeModules[routeId];\n    let manifestRoute = context.manifest.routes[routeId];\n    if (route && manifestRoute && shouldHydrateRouteLoader(\n      routeId,\n      route.clientLoader,\n      manifestRoute.hasLoader,\n      context.isSpaMode\n    ) && (route.HydrateFallback || !manifestRoute.hasLoader)) {\n      delete context.staticHandlerContext.loaderData[routeId];\n    }\n  }\n  let router = createStaticRouter(routes, context.staticHandlerContext);\n  return /* @__PURE__ */ React.createElement(React.Fragment, null, /* @__PURE__ */ React.createElement(\n    FrameworkContext.Provider,\n    {\n      value: {\n        manifest,\n        routeModules,\n        criticalCss,\n        serverHandoffString,\n        future: context.future,\n        ssr: context.ssr,\n        isSpaMode: context.isSpaMode,\n        routeDiscovery: context.routeDiscovery,\n        serializeError: context.serializeError,\n        renderMeta: context.renderMeta\n      }\n    },\n    /* @__PURE__ */ React.createElement(RemixErrorBoundary, { location: router.state.location }, /* @__PURE__ */ React.createElement(\n      StaticRouterProvider,\n      {\n        router,\n        context: context.staticHandlerContext,\n        hydrate: false\n      }\n    ))\n  ), context.serverHandoffStream ? /* @__PURE__ */ React.createElement(React.Suspense, null, /* @__PURE__ */ React.createElement(\n    StreamTransfer,\n    {\n      context,\n      identifier: 0,\n      reader: context.serverHandoffStream.getReader(),\n      textDecoder: new TextDecoder(),\n      nonce\n    }\n  )) : null);\n}\n\n// lib/dom/ssr/routes-test-stub.tsx\nimport * as React2 from \"react\";\nfunction createRoutesStub(routes, _context) {\n  return function RoutesTestStub({\n    initialEntries,\n    initialIndex,\n    hydrationData,\n    future\n  }) {\n    let routerRef = React2.useRef();\n    let frameworkContextRef = React2.useRef();\n    if (routerRef.current == null) {\n      frameworkContextRef.current = {\n        future: {\n          unstable_subResourceIntegrity: future?.unstable_subResourceIntegrity === true,\n          unstable_middleware: future?.unstable_middleware === true\n        },\n        manifest: {\n          routes: {},\n          entry: { imports: [], module: \"\" },\n          url: \"\",\n          version: \"\"\n        },\n        routeModules: {},\n        ssr: false,\n        isSpaMode: false,\n        routeDiscovery: { mode: \"lazy\", manifestPath: \"/__manifest\" }\n      };\n      let patched = processRoutes(\n        // @ts-expect-error `StubRouteObject` is stricter about `loader`/`action`\n        // types compared to `AgnosticRouteObject`\n        convertRoutesToDataRoutes(routes, (r) => r),\n        _context !== void 0 ? _context : future?.unstable_middleware ? new unstable_RouterContextProvider() : {},\n        frameworkContextRef.current.manifest,\n        frameworkContextRef.current.routeModules\n      );\n      routerRef.current = createMemoryRouter(patched, {\n        initialEntries,\n        initialIndex,\n        hydrationData\n      });\n    }\n    return /* @__PURE__ */ React2.createElement(FrameworkContext.Provider, { value: frameworkContextRef.current }, /* @__PURE__ */ React2.createElement(RouterProvider, { router: routerRef.current }));\n  };\n}\nfunction processRoutes(routes, context, manifest, routeModules, parentId) {\n  return routes.map((route) => {\n    if (!route.id) {\n      throw new Error(\n        \"Expected a route.id in react-router processRoutes() function\"\n      );\n    }\n    let newRoute = {\n      id: route.id,\n      path: route.path,\n      index: route.index,\n      Component: route.Component ? withComponentProps(route.Component) : void 0,\n      HydrateFallback: route.HydrateFallback ? withHydrateFallbackProps(route.HydrateFallback) : void 0,\n      ErrorBoundary: route.ErrorBoundary ? withErrorBoundaryProps(route.ErrorBoundary) : void 0,\n      action: route.action ? (args) => route.action({ ...args, context }) : void 0,\n      loader: route.loader ? (args) => route.loader({ ...args, context }) : void 0,\n      handle: route.handle,\n      shouldRevalidate: route.shouldRevalidate\n    };\n    let entryRoute = {\n      id: route.id,\n      path: route.path,\n      index: route.index,\n      parentId,\n      hasAction: route.action != null,\n      hasLoader: route.loader != null,\n      // When testing routes, you should be stubbing loader/action/middleware,\n      // not trying to re-implement the full loader/clientLoader/SSR/hydration\n      // flow. That is better tested via E2E tests.\n      hasClientAction: false,\n      hasClientLoader: false,\n      hasClientMiddleware: false,\n      hasErrorBoundary: route.ErrorBoundary != null,\n      // any need for these?\n      module: \"build/stub-path-to-module.js\",\n      clientActionModule: void 0,\n      clientLoaderModule: void 0,\n      clientMiddlewareModule: void 0,\n      hydrateFallbackModule: void 0\n    };\n    manifest.routes[newRoute.id] = entryRoute;\n    routeModules[route.id] = {\n      default: newRoute.Component || Outlet,\n      ErrorBoundary: newRoute.ErrorBoundary || void 0,\n      handle: route.handle,\n      links: route.links,\n      meta: route.meta,\n      shouldRevalidate: route.shouldRevalidate\n    };\n    if (route.children) {\n      newRoute.children = processRoutes(\n        route.children,\n        context,\n        manifest,\n        routeModules,\n        newRoute.id\n      );\n    }\n    return newRoute;\n  });\n}\n\n// lib/server-runtime/cookies.ts\nimport { parse, serialize } from \"cookie\";\n\n// lib/server-runtime/crypto.ts\nvar encoder = /* @__PURE__ */ new TextEncoder();\nvar sign = async (value, secret) => {\n  let data2 = encoder.encode(value);\n  let key = await createKey(secret, [\"sign\"]);\n  let signature = await crypto.subtle.sign(\"HMAC\", key, data2);\n  let hash = btoa(String.fromCharCode(...new Uint8Array(signature))).replace(\n    /=+$/,\n    \"\"\n  );\n  return value + \".\" + hash;\n};\nvar unsign = async (cookie, secret) => {\n  let index = cookie.lastIndexOf(\".\");\n  let value = cookie.slice(0, index);\n  let hash = cookie.slice(index + 1);\n  let data2 = encoder.encode(value);\n  let key = await createKey(secret, [\"verify\"]);\n  try {\n    let signature = byteStringToUint8Array(atob(hash));\n    let valid = await crypto.subtle.verify(\"HMAC\", key, signature, data2);\n    return valid ? value : false;\n  } catch (error) {\n    return false;\n  }\n};\nvar createKey = async (secret, usages) => crypto.subtle.importKey(\n  \"raw\",\n  encoder.encode(secret),\n  { name: \"HMAC\", hash: \"SHA-256\" },\n  false,\n  usages\n);\nfunction byteStringToUint8Array(byteString) {\n  let array = new Uint8Array(byteString.length);\n  for (let i = 0; i < byteString.length; i++) {\n    array[i] = byteString.charCodeAt(i);\n  }\n  return array;\n}\n\n// lib/server-runtime/cookies.ts\nvar createCookie = (name, cookieOptions = {}) => {\n  let { secrets = [], ...options } = {\n    path: \"/\",\n    sameSite: \"lax\",\n    ...cookieOptions\n  };\n  warnOnceAboutExpiresCookie(name, options.expires);\n  return {\n    get name() {\n      return name;\n    },\n    get isSigned() {\n      return secrets.length > 0;\n    },\n    get expires() {\n      return typeof options.maxAge !== \"undefined\" ? new Date(Date.now() + options.maxAge * 1e3) : options.expires;\n    },\n    async parse(cookieHeader, parseOptions) {\n      if (!cookieHeader) return null;\n      let cookies = parse(cookieHeader, { ...options, ...parseOptions });\n      if (name in cookies) {\n        let value = cookies[name];\n        if (typeof value === \"string\" && value !== \"\") {\n          let decoded = await decodeCookieValue(value, secrets);\n          return decoded;\n        } else {\n          return \"\";\n        }\n      } else {\n        return null;\n      }\n    },\n    async serialize(value, serializeOptions) {\n      return serialize(\n        name,\n        value === \"\" ? \"\" : await encodeCookieValue(value, secrets),\n        {\n          ...options,\n          ...serializeOptions\n        }\n      );\n    }\n  };\n};\nvar isCookie = (object) => {\n  return object != null && typeof object.name === \"string\" && typeof object.isSigned === \"boolean\" && typeof object.parse === \"function\" && typeof object.serialize === \"function\";\n};\nasync function encodeCookieValue(value, secrets) {\n  let encoded = encodeData(value);\n  if (secrets.length > 0) {\n    encoded = await sign(encoded, secrets[0]);\n  }\n  return encoded;\n}\nasync function decodeCookieValue(value, secrets) {\n  if (secrets.length > 0) {\n    for (let secret of secrets) {\n      let unsignedValue = await unsign(value, secret);\n      if (unsignedValue !== false) {\n        return decodeData(unsignedValue);\n      }\n    }\n    return null;\n  }\n  return decodeData(value);\n}\nfunction encodeData(value) {\n  return btoa(myUnescape(encodeURIComponent(JSON.stringify(value))));\n}\nfunction decodeData(value) {\n  try {\n    return JSON.parse(decodeURIComponent(myEscape(atob(value))));\n  } catch (error) {\n    return {};\n  }\n}\nfunction myEscape(value) {\n  let str = value.toString();\n  let result = \"\";\n  let index = 0;\n  let chr, code;\n  while (index < str.length) {\n    chr = str.charAt(index++);\n    if (/[\\w*+\\-./@]/.exec(chr)) {\n      result += chr;\n    } else {\n      code = chr.charCodeAt(0);\n      if (code < 256) {\n        result += \"%\" + hex(code, 2);\n      } else {\n        result += \"%u\" + hex(code, 4).toUpperCase();\n      }\n    }\n  }\n  return result;\n}\nfunction hex(code, length) {\n  let result = code.toString(16);\n  while (result.length < length) result = \"0\" + result;\n  return result;\n}\nfunction myUnescape(value) {\n  let str = value.toString();\n  let result = \"\";\n  let index = 0;\n  let chr, part;\n  while (index < str.length) {\n    chr = str.charAt(index++);\n    if (chr === \"%\") {\n      if (str.charAt(index) === \"u\") {\n        part = str.slice(index + 1, index + 5);\n        if (/^[\\da-f]{4}$/i.exec(part)) {\n          result += String.fromCharCode(parseInt(part, 16));\n          index += 5;\n          continue;\n        }\n      } else {\n        part = str.slice(index, index + 2);\n        if (/^[\\da-f]{2}$/i.exec(part)) {\n          result += String.fromCharCode(parseInt(part, 16));\n          index += 2;\n          continue;\n        }\n      }\n    }\n    result += chr;\n  }\n  return result;\n}\nfunction warnOnceAboutExpiresCookie(name, expires) {\n  warnOnce(\n    !expires,\n    `The \"${name}\" cookie has an \"expires\" property set. This will cause the expires value to not be updated when the session is committed. Instead, you should set the expires value when serializing the cookie. You can use \\`commitSession(session, { expires })\\` if using a session storage object, or \\`cookie.serialize(\"value\", { expires })\\` if you're using the cookie directly.`\n  );\n}\n\n// lib/server-runtime/entry.ts\nfunction createEntryRouteModules(manifest) {\n  return Object.keys(manifest).reduce((memo, routeId) => {\n    let route = manifest[routeId];\n    if (route) {\n      memo[routeId] = route.module;\n    }\n    return memo;\n  }, {});\n}\n\n// lib/server-runtime/mode.ts\nvar ServerMode = /* @__PURE__ */ ((ServerMode2) => {\n  ServerMode2[\"Development\"] = \"development\";\n  ServerMode2[\"Production\"] = \"production\";\n  ServerMode2[\"Test\"] = \"test\";\n  return ServerMode2;\n})(ServerMode || {});\nfunction isServerMode(value) {\n  return value === \"development\" /* Development */ || value === \"production\" /* Production */ || value === \"test\" /* Test */;\n}\n\n// lib/server-runtime/errors.ts\nfunction sanitizeError(error, serverMode) {\n  if (error instanceof Error && serverMode !== \"development\" /* Development */) {\n    let sanitized = new Error(\"Unexpected Server Error\");\n    sanitized.stack = void 0;\n    return sanitized;\n  }\n  return error;\n}\nfunction sanitizeErrors(errors, serverMode) {\n  return Object.entries(errors).reduce((acc, [routeId, error]) => {\n    return Object.assign(acc, { [routeId]: sanitizeError(error, serverMode) });\n  }, {});\n}\nfunction serializeError(error, serverMode) {\n  let sanitized = sanitizeError(error, serverMode);\n  return {\n    message: sanitized.message,\n    stack: sanitized.stack\n  };\n}\nfunction serializeErrors(errors, serverMode) {\n  if (!errors) return null;\n  let entries = Object.entries(errors);\n  let serialized = {};\n  for (let [key, val] of entries) {\n    if (isRouteErrorResponse(val)) {\n      serialized[key] = { ...val, __type: \"RouteErrorResponse\" };\n    } else if (val instanceof Error) {\n      let sanitized = sanitizeError(val, serverMode);\n      serialized[key] = {\n        message: sanitized.message,\n        stack: sanitized.stack,\n        __type: \"Error\",\n        // If this is a subclass (i.e., ReferenceError), send up the type so we\n        // can re-create the same type during hydration.  This will only apply\n        // in dev mode since all production errors are sanitized to normal\n        // Error instances\n        ...sanitized.name !== \"Error\" ? {\n          __subType: sanitized.name\n        } : {}\n      };\n    } else {\n      serialized[key] = val;\n    }\n  }\n  return serialized;\n}\n\n// lib/server-runtime/routeMatching.ts\nfunction matchServerRoutes(routes, pathname, basename) {\n  let matches = matchRoutes(\n    routes,\n    pathname,\n    basename\n  );\n  if (!matches) return null;\n  return matches.map((match) => ({\n    params: match.params,\n    pathname: match.pathname,\n    route: match.route\n  }));\n}\n\n// lib/server-runtime/data.ts\nasync function callRouteHandler(handler, args) {\n  let result = await handler({\n    request: stripRoutesParam(stripIndexParam2(args.request)),\n    params: args.params,\n    context: args.context\n  });\n  if (isDataWithResponseInit(result) && result.init && result.init.status && isRedirectStatusCode(result.init.status)) {\n    throw new Response(null, result.init);\n  }\n  return result;\n}\nfunction stripIndexParam2(request) {\n  let url = new URL(request.url);\n  let indexValues = url.searchParams.getAll(\"index\");\n  url.searchParams.delete(\"index\");\n  let indexValuesToKeep = [];\n  for (let indexValue of indexValues) {\n    if (indexValue) {\n      indexValuesToKeep.push(indexValue);\n    }\n  }\n  for (let toKeep of indexValuesToKeep) {\n    url.searchParams.append(\"index\", toKeep);\n  }\n  let init = {\n    method: request.method,\n    body: request.body,\n    headers: request.headers,\n    signal: request.signal\n  };\n  if (init.body) {\n    init.duplex = \"half\";\n  }\n  return new Request(url.href, init);\n}\nfunction stripRoutesParam(request) {\n  let url = new URL(request.url);\n  url.searchParams.delete(\"_routes\");\n  let init = {\n    method: request.method,\n    body: request.body,\n    headers: request.headers,\n    signal: request.signal\n  };\n  if (init.body) {\n    init.duplex = \"half\";\n  }\n  return new Request(url.href, init);\n}\n\n// lib/server-runtime/invariant.ts\nfunction invariant2(value, message) {\n  if (value === false || value === null || typeof value === \"undefined\") {\n    console.error(\n      \"The following error is a bug in React Router; please open an issue! https://github.com/remix-run/react-router/issues/new/choose\"\n    );\n    throw new Error(message);\n  }\n}\n\n// lib/server-runtime/dev.ts\nvar globalDevServerHooksKey = \"__reactRouterDevServerHooks\";\nfunction setDevServerHooks(devServerHooks) {\n  globalThis[globalDevServerHooksKey] = devServerHooks;\n}\nfunction getDevServerHooks() {\n  return globalThis[globalDevServerHooksKey];\n}\nfunction getBuildTimeHeader(request, headerName) {\n  if (typeof process !== \"undefined\") {\n    try {\n      if (process.env?.IS_RR_BUILD_REQUEST === \"yes\") {\n        return request.headers.get(headerName);\n      }\n    } catch (e) {\n    }\n  }\n  return null;\n}\n\n// lib/server-runtime/routes.ts\nfunction groupRoutesByParentId(manifest) {\n  let routes = {};\n  Object.values(manifest).forEach((route) => {\n    if (route) {\n      let parentId = route.parentId || \"\";\n      if (!routes[parentId]) {\n        routes[parentId] = [];\n      }\n      routes[parentId].push(route);\n    }\n  });\n  return routes;\n}\nfunction createRoutes(manifest, parentId = \"\", routesByParentId = groupRoutesByParentId(manifest)) {\n  return (routesByParentId[parentId] || []).map((route) => ({\n    ...route,\n    children: createRoutes(manifest, route.id, routesByParentId)\n  }));\n}\nfunction createStaticHandlerDataRoutes(manifest, future, parentId = \"\", routesByParentId = groupRoutesByParentId(manifest)) {\n  return (routesByParentId[parentId] || []).map((route) => {\n    let commonRoute = {\n      // Always include root due to default boundaries\n      hasErrorBoundary: route.id === \"root\" || route.module.ErrorBoundary != null,\n      id: route.id,\n      path: route.path,\n      unstable_middleware: route.module.unstable_middleware,\n      // Need to use RR's version in the param typed here to permit the optional\n      // context even though we know it'll always be provided in remix\n      loader: route.module.loader ? async (args) => {\n        let preRenderedData = getBuildTimeHeader(\n          args.request,\n          \"X-React-Router-Prerender-Data\"\n        );\n        if (preRenderedData != null) {\n          let encoded = preRenderedData ? decodeURI(preRenderedData) : preRenderedData;\n          invariant2(encoded, \"Missing prerendered data for route\");\n          let uint8array = new TextEncoder().encode(encoded);\n          let stream = new ReadableStream({\n            start(controller) {\n              controller.enqueue(uint8array);\n              controller.close();\n            }\n          });\n          let decoded = await decodeViaTurboStream(stream, global);\n          let data2 = decoded.value;\n          if (data2 && SingleFetchRedirectSymbol in data2) {\n            let result = data2[SingleFetchRedirectSymbol];\n            let init = { status: result.status };\n            if (result.reload) {\n              throw redirectDocument(result.redirect, init);\n            } else if (result.replace) {\n              throw replace(result.redirect, init);\n            } else {\n              throw redirect(result.redirect, init);\n            }\n          } else {\n            invariant2(\n              data2 && route.id in data2,\n              \"Unable to decode prerendered data\"\n            );\n            let result = data2[route.id];\n            invariant2(\n              \"data\" in result,\n              \"Unable to process prerendered data\"\n            );\n            return result.data;\n          }\n        }\n        let val = await callRouteHandler(route.module.loader, args);\n        return val;\n      } : void 0,\n      action: route.module.action ? (args) => callRouteHandler(route.module.action, args) : void 0,\n      handle: route.module.handle\n    };\n    return route.index ? {\n      index: true,\n      ...commonRoute\n    } : {\n      caseSensitive: route.caseSensitive,\n      children: createStaticHandlerDataRoutes(\n        manifest,\n        future,\n        route.id,\n        routesByParentId\n      ),\n      ...commonRoute\n    };\n  });\n}\n\n// lib/server-runtime/markup.ts\nvar ESCAPE_LOOKUP = {\n  \"&\": \"\\\\u0026\",\n  \">\": \"\\\\u003e\",\n  \"<\": \"\\\\u003c\",\n  \"\\u2028\": \"\\\\u2028\",\n  \"\\u2029\": \"\\\\u2029\"\n};\nvar ESCAPE_REGEX = /[&><\\u2028\\u2029]/g;\nfunction escapeHtml(html) {\n  return html.replace(ESCAPE_REGEX, (match) => ESCAPE_LOOKUP[match]);\n}\n\n// lib/server-runtime/serverHandoff.ts\nfunction createServerHandoffString(serverHandoff) {\n  return escapeHtml(JSON.stringify(serverHandoff));\n}\n\n// lib/server-runtime/headers.ts\nimport { splitCookiesString } from \"set-cookie-parser\";\nfunction getDocumentHeaders(context, build) {\n  return getDocumentHeadersImpl(context, (m) => {\n    let route = build.routes[m.route.id];\n    invariant2(route, `Route with id \"${m.route.id}\" not found in build`);\n    return route.module.headers;\n  });\n}\nfunction getDocumentHeadersImpl(context, getRouteHeadersFn) {\n  let boundaryIdx = context.errors ? context.matches.findIndex((m) => context.errors[m.route.id]) : -1;\n  let matches = boundaryIdx >= 0 ? context.matches.slice(0, boundaryIdx + 1) : context.matches;\n  let errorHeaders;\n  if (boundaryIdx >= 0) {\n    let { actionHeaders, actionData, loaderHeaders, loaderData } = context;\n    context.matches.slice(boundaryIdx).some((match) => {\n      let id = match.route.id;\n      if (actionHeaders[id] && (!actionData || !actionData.hasOwnProperty(id))) {\n        errorHeaders = actionHeaders[id];\n      } else if (loaderHeaders[id] && !loaderData.hasOwnProperty(id)) {\n        errorHeaders = loaderHeaders[id];\n      }\n      return errorHeaders != null;\n    });\n  }\n  return matches.reduce((parentHeaders, match, idx) => {\n    let { id } = match.route;\n    let loaderHeaders = context.loaderHeaders[id] || new Headers();\n    let actionHeaders = context.actionHeaders[id] || new Headers();\n    let includeErrorHeaders = errorHeaders != null && idx === matches.length - 1;\n    let includeErrorCookies = includeErrorHeaders && errorHeaders !== loaderHeaders && errorHeaders !== actionHeaders;\n    let headersFn = getRouteHeadersFn(match);\n    if (headersFn == null) {\n      let headers2 = new Headers(parentHeaders);\n      if (includeErrorCookies) {\n        prependCookies(errorHeaders, headers2);\n      }\n      prependCookies(actionHeaders, headers2);\n      prependCookies(loaderHeaders, headers2);\n      return headers2;\n    }\n    let headers = new Headers(\n      typeof headersFn === \"function\" ? headersFn({\n        loaderHeaders,\n        parentHeaders,\n        actionHeaders,\n        errorHeaders: includeErrorHeaders ? errorHeaders : void 0\n      }) : headersFn\n    );\n    if (includeErrorCookies) {\n      prependCookies(errorHeaders, headers);\n    }\n    prependCookies(actionHeaders, headers);\n    prependCookies(loaderHeaders, headers);\n    prependCookies(parentHeaders, headers);\n    return headers;\n  }, new Headers());\n}\nfunction prependCookies(parentHeaders, childHeaders) {\n  let parentSetCookieString = parentHeaders.get(\"Set-Cookie\");\n  if (parentSetCookieString) {\n    let cookies = splitCookiesString(parentSetCookieString);\n    let childCookies = new Set(childHeaders.getSetCookie());\n    cookies.forEach((cookie) => {\n      if (!childCookies.has(cookie)) {\n        childHeaders.append(\"Set-Cookie\", cookie);\n      }\n    });\n  }\n}\n\n// lib/server-runtime/single-fetch.ts\nvar SERVER_NO_BODY_STATUS_CODES = /* @__PURE__ */ new Set([\n  ...NO_BODY_STATUS_CODES,\n  304\n]);\nasync function singleFetchAction(build, serverMode, staticHandler, request, handlerUrl, loadContext, handleError) {\n  try {\n    let respond2 = function(context) {\n      let headers = getDocumentHeaders(context, build);\n      if (isRedirectStatusCode(context.statusCode) && headers.has(\"Location\")) {\n        return generateSingleFetchResponse(request, build, serverMode, {\n          result: getSingleFetchRedirect(\n            context.statusCode,\n            headers,\n            build.basename\n          ),\n          headers,\n          status: SINGLE_FETCH_REDIRECT_STATUS\n        });\n      }\n      if (context.errors) {\n        Object.values(context.errors).forEach((err) => {\n          if (!isRouteErrorResponse(err) || err.error) {\n            handleError(err);\n          }\n        });\n        context.errors = sanitizeErrors(context.errors, serverMode);\n      }\n      let singleFetchResult;\n      if (context.errors) {\n        singleFetchResult = { error: Object.values(context.errors)[0] };\n      } else {\n        singleFetchResult = {\n          data: Object.values(context.actionData || {})[0]\n        };\n      }\n      return generateSingleFetchResponse(request, build, serverMode, {\n        result: singleFetchResult,\n        headers,\n        status: context.statusCode\n      });\n    };\n    var respond = respond2;\n    let handlerRequest = new Request(handlerUrl, {\n      method: request.method,\n      body: request.body,\n      headers: request.headers,\n      signal: request.signal,\n      ...request.body ? { duplex: \"half\" } : void 0\n    });\n    let result = await staticHandler.query(handlerRequest, {\n      requestContext: loadContext,\n      skipLoaderErrorBubbling: true,\n      skipRevalidation: true,\n      unstable_respond: respond2\n    });\n    if (!isResponse(result)) {\n      result = respond2(result);\n    }\n    if (isRedirectResponse(result)) {\n      return generateSingleFetchResponse(request, build, serverMode, {\n        result: getSingleFetchRedirect(\n          result.status,\n          result.headers,\n          build.basename\n        ),\n        headers: result.headers,\n        status: SINGLE_FETCH_REDIRECT_STATUS\n      });\n    }\n    return result;\n  } catch (error) {\n    handleError(error);\n    return generateSingleFetchResponse(request, build, serverMode, {\n      result: { error },\n      headers: new Headers(),\n      status: 500\n    });\n  }\n}\nasync function singleFetchLoaders(build, serverMode, staticHandler, request, handlerUrl, loadContext, handleError) {\n  try {\n    let respond2 = function(context) {\n      let headers = getDocumentHeaders(context, build);\n      if (isRedirectStatusCode(context.statusCode) && headers.has(\"Location\")) {\n        return generateSingleFetchResponse(request, build, serverMode, {\n          result: {\n            [SingleFetchRedirectSymbol]: getSingleFetchRedirect(\n              context.statusCode,\n              headers,\n              build.basename\n            )\n          },\n          headers,\n          status: SINGLE_FETCH_REDIRECT_STATUS\n        });\n      }\n      if (context.errors) {\n        Object.values(context.errors).forEach((err) => {\n          if (!isRouteErrorResponse(err) || err.error) {\n            handleError(err);\n          }\n        });\n        context.errors = sanitizeErrors(context.errors, serverMode);\n      }\n      let results = {};\n      let loadedMatches = new Set(\n        context.matches.filter(\n          (m) => loadRouteIds ? loadRouteIds.has(m.route.id) : m.route.loader != null\n        ).map((m) => m.route.id)\n      );\n      if (context.errors) {\n        for (let [id, error] of Object.entries(context.errors)) {\n          results[id] = { error };\n        }\n      }\n      for (let [id, data2] of Object.entries(context.loaderData)) {\n        if (!(id in results) && loadedMatches.has(id)) {\n          results[id] = { data: data2 };\n        }\n      }\n      return generateSingleFetchResponse(request, build, serverMode, {\n        result: results,\n        headers,\n        status: context.statusCode\n      });\n    };\n    var respond = respond2;\n    let handlerRequest = new Request(handlerUrl, {\n      headers: request.headers,\n      signal: request.signal\n    });\n    let routesParam = new URL(request.url).searchParams.get(\"_routes\");\n    let loadRouteIds = routesParam ? new Set(routesParam.split(\",\")) : null;\n    let result = await staticHandler.query(handlerRequest, {\n      requestContext: loadContext,\n      filterMatchesToLoad: (m) => !loadRouteIds || loadRouteIds.has(m.route.id),\n      skipLoaderErrorBubbling: true,\n      unstable_respond: respond2\n    });\n    if (!isResponse(result)) {\n      result = respond2(result);\n    }\n    if (isRedirectResponse(result)) {\n      return generateSingleFetchResponse(request, build, serverMode, {\n        result: {\n          [SingleFetchRedirectSymbol]: getSingleFetchRedirect(\n            result.status,\n            result.headers,\n            build.basename\n          )\n        },\n        headers: result.headers,\n        status: SINGLE_FETCH_REDIRECT_STATUS\n      });\n    }\n    return result;\n  } catch (error) {\n    handleError(error);\n    return generateSingleFetchResponse(request, build, serverMode, {\n      result: { root: { error } },\n      headers: new Headers(),\n      status: 500\n    });\n  }\n}\nfunction generateSingleFetchResponse(request, build, serverMode, {\n  result,\n  headers,\n  status\n}) {\n  let resultHeaders = new Headers(headers);\n  resultHeaders.set(\"X-Remix-Response\", \"yes\");\n  if (SERVER_NO_BODY_STATUS_CODES.has(status)) {\n    return new Response(null, { status, headers: resultHeaders });\n  }\n  resultHeaders.set(\"Content-Type\", \"text/x-script\");\n  resultHeaders.delete(\"Content-Length\");\n  return new Response(\n    encodeViaTurboStream(\n      result,\n      request.signal,\n      build.entry.module.streamTimeout,\n      serverMode\n    ),\n    {\n      status: status || 200,\n      headers: resultHeaders\n    }\n  );\n}\nfunction getSingleFetchRedirect(status, headers, basename) {\n  let redirect2 = headers.get(\"Location\");\n  if (basename) {\n    redirect2 = stripBasename(redirect2, basename) || redirect2;\n  }\n  return {\n    redirect: redirect2,\n    status,\n    revalidate: (\n      // Technically X-Remix-Revalidate isn't needed here - that was an implementation\n      // detail of ?_data requests as our way to tell the front end to revalidate when\n      // we didn't have a response body to include that information in.\n      // With single fetch, we tell the front end via this revalidate boolean field.\n      // However, we're respecting it for now because it may be something folks have\n      // used in their own responses\n      // TODO(v3): Consider removing or making this official public API\n      headers.has(\"X-Remix-Revalidate\") || headers.has(\"Set-Cookie\")\n    ),\n    reload: headers.has(\"X-Remix-Reload-Document\"),\n    replace: headers.has(\"X-Remix-Replace\")\n  };\n}\nfunction encodeViaTurboStream(data2, requestSignal, streamTimeout, serverMode) {\n  let controller = new AbortController();\n  let timeoutId = setTimeout(\n    () => controller.abort(new Error(\"Server Timeout\")),\n    typeof streamTimeout === \"number\" ? streamTimeout : 4950\n  );\n  requestSignal.addEventListener(\"abort\", () => clearTimeout(timeoutId));\n  return encode(data2, {\n    signal: controller.signal,\n    plugins: [\n      (value) => {\n        if (value instanceof Error) {\n          let { name, message, stack } = serverMode === \"production\" /* Production */ ? sanitizeError(value, serverMode) : value;\n          return [\"SanitizedError\", name, message, stack];\n        }\n        if (value instanceof ErrorResponseImpl) {\n          let { data: data3, status, statusText } = value;\n          return [\"ErrorResponse\", data3, status, statusText];\n        }\n        if (value && typeof value === \"object\" && SingleFetchRedirectSymbol in value) {\n          return [\"SingleFetchRedirect\", value[SingleFetchRedirectSymbol]];\n        }\n      }\n    ],\n    postPlugins: [\n      (value) => {\n        if (!value) return;\n        if (typeof value !== \"object\") return;\n        return [\n          \"SingleFetchClassInstance\",\n          Object.fromEntries(Object.entries(value))\n        ];\n      },\n      () => [\"SingleFetchFallback\"]\n    ]\n  });\n}\n\n// lib/server-runtime/server.ts\nfunction derive(build, mode) {\n  let routes = createRoutes(build.routes);\n  let dataRoutes = createStaticHandlerDataRoutes(build.routes, build.future);\n  let serverMode = isServerMode(mode) ? mode : \"production\" /* Production */;\n  let staticHandler = createStaticHandler(dataRoutes, {\n    basename: build.basename\n  });\n  let errorHandler = build.entry.module.handleError || ((error, { request }) => {\n    if (serverMode !== \"test\" /* Test */ && !request.signal.aborted) {\n      console.error(\n        // @ts-expect-error This is \"private\" from users but intended for internal use\n        isRouteErrorResponse(error) && error.error ? error.error : error\n      );\n    }\n  });\n  return {\n    routes,\n    dataRoutes,\n    serverMode,\n    staticHandler,\n    errorHandler\n  };\n}\nvar createRequestHandler = (build, mode) => {\n  let _build;\n  let routes;\n  let serverMode;\n  let staticHandler;\n  let errorHandler;\n  return async function requestHandler(request, initialContext) {\n    _build = typeof build === \"function\" ? await build() : build;\n    if (typeof build === \"function\") {\n      let derived = derive(_build, mode);\n      routes = derived.routes;\n      serverMode = derived.serverMode;\n      staticHandler = derived.staticHandler;\n      errorHandler = derived.errorHandler;\n    } else if (!routes || !serverMode || !staticHandler || !errorHandler) {\n      let derived = derive(_build, mode);\n      routes = derived.routes;\n      serverMode = derived.serverMode;\n      staticHandler = derived.staticHandler;\n      errorHandler = derived.errorHandler;\n    }\n    let params = {};\n    let loadContext;\n    let handleError = (error) => {\n      if (mode === \"development\" /* Development */) {\n        getDevServerHooks()?.processRequestError?.(error);\n      }\n      errorHandler(error, {\n        context: loadContext,\n        params,\n        request\n      });\n    };\n    if (_build.future.unstable_middleware) {\n      if (initialContext == null) {\n        loadContext = new unstable_RouterContextProvider();\n      } else {\n        try {\n          loadContext = new unstable_RouterContextProvider(\n            initialContext\n          );\n        } catch (e) {\n          let error = new Error(\n            `Unable to create initial \\`unstable_RouterContextProvider\\` instance. Please confirm you are returning an instance of \\`Map<unstable_routerContext, unknown>\\` from your \\`getLoadContext\\` function.\n\nError: ${e instanceof Error ? e.toString() : e}`\n          );\n          handleError(error);\n          return returnLastResortErrorResponse(error, serverMode);\n        }\n      }\n    } else {\n      loadContext = initialContext || {};\n    }\n    let url = new URL(request.url);\n    let normalizedBasename = _build.basename || \"/\";\n    let normalizedPath = url.pathname;\n    if (stripBasename(normalizedPath, normalizedBasename) === \"/_root.data\") {\n      normalizedPath = normalizedBasename;\n    } else if (normalizedPath.endsWith(\".data\")) {\n      normalizedPath = normalizedPath.replace(/\\.data$/, \"\");\n    }\n    if (stripBasename(normalizedPath, normalizedBasename) !== \"/\" && normalizedPath.endsWith(\"/\")) {\n      normalizedPath = normalizedPath.slice(0, -1);\n    }\n    let isSpaMode = getBuildTimeHeader(request, \"X-React-Router-SPA-Mode\") === \"yes\";\n    if (!_build.ssr) {\n      let decodedPath = decodeURI(normalizedPath);\n      if (_build.prerender.length === 0) {\n        isSpaMode = true;\n      } else if (!_build.prerender.includes(decodedPath) && !_build.prerender.includes(decodedPath + \"/\")) {\n        if (url.pathname.endsWith(\".data\")) {\n          errorHandler(\n            new ErrorResponseImpl(\n              404,\n              \"Not Found\",\n              `Refusing to SSR the path \\`${decodedPath}\\` because \\`ssr:false\\` is set and the path is not included in the \\`prerender\\` config, so in production the path will be a 404.`\n            ),\n            {\n              context: loadContext,\n              params,\n              request\n            }\n          );\n          return new Response(\"Not Found\", {\n            status: 404,\n            statusText: \"Not Found\"\n          });\n        } else {\n          isSpaMode = true;\n        }\n      }\n    }\n    let manifestUrl = getManifestPath(\n      _build.routeDiscovery.manifestPath,\n      normalizedBasename\n    );\n    if (url.pathname === manifestUrl) {\n      try {\n        let res = await handleManifestRequest(_build, routes, url);\n        return res;\n      } catch (e) {\n        handleError(e);\n        return new Response(\"Unknown Server Error\", { status: 500 });\n      }\n    }\n    let matches = matchServerRoutes(routes, normalizedPath, _build.basename);\n    if (matches && matches.length > 0) {\n      Object.assign(params, matches[0].params);\n    }\n    let response;\n    if (url.pathname.endsWith(\".data\")) {\n      let handlerUrl = new URL(request.url);\n      handlerUrl.pathname = normalizedPath;\n      let singleFetchMatches = matchServerRoutes(\n        routes,\n        handlerUrl.pathname,\n        _build.basename\n      );\n      response = await handleSingleFetchRequest(\n        serverMode,\n        _build,\n        staticHandler,\n        request,\n        handlerUrl,\n        loadContext,\n        handleError\n      );\n      if (_build.entry.module.handleDataRequest) {\n        response = await _build.entry.module.handleDataRequest(response, {\n          context: loadContext,\n          params: singleFetchMatches ? singleFetchMatches[0].params : {},\n          request\n        });\n        if (isRedirectResponse(response)) {\n          let result = getSingleFetchRedirect(\n            response.status,\n            response.headers,\n            _build.basename\n          );\n          if (request.method === \"GET\") {\n            result = {\n              [SingleFetchRedirectSymbol]: result\n            };\n          }\n          let headers = new Headers(response.headers);\n          headers.set(\"Content-Type\", \"text/x-script\");\n          return new Response(\n            encodeViaTurboStream(\n              result,\n              request.signal,\n              _build.entry.module.streamTimeout,\n              serverMode\n            ),\n            {\n              status: SINGLE_FETCH_REDIRECT_STATUS,\n              headers\n            }\n          );\n        }\n      }\n    } else if (!isSpaMode && matches && matches[matches.length - 1].route.module.default == null && matches[matches.length - 1].route.module.ErrorBoundary == null) {\n      response = await handleResourceRequest(\n        serverMode,\n        _build,\n        staticHandler,\n        matches.slice(-1)[0].route.id,\n        request,\n        loadContext,\n        handleError\n      );\n    } else {\n      let { pathname } = url;\n      let criticalCss = void 0;\n      if (_build.unstable_getCriticalCss) {\n        criticalCss = await _build.unstable_getCriticalCss({ pathname });\n      } else if (mode === \"development\" /* Development */ && getDevServerHooks()?.getCriticalCss) {\n        criticalCss = await getDevServerHooks()?.getCriticalCss?.(pathname);\n      }\n      response = await handleDocumentRequest(\n        serverMode,\n        _build,\n        staticHandler,\n        request,\n        loadContext,\n        handleError,\n        isSpaMode,\n        criticalCss\n      );\n    }\n    if (request.method === \"HEAD\") {\n      return new Response(null, {\n        headers: response.headers,\n        status: response.status,\n        statusText: response.statusText\n      });\n    }\n    return response;\n  };\n};\nasync function handleManifestRequest(build, routes, url) {\n  if (build.assets.version !== url.searchParams.get(\"version\")) {\n    return new Response(null, {\n      status: 204,\n      headers: {\n        \"X-Remix-Reload-Document\": \"true\"\n      }\n    });\n  }\n  let patches = {};\n  if (url.searchParams.has(\"p\")) {\n    let paths = /* @__PURE__ */ new Set();\n    url.searchParams.getAll(\"p\").forEach((path) => {\n      if (!path.startsWith(\"/\")) {\n        path = `/${path}`;\n      }\n      let segments = path.split(\"/\").slice(1);\n      segments.forEach((_, i) => {\n        let partialPath = segments.slice(0, i + 1).join(\"/\");\n        paths.add(`/${partialPath}`);\n      });\n    });\n    for (let path of paths) {\n      let matches = matchServerRoutes(routes, path, build.basename);\n      if (matches) {\n        for (let match of matches) {\n          let routeId = match.route.id;\n          let route = build.assets.routes[routeId];\n          if (route) {\n            patches[routeId] = route;\n          }\n        }\n      }\n    }\n    return Response.json(patches, {\n      headers: {\n        \"Cache-Control\": \"public, max-age=31536000, immutable\"\n      }\n    });\n  }\n  return new Response(\"Invalid Request\", { status: 400 });\n}\nasync function handleSingleFetchRequest(serverMode, build, staticHandler, request, handlerUrl, loadContext, handleError) {\n  let response = request.method !== \"GET\" ? await singleFetchAction(\n    build,\n    serverMode,\n    staticHandler,\n    request,\n    handlerUrl,\n    loadContext,\n    handleError\n  ) : await singleFetchLoaders(\n    build,\n    serverMode,\n    staticHandler,\n    request,\n    handlerUrl,\n    loadContext,\n    handleError\n  );\n  return response;\n}\nasync function handleDocumentRequest(serverMode, build, staticHandler, request, loadContext, handleError, isSpaMode, criticalCss) {\n  try {\n    let response = await staticHandler.query(request, {\n      requestContext: loadContext,\n      unstable_respond: build.future.unstable_middleware ? (ctx) => renderHtml(ctx, isSpaMode) : void 0\n    });\n    return isResponse(response) ? response : renderHtml(response, isSpaMode);\n  } catch (error) {\n    handleError(error);\n    return new Response(null, { status: 500 });\n  }\n  async function renderHtml(context, isSpaMode2) {\n    if (isResponse(context)) {\n      return context;\n    }\n    let headers = getDocumentHeaders(context, build);\n    if (SERVER_NO_BODY_STATUS_CODES.has(context.statusCode)) {\n      return new Response(null, { status: context.statusCode, headers });\n    }\n    if (context.errors) {\n      Object.values(context.errors).forEach((err) => {\n        if (!isRouteErrorResponse(err) || err.error) {\n          handleError(err);\n        }\n      });\n      context.errors = sanitizeErrors(context.errors, serverMode);\n    }\n    let state = {\n      loaderData: context.loaderData,\n      actionData: context.actionData,\n      errors: serializeErrors(context.errors, serverMode)\n    };\n    let baseServerHandoff = {\n      basename: build.basename,\n      future: build.future,\n      routeDiscovery: build.routeDiscovery,\n      ssr: build.ssr,\n      isSpaMode: isSpaMode2\n    };\n    let entryContext = {\n      manifest: build.assets,\n      routeModules: createEntryRouteModules(build.routes),\n      staticHandlerContext: context,\n      criticalCss,\n      serverHandoffString: createServerHandoffString({\n        ...baseServerHandoff,\n        criticalCss\n      }),\n      serverHandoffStream: encodeViaTurboStream(\n        state,\n        request.signal,\n        build.entry.module.streamTimeout,\n        serverMode\n      ),\n      renderMeta: {},\n      future: build.future,\n      ssr: build.ssr,\n      routeDiscovery: build.routeDiscovery,\n      isSpaMode: isSpaMode2,\n      serializeError: (err) => serializeError(err, serverMode)\n    };\n    let handleDocumentRequestFunction = build.entry.module.default;\n    try {\n      return await handleDocumentRequestFunction(\n        request,\n        context.statusCode,\n        headers,\n        entryContext,\n        loadContext\n      );\n    } catch (error) {\n      handleError(error);\n      let errorForSecondRender = error;\n      if (isResponse(error)) {\n        try {\n          let data2 = await unwrapResponse(error);\n          errorForSecondRender = new ErrorResponseImpl(\n            error.status,\n            error.statusText,\n            data2\n          );\n        } catch (e) {\n        }\n      }\n      context = getStaticContextFromError(\n        staticHandler.dataRoutes,\n        context,\n        errorForSecondRender\n      );\n      if (context.errors) {\n        context.errors = sanitizeErrors(context.errors, serverMode);\n      }\n      let state2 = {\n        loaderData: context.loaderData,\n        actionData: context.actionData,\n        errors: serializeErrors(context.errors, serverMode)\n      };\n      entryContext = {\n        ...entryContext,\n        staticHandlerContext: context,\n        serverHandoffString: createServerHandoffString(baseServerHandoff),\n        serverHandoffStream: encodeViaTurboStream(\n          state2,\n          request.signal,\n          build.entry.module.streamTimeout,\n          serverMode\n        ),\n        renderMeta: {}\n      };\n      try {\n        return await handleDocumentRequestFunction(\n          request,\n          context.statusCode,\n          headers,\n          entryContext,\n          loadContext\n        );\n      } catch (error2) {\n        handleError(error2);\n        return returnLastResortErrorResponse(error2, serverMode);\n      }\n    }\n  }\n}\nasync function handleResourceRequest(serverMode, build, staticHandler, routeId, request, loadContext, handleError) {\n  try {\n    let response = await staticHandler.queryRoute(request, {\n      routeId,\n      requestContext: loadContext,\n      unstable_respond: build.future.unstable_middleware ? (ctx) => ctx : void 0\n    });\n    if (isResponse(response)) {\n      return response;\n    }\n    if (typeof response === \"string\") {\n      return new Response(response);\n    }\n    return Response.json(response);\n  } catch (error) {\n    if (isResponse(error)) {\n      error.headers.set(\"X-Remix-Catch\", \"yes\");\n      return error;\n    }\n    if (isRouteErrorResponse(error)) {\n      if (error) {\n        handleError(error);\n      }\n      return errorResponseToJson(error, serverMode);\n    }\n    if (error instanceof Error && error.message === \"Expected a response from queryRoute\") {\n      let newError = new Error(\n        \"Expected a Response to be returned from resource route handler\"\n      );\n      handleError(newError);\n      return returnLastResortErrorResponse(newError, serverMode);\n    }\n    handleError(error);\n    return returnLastResortErrorResponse(error, serverMode);\n  }\n}\nfunction errorResponseToJson(errorResponse, serverMode) {\n  return Response.json(\n    serializeError(\n      // @ts-expect-error This is \"private\" from users but intended for internal use\n      errorResponse.error || new Error(\"Unexpected Server Error\"),\n      serverMode\n    ),\n    {\n      status: errorResponse.status,\n      statusText: errorResponse.statusText,\n      headers: {\n        \"X-Remix-Error\": \"yes\"\n      }\n    }\n  );\n}\nfunction returnLastResortErrorResponse(error, serverMode) {\n  let message = \"Unexpected Server Error\";\n  if (serverMode !== \"production\" /* Production */) {\n    message += `\n\n${String(error)}`;\n  }\n  return new Response(message, {\n    status: 500,\n    headers: {\n      \"Content-Type\": \"text/plain\"\n    }\n  });\n}\nfunction unwrapResponse(response) {\n  let contentType = response.headers.get(\"Content-Type\");\n  return contentType && /\\bapplication\\/json\\b/.test(contentType) ? response.body == null ? null : response.json() : response.text();\n}\n\n// lib/server-runtime/sessions.ts\nfunction flash(name) {\n  return `__flash_${name}__`;\n}\nvar createSession = (initialData = {}, id = \"\") => {\n  let map = new Map(Object.entries(initialData));\n  return {\n    get id() {\n      return id;\n    },\n    get data() {\n      return Object.fromEntries(map);\n    },\n    has(name) {\n      return map.has(name) || map.has(flash(name));\n    },\n    get(name) {\n      if (map.has(name)) return map.get(name);\n      let flashName = flash(name);\n      if (map.has(flashName)) {\n        let value = map.get(flashName);\n        map.delete(flashName);\n        return value;\n      }\n      return void 0;\n    },\n    set(name, value) {\n      map.set(name, value);\n    },\n    flash(name, value) {\n      map.set(flash(name), value);\n    },\n    unset(name) {\n      map.delete(name);\n    }\n  };\n};\nvar isSession = (object) => {\n  return object != null && typeof object.id === \"string\" && typeof object.data !== \"undefined\" && typeof object.has === \"function\" && typeof object.get === \"function\" && typeof object.set === \"function\" && typeof object.flash === \"function\" && typeof object.unset === \"function\";\n};\nfunction createSessionStorage({\n  cookie: cookieArg,\n  createData,\n  readData,\n  updateData,\n  deleteData\n}) {\n  let cookie = isCookie(cookieArg) ? cookieArg : createCookie(cookieArg?.name || \"__session\", cookieArg);\n  warnOnceAboutSigningSessionCookie(cookie);\n  return {\n    async getSession(cookieHeader, options) {\n      let id = cookieHeader && await cookie.parse(cookieHeader, options);\n      let data2 = id && await readData(id);\n      return createSession(data2 || {}, id || \"\");\n    },\n    async commitSession(session, options) {\n      let { id, data: data2 } = session;\n      let expires = options?.maxAge != null ? new Date(Date.now() + options.maxAge * 1e3) : options?.expires != null ? options.expires : cookie.expires;\n      if (id) {\n        await updateData(id, data2, expires);\n      } else {\n        id = await createData(data2, expires);\n      }\n      return cookie.serialize(id, options);\n    },\n    async destroySession(session, options) {\n      await deleteData(session.id);\n      return cookie.serialize(\"\", {\n        ...options,\n        maxAge: void 0,\n        expires: /* @__PURE__ */ new Date(0)\n      });\n    }\n  };\n}\nfunction warnOnceAboutSigningSessionCookie(cookie) {\n  warnOnce(\n    cookie.isSigned,\n    `The \"${cookie.name}\" cookie is not signed, but session cookies should be signed to prevent tampering on the client before they are sent back to the server. See https://reactrouter.com/explanation/sessions-and-cookies#signing-cookies for more information.`\n  );\n}\n\n// lib/server-runtime/sessions/cookieStorage.ts\nfunction createCookieSessionStorage({ cookie: cookieArg } = {}) {\n  let cookie = isCookie(cookieArg) ? cookieArg : createCookie(cookieArg?.name || \"__session\", cookieArg);\n  warnOnceAboutSigningSessionCookie(cookie);\n  return {\n    async getSession(cookieHeader, options) {\n      return createSession(\n        cookieHeader && await cookie.parse(cookieHeader, options) || {}\n      );\n    },\n    async commitSession(session, options) {\n      let serializedCookie = await cookie.serialize(session.data, options);\n      if (serializedCookie.length > 4096) {\n        throw new Error(\n          \"Cookie length will exceed browser maximum. Length: \" + serializedCookie.length\n        );\n      }\n      return serializedCookie;\n    },\n    async destroySession(_session, options) {\n      return cookie.serialize(\"\", {\n        ...options,\n        maxAge: void 0,\n        expires: /* @__PURE__ */ new Date(0)\n      });\n    }\n  };\n}\n\n// lib/server-runtime/sessions/memoryStorage.ts\nfunction createMemorySessionStorage({ cookie } = {}) {\n  let map = /* @__PURE__ */ new Map();\n  return createSessionStorage({\n    cookie,\n    async createData(data2, expires) {\n      let id = Math.random().toString(36).substring(2, 10);\n      map.set(id, { data: data2, expires });\n      return id;\n    },\n    async readData(id) {\n      if (map.has(id)) {\n        let { data: data2, expires } = map.get(id);\n        if (!expires || expires > /* @__PURE__ */ new Date()) {\n          return data2;\n        }\n        if (expires) map.delete(id);\n      }\n      return null;\n    },\n    async updateData(id, data2, expires) {\n      map.set(id, { data: data2, expires });\n    },\n    async deleteData(id) {\n      map.delete(id);\n    }\n  });\n}\n\n// lib/href.ts\nfunction href(path, ...args) {\n  let params = args[0];\n  return path.split(\"/\").map((segment) => {\n    if (segment === \"*\") {\n      return params ? params[\"*\"] : void 0;\n    }\n    const match = segment.match(/^:([\\w-]+)(\\?)?/);\n    if (!match) return segment;\n    const param = match[1];\n    const value = params ? params[param] : void 0;\n    const isRequired = match[2] === void 0;\n    if (isRequired && value === void 0) {\n      throw Error(\n        `Path '${path}' requires param '${param}' but it was not provided`\n      );\n    }\n    return value;\n  }).filter((segment) => segment !== void 0).join(\"/\");\n}\n\n// lib/rsc/browser.tsx\nimport * as React4 from \"react\";\nimport * as ReactDOM from \"react-dom\";\n\n// lib/dom/ssr/hydration.tsx\nfunction getHydrationData(state, routes, getRouteInfo, location2, basename, isSpaMode) {\n  let hydrationData = {\n    ...state,\n    loaderData: { ...state.loaderData }\n  };\n  let initialMatches = matchRoutes(routes, location2, basename);\n  if (initialMatches) {\n    for (let match of initialMatches) {\n      let routeId = match.route.id;\n      let routeInfo = getRouteInfo(routeId);\n      if (shouldHydrateRouteLoader(\n        routeId,\n        routeInfo.clientLoader,\n        routeInfo.hasLoader,\n        isSpaMode\n      ) && (routeInfo.hasHydrateFallback || !routeInfo.hasLoader)) {\n        delete hydrationData.loaderData[routeId];\n      } else if (!routeInfo.hasLoader) {\n        hydrationData.loaderData[routeId] = null;\n      }\n    }\n  }\n  return hydrationData;\n}\n\n// lib/rsc/errorBoundaries.tsx\nimport React3 from \"react\";\nvar RSCRouterGlobalErrorBoundary = class extends React3.Component {\n  constructor(props) {\n    super(props);\n    this.state = { error: null, location: props.location };\n  }\n  static getDerivedStateFromError(error) {\n    return { error };\n  }\n  static getDerivedStateFromProps(props, state) {\n    if (state.location !== props.location) {\n      return { error: null, location: props.location };\n    }\n    return { error: state.error, location: state.location };\n  }\n  render() {\n    if (this.state.error) {\n      return /* @__PURE__ */ React3.createElement(\n        RSCDefaultRootErrorBoundaryImpl,\n        {\n          error: this.state.error,\n          renderAppShell: true\n        }\n      );\n    } else {\n      return this.props.children;\n    }\n  }\n};\nfunction ErrorWrapper({\n  renderAppShell,\n  title,\n  children\n}) {\n  if (!renderAppShell) {\n    return children;\n  }\n  return /* @__PURE__ */ React3.createElement(\"html\", { lang: \"en\" }, /* @__PURE__ */ React3.createElement(\"head\", null, /* @__PURE__ */ React3.createElement(\"meta\", { charSet: \"utf-8\" }), /* @__PURE__ */ React3.createElement(\n    \"meta\",\n    {\n      name: \"viewport\",\n      content: \"width=device-width,initial-scale=1,viewport-fit=cover\"\n    }\n  ), /* @__PURE__ */ React3.createElement(\"title\", null, title)), /* @__PURE__ */ React3.createElement(\"body\", null, /* @__PURE__ */ React3.createElement(\"main\", { style: { fontFamily: \"system-ui, sans-serif\", padding: \"2rem\" } }, children)));\n}\nfunction RSCDefaultRootErrorBoundaryImpl({\n  error,\n  renderAppShell\n}) {\n  console.error(error);\n  let heyDeveloper = /* @__PURE__ */ React3.createElement(\n    \"script\",\n    {\n      dangerouslySetInnerHTML: {\n        __html: `\n        console.log(\n          \"\\u{1F4BF} Hey developer \\u{1F44B}. You can provide a way better UX than this when your app throws errors. Check out https://reactrouter.com/how-to/error-boundary for more information.\"\n        );\n      `\n      }\n    }\n  );\n  if (isRouteErrorResponse(error)) {\n    return /* @__PURE__ */ React3.createElement(\n      ErrorWrapper,\n      {\n        renderAppShell,\n        title: \"Unhandled Thrown Response!\"\n      },\n      /* @__PURE__ */ React3.createElement(\"h1\", { style: { fontSize: \"24px\" } }, error.status, \" \", error.statusText),\n      ENABLE_DEV_WARNINGS ? heyDeveloper : null\n    );\n  }\n  let errorInstance;\n  if (error instanceof Error) {\n    errorInstance = error;\n  } else {\n    let errorString = error == null ? \"Unknown Error\" : typeof error === \"object\" && \"toString\" in error ? error.toString() : JSON.stringify(error);\n    errorInstance = new Error(errorString);\n  }\n  return /* @__PURE__ */ React3.createElement(ErrorWrapper, { renderAppShell, title: \"Application Error!\" }, /* @__PURE__ */ React3.createElement(\"h1\", { style: { fontSize: \"24px\" } }, \"Application Error\"), /* @__PURE__ */ React3.createElement(\n    \"pre\",\n    {\n      style: {\n        padding: \"2rem\",\n        background: \"hsla(10, 50%, 50%, 0.1)\",\n        color: \"red\",\n        overflow: \"auto\"\n      }\n    },\n    errorInstance.stack\n  ), heyDeveloper);\n}\nfunction RSCDefaultRootErrorBoundary({\n  hasRootLayout\n}) {\n  let error = useRouteError();\n  if (hasRootLayout === void 0) {\n    throw new Error(\"Missing 'hasRootLayout' prop\");\n  }\n  return /* @__PURE__ */ React3.createElement(\n    RSCDefaultRootErrorBoundaryImpl,\n    {\n      renderAppShell: !hasRootLayout,\n      error\n    }\n  );\n}\n\n// lib/rsc/browser.tsx\nfunction createCallServer({\n  createFromReadableStream,\n  createTemporaryReferenceSet,\n  encodeReply,\n  fetch: fetchImplementation = fetch\n}) {\n  const globalVar = window;\n  let landedActionId = 0;\n  return async (id, args) => {\n    let actionId = globalVar.__routerActionID = (globalVar.__routerActionID ?? (globalVar.__routerActionID = 0)) + 1;\n    const temporaryReferences = createTemporaryReferenceSet();\n    const response = await fetchImplementation(\n      new Request(location.href, {\n        body: await encodeReply(args, { temporaryReferences }),\n        method: \"POST\",\n        headers: {\n          Accept: \"text/x-component\",\n          \"rsc-action-id\": id\n        }\n      })\n    );\n    if (!response.body) {\n      throw new Error(\"No response body\");\n    }\n    const payload = await createFromReadableStream(response.body, {\n      temporaryReferences\n    });\n    if (payload.type === \"redirect\") {\n      if (payload.reload) {\n        window.location.href = payload.location;\n        return;\n      }\n      globalVar.__router.navigate(payload.location, {\n        replace: payload.replace\n      });\n      return payload.actionResult;\n    }\n    if (payload.type !== \"action\") {\n      throw new Error(\"Unexpected payload type\");\n    }\n    if (payload.rerender) {\n      React4.startTransition(\n        // @ts-expect-error - We have old react types that don't know this can be async\n        async () => {\n          const rerender = await payload.rerender;\n          if (!rerender) return;\n          if (landedActionId < actionId && globalVar.__routerActionID <= actionId) {\n            landedActionId = actionId;\n            if (rerender.type === \"redirect\") {\n              if (rerender.reload) {\n                window.location.href = rerender.location;\n                return;\n              }\n              globalVar.__router.navigate(rerender.location, {\n                replace: rerender.replace\n              });\n              return;\n            }\n            let lastMatch;\n            for (const match of rerender.matches) {\n              globalVar.__router.patchRoutes(\n                lastMatch?.id ?? null,\n                [createRouteFromServerManifest(match)],\n                true\n              );\n              lastMatch = match;\n            }\n            window.__router._internalSetStateDoNotUseOrYouWillBreakYourApp({});\n            React4.startTransition(() => {\n              window.__router._internalSetStateDoNotUseOrYouWillBreakYourApp({\n                loaderData: Object.assign(\n                  {},\n                  globalVar.__router.state.loaderData,\n                  rerender.loaderData\n                ),\n                errors: rerender.errors ? Object.assign(\n                  {},\n                  globalVar.__router.state.errors,\n                  rerender.errors\n                ) : null\n              });\n            });\n          }\n        }\n      );\n    }\n    return payload.actionResult;\n  };\n}\nfunction createRouterFromPayload({\n  fetchImplementation,\n  createFromReadableStream,\n  unstable_getContext,\n  payload\n}) {\n  const globalVar = window;\n  if (globalVar.__router) return globalVar.__router;\n  if (payload.type !== \"render\") throw new Error(\"Invalid payload type\");\n  let patches = /* @__PURE__ */ new Map();\n  payload.patches?.forEach((patch) => {\n    invariant(patch.parentId, \"Invalid patch parentId\");\n    if (!patches.has(patch.parentId)) {\n      patches.set(patch.parentId, []);\n    }\n    patches.get(patch.parentId)?.push(patch);\n  });\n  let routes = payload.matches.reduceRight((previous, match) => {\n    const route = createRouteFromServerManifest(\n      match,\n      payload\n    );\n    if (previous.length > 0) {\n      route.children = previous;\n      let childrenToPatch = patches.get(match.id);\n      if (childrenToPatch) {\n        route.children.push(\n          ...childrenToPatch.map((r) => createRouteFromServerManifest(r))\n        );\n      }\n    }\n    return [route];\n  }, []);\n  globalVar.__router = createRouter({\n    routes,\n    unstable_getContext,\n    basename: payload.basename,\n    history: createBrowserHistory(),\n    hydrationData: getHydrationData(\n      {\n        loaderData: payload.loaderData,\n        actionData: payload.actionData,\n        errors: payload.errors\n      },\n      routes,\n      (routeId) => {\n        let match = payload.matches.find((m) => m.id === routeId);\n        invariant(match, \"Route not found in payload\");\n        return {\n          clientLoader: match.clientLoader,\n          hasLoader: match.hasLoader,\n          hasHydrateFallback: match.hydrateFallbackElement != null\n        };\n      },\n      payload.location,\n      void 0,\n      false\n    ),\n    async patchRoutesOnNavigation({ path, signal }) {\n      if (discoveredPaths.has(path)) {\n        return;\n      }\n      await fetchAndApplyManifestPatches(\n        [path],\n        createFromReadableStream,\n        fetchImplementation,\n        signal\n      );\n    },\n    // FIXME: Pass `build.ssr` into this function\n    dataStrategy: getRSCSingleFetchDataStrategy(\n      () => globalVar.__router,\n      true,\n      payload.basename,\n      createFromReadableStream,\n      fetchImplementation\n    )\n  });\n  if (globalVar.__router.state.initialized) {\n    globalVar.__routerInitialized = true;\n    globalVar.__router.initialize();\n  } else {\n    globalVar.__routerInitialized = false;\n  }\n  let lastLoaderData = void 0;\n  globalVar.__router.subscribe(({ loaderData, actionData }) => {\n    if (lastLoaderData !== loaderData) {\n      globalVar.__routerActionID = (globalVar.__routerActionID ?? (globalVar.__routerActionID = 0)) + 1;\n    }\n  });\n  return globalVar.__router;\n}\nvar renderedRoutesContext = unstable_createContext();\nfunction getRSCSingleFetchDataStrategy(getRouter, ssr, basename, createFromReadableStream, fetchImplementation) {\n  let dataStrategy = getSingleFetchDataStrategyImpl(\n    getRouter,\n    (match) => {\n      let M = match;\n      return {\n        hasLoader: M.route.hasLoader,\n        hasClientLoader: M.route.hasClientLoader,\n        hasComponent: M.route.hasComponent,\n        hasAction: M.route.hasAction,\n        hasClientAction: M.route.hasClientAction,\n        hasShouldRevalidate: M.route.hasShouldRevalidate\n      };\n    },\n    // pass map into fetchAndDecode so it can add payloads\n    getFetchAndDecodeViaRSC(createFromReadableStream, fetchImplementation),\n    ssr,\n    basename,\n    // If the route has a component but we don't have an element, we need to hit\n    // the server loader flow regardless of whether the client loader calls\n    // `serverLoader` or not, otherwise we'll have nothing to render.\n    (match) => {\n      let M = match;\n      return M.route.hasComponent && !M.route.element;\n    }\n  );\n  return async (args) => args.unstable_runClientMiddleware(async () => {\n    let context = args.context;\n    context.set(renderedRoutesContext, []);\n    let results = await dataStrategy(args);\n    const renderedRoutesById = /* @__PURE__ */ new Map();\n    for (const route of context.get(renderedRoutesContext)) {\n      if (!renderedRoutesById.has(route.id)) {\n        renderedRoutesById.set(route.id, []);\n      }\n      renderedRoutesById.get(route.id).push(route);\n    }\n    for (const match of args.matches) {\n      const renderedRoutes = renderedRoutesById.get(match.route.id);\n      if (renderedRoutes) {\n        for (const rendered of renderedRoutes) {\n          window.__router.patchRoutes(\n            rendered.parentId ?? null,\n            [createRouteFromServerManifest(rendered)],\n            true\n          );\n        }\n      }\n    }\n    return results;\n  });\n}\nfunction getFetchAndDecodeViaRSC(createFromReadableStream, fetchImplementation) {\n  return async (args, basename, targetRoutes) => {\n    let { request, context } = args;\n    let url = singleFetchUrl(request.url, basename, \"rsc\");\n    if (request.method === \"GET\") {\n      url = stripIndexParam(url);\n      if (targetRoutes) {\n        url.searchParams.set(\"_routes\", targetRoutes.join(\",\"));\n      }\n    }\n    let res = await fetchImplementation(\n      new Request(url, await createRequestInit(request))\n    );\n    if (res.status === 404 && !res.headers.has(\"X-Remix-Response\")) {\n      throw new ErrorResponseImpl(404, \"Not Found\", true);\n    }\n    invariant(res.body, \"No response body to decode\");\n    try {\n      const payload = await createFromReadableStream(res.body, {\n        temporaryReferences: void 0\n      });\n      if (payload.type === \"redirect\") {\n        return {\n          status: res.status,\n          data: {\n            redirect: {\n              redirect: payload.location,\n              reload: payload.reload,\n              replace: payload.replace,\n              revalidate: false,\n              status: payload.status\n            }\n          }\n        };\n      }\n      if (payload.type !== \"render\") {\n        throw new Error(\"Unexpected payload type\");\n      }\n      context.get(renderedRoutesContext).push(...payload.matches);\n      let results = { routes: {} };\n      const dataKey = isMutationMethod(request.method) ? \"actionData\" : \"loaderData\";\n      for (let [routeId, data2] of Object.entries(payload[dataKey] || {})) {\n        results.routes[routeId] = { data: data2 };\n      }\n      if (payload.errors) {\n        for (let [routeId, error] of Object.entries(payload.errors)) {\n          results.routes[routeId] = { error };\n        }\n      }\n      return { status: res.status, data: results };\n    } catch (e) {\n      throw new Error(\"Unable to decode RSC response\");\n    }\n  };\n}\nfunction RSCHydratedRouter({\n  createFromReadableStream,\n  fetch: fetchImplementation = fetch,\n  payload,\n  routeDiscovery = \"eager\",\n  unstable_getContext\n}) {\n  if (payload.type !== \"render\") throw new Error(\"Invalid payload type\");\n  let router = React4.useMemo(\n    () => createRouterFromPayload({\n      payload,\n      fetchImplementation,\n      unstable_getContext,\n      createFromReadableStream\n    }),\n    [\n      createFromReadableStream,\n      payload,\n      fetchImplementation,\n      unstable_getContext\n    ]\n  );\n  React4.useLayoutEffect(() => {\n    const globalVar = window;\n    if (!globalVar.__routerInitialized) {\n      globalVar.__routerInitialized = true;\n      globalVar.__router.initialize();\n    }\n  }, []);\n  let [location2, setLocation] = React4.useState(router.state.location);\n  React4.useLayoutEffect(\n    () => router.subscribe((newState) => {\n      if (newState.location !== location2) {\n        setLocation(newState.location);\n      }\n    }),\n    [router, location2]\n  );\n  React4.useEffect(() => {\n    if (routeDiscovery === \"lazy\" || // @ts-expect-error - TS doesn't know about this yet\n    window.navigator?.connection?.saveData === true) {\n      return;\n    }\n    function registerElement(el) {\n      let path = el.tagName === \"FORM\" ? el.getAttribute(\"action\") : el.getAttribute(\"href\");\n      if (!path) {\n        return;\n      }\n      let pathname = el.tagName === \"A\" ? el.pathname : new URL(path, window.location.origin).pathname;\n      if (!discoveredPaths.has(pathname)) {\n        nextPaths.add(pathname);\n      }\n    }\n    async function fetchPatches() {\n      document.querySelectorAll(\"a[data-discover], form[data-discover]\").forEach(registerElement);\n      let paths = Array.from(nextPaths.keys()).filter((path) => {\n        if (discoveredPaths.has(path)) {\n          nextPaths.delete(path);\n          return false;\n        }\n        return true;\n      });\n      if (paths.length === 0) {\n        return;\n      }\n      try {\n        await fetchAndApplyManifestPatches(\n          paths,\n          createFromReadableStream,\n          fetchImplementation\n        );\n      } catch (e) {\n        console.error(\"Failed to fetch manifest patches\", e);\n      }\n    }\n    let debouncedFetchPatches = debounce(fetchPatches, 100);\n    fetchPatches();\n    let observer = new MutationObserver(() => debouncedFetchPatches());\n    observer.observe(document.documentElement, {\n      subtree: true,\n      childList: true,\n      attributes: true,\n      attributeFilter: [\"data-discover\", \"href\", \"action\"]\n    });\n  }, [routeDiscovery, createFromReadableStream, fetchImplementation]);\n  const frameworkContext = {\n    future: {\n      // These flags have no runtime impact so can always be false.  If we add\n      // flags that drive runtime behavior they'll need to be proxied through.\n      unstable_middleware: false,\n      unstable_subResourceIntegrity: false\n    },\n    isSpaMode: true,\n    ssr: true,\n    criticalCss: \"\",\n    manifest: {\n      routes: {},\n      version: \"1\",\n      url: \"\",\n      entry: {\n        module: \"\",\n        imports: []\n      }\n    },\n    routeDiscovery: { mode: \"lazy\", manifestPath: \"/__manifest\" },\n    routeModules: {}\n  };\n  return /* @__PURE__ */ React4.createElement(RSCRouterContext.Provider, { value: true }, /* @__PURE__ */ React4.createElement(RSCRouterGlobalErrorBoundary, { location: location2 }, /* @__PURE__ */ React4.createElement(FrameworkContext.Provider, { value: frameworkContext }, /* @__PURE__ */ React4.createElement(RouterProvider, { router, flushSync: ReactDOM.flushSync }))));\n}\nfunction createRouteFromServerManifest(match, payload) {\n  let hasInitialData = payload && match.id in payload.loaderData;\n  let initialData = payload?.loaderData[match.id];\n  let hasInitialError = payload?.errors && match.id in payload.errors;\n  let initialError = payload?.errors?.[match.id];\n  let isHydrationRequest = match.clientLoader?.hydrate === true || !match.hasLoader || // If the route has a component but we don't have an element, we need to hit\n  // the server loader flow regardless of whether the client loader calls\n  // `serverLoader` or not, otherwise we'll have nothing to render.\n  match.hasComponent && !match.element;\n  let dataRoute = {\n    id: match.id,\n    element: match.element,\n    errorElement: match.errorElement,\n    handle: match.handle,\n    hasErrorBoundary: match.hasErrorBoundary,\n    hydrateFallbackElement: match.hydrateFallbackElement,\n    index: match.index,\n    loader: match.clientLoader ? async (args, singleFetch) => {\n      try {\n        let result = await match.clientLoader({\n          ...args,\n          serverLoader: () => {\n            preventInvalidServerHandlerCall(\n              \"loader\",\n              match.id,\n              match.hasLoader\n            );\n            if (isHydrationRequest) {\n              if (hasInitialData) {\n                return initialData;\n              }\n              if (hasInitialError) {\n                throw initialError;\n              }\n            }\n            return callSingleFetch(singleFetch);\n          }\n        });\n        return result;\n      } finally {\n        isHydrationRequest = false;\n      }\n    } : (\n      // We always make the call in this RSC world since even if we don't\n      // have a `loader` we may need to get the `element` implementation\n      (_, singleFetch) => callSingleFetch(singleFetch)\n    ),\n    action: match.clientAction ? (args, singleFetch) => match.clientAction({\n      ...args,\n      serverAction: async () => {\n        preventInvalidServerHandlerCall(\n          \"action\",\n          match.id,\n          match.hasLoader\n        );\n        return await callSingleFetch(singleFetch);\n      }\n    }) : match.hasAction ? (_, singleFetch) => callSingleFetch(singleFetch) : () => {\n      throw noActionDefinedError(\"action\", match.id);\n    },\n    path: match.path,\n    shouldRevalidate: match.shouldRevalidate,\n    // We always have a \"loader\" in this RSC world since even if we don't\n    // have a `loader` we may need to get the `element` implementation\n    hasLoader: true,\n    hasClientLoader: match.clientLoader != null,\n    hasAction: match.hasAction,\n    hasClientAction: match.clientAction != null,\n    hasShouldRevalidate: match.shouldRevalidate != null\n  };\n  if (typeof dataRoute.loader === \"function\") {\n    dataRoute.loader.hydrate = shouldHydrateRouteLoader(\n      match.id,\n      match.clientLoader,\n      match.hasLoader,\n      false\n    );\n  }\n  return dataRoute;\n}\nfunction callSingleFetch(singleFetch) {\n  invariant(typeof singleFetch === \"function\", \"Invalid singleFetch parameter\");\n  return singleFetch();\n}\nfunction preventInvalidServerHandlerCall(type, routeId, hasHandler) {\n  if (!hasHandler) {\n    let fn = type === \"action\" ? \"serverAction()\" : \"serverLoader()\";\n    let msg = `You are trying to call ${fn} on a route that does not have a server ${type} (routeId: \"${routeId}\")`;\n    console.error(msg);\n    throw new ErrorResponseImpl(400, \"Bad Request\", new Error(msg), true);\n  }\n}\nvar nextPaths = /* @__PURE__ */ new Set();\nvar discoveredPathsMaxSize = 1e3;\nvar discoveredPaths = /* @__PURE__ */ new Set();\nvar URL_LIMIT = 7680;\nfunction getManifestUrl(paths) {\n  if (paths.length === 0) {\n    return null;\n  }\n  if (paths.length === 1) {\n    return new URL(`${paths[0]}.manifest`, window.location.origin);\n  }\n  const globalVar = window;\n  let basename = (globalVar.__router.basename ?? \"\").replace(/^\\/|\\/$/g, \"\");\n  let url = new URL(`${basename}/.manifest`, window.location.origin);\n  paths.sort().forEach((path) => url.searchParams.append(\"p\", path));\n  return url;\n}\nasync function fetchAndApplyManifestPatches(paths, createFromReadableStream, fetchImplementation, signal) {\n  let url = getManifestUrl(paths);\n  if (url == null) {\n    return;\n  }\n  if (url.toString().length > URL_LIMIT) {\n    nextPaths.clear();\n    return;\n  }\n  let response = await fetchImplementation(new Request(url, { signal }));\n  if (!response.body || response.status < 200 || response.status >= 300) {\n    throw new Error(\"Unable to fetch new route matches from the server\");\n  }\n  let payload = await createFromReadableStream(response.body, {\n    temporaryReferences: void 0\n  });\n  if (payload.type !== \"manifest\") {\n    throw new Error(\"Failed to patch routes\");\n  }\n  paths.forEach((p) => addToFifoQueue(p, discoveredPaths));\n  payload.patches.forEach((p) => {\n    window.__router.patchRoutes(\n      p.parentId ?? null,\n      [createRouteFromServerManifest(p)]\n    );\n  });\n}\nfunction addToFifoQueue(path, queue) {\n  if (queue.size >= discoveredPathsMaxSize) {\n    let first = queue.values().next().value;\n    queue.delete(first);\n  }\n  queue.add(path);\n}\nfunction debounce(callback, wait) {\n  let timeoutId;\n  return (...args) => {\n    window.clearTimeout(timeoutId);\n    timeoutId = window.setTimeout(() => callback(...args), wait);\n  };\n}\n\n// lib/rsc/server.ssr.tsx\nimport * as React5 from \"react\";\n\n// lib/rsc/html-stream/server.ts\nvar encoder2 = new TextEncoder();\nvar trailer = \"</body></html>\";\nfunction injectRSCPayload(rscStream) {\n  let decoder = new TextDecoder();\n  let resolveFlightDataPromise;\n  let flightDataPromise = new Promise(\n    (resolve) => resolveFlightDataPromise = resolve\n  );\n  let startedRSC = false;\n  let buffered = [];\n  let timeout = null;\n  function flushBufferedChunks(controller) {\n    for (let chunk of buffered) {\n      let buf = decoder.decode(chunk, { stream: true });\n      if (buf.endsWith(trailer)) {\n        buf = buf.slice(0, -trailer.length);\n      }\n      controller.enqueue(encoder2.encode(buf));\n    }\n    buffered.length = 0;\n    timeout = null;\n  }\n  return new TransformStream({\n    transform(chunk, controller) {\n      buffered.push(chunk);\n      if (timeout) {\n        return;\n      }\n      timeout = setTimeout(async () => {\n        flushBufferedChunks(controller);\n        if (!startedRSC) {\n          startedRSC = true;\n          writeRSCStream(rscStream, controller).catch((err) => controller.error(err)).then(resolveFlightDataPromise);\n        }\n      }, 0);\n    },\n    async flush(controller) {\n      await flightDataPromise;\n      if (timeout) {\n        clearTimeout(timeout);\n        flushBufferedChunks(controller);\n      }\n      controller.enqueue(encoder2.encode(\"</body></html>\"));\n    }\n  });\n}\nasync function writeRSCStream(rscStream, controller) {\n  let decoder = new TextDecoder(\"utf-8\", { fatal: true });\n  const reader = rscStream.getReader();\n  try {\n    let read;\n    while ((read = await reader.read()) && !read.done) {\n      const chunk = read.value;\n      try {\n        writeChunk(\n          JSON.stringify(decoder.decode(chunk, { stream: true })),\n          controller\n        );\n      } catch (err) {\n        let base64 = JSON.stringify(btoa(String.fromCodePoint(...chunk)));\n        writeChunk(\n          `Uint8Array.from(atob(${base64}), m => m.codePointAt(0))`,\n          controller\n        );\n      }\n    }\n  } finally {\n    reader.releaseLock();\n  }\n  let remaining = decoder.decode();\n  if (remaining.length) {\n    writeChunk(JSON.stringify(remaining), controller);\n  }\n}\nfunction writeChunk(chunk, controller) {\n  controller.enqueue(\n    encoder2.encode(\n      `<script>${escapeScript(\n        `(self.__FLIGHT_DATA||=[]).push(${chunk})`\n      )}</script>`\n    )\n  );\n}\nfunction escapeScript(script) {\n  return script.replace(/<!--/g, \"<\\\\!--\").replace(/<\\/(script)/gi, \"</\\\\$1\");\n}\n\n// lib/rsc/server.ssr.tsx\nasync function routeRSCServerRequest({\n  request,\n  fetchServer,\n  createFromReadableStream,\n  renderHTML,\n  hydrate = true\n}) {\n  const url = new URL(request.url);\n  const isDataRequest = isReactServerRequest(url);\n  const respondWithRSCPayload = isDataRequest || isManifestRequest(url) || request.headers.has(\"rsc-action-id\");\n  const serverResponse = await fetchServer(request);\n  if (respondWithRSCPayload || serverResponse.headers.get(\"React-Router-Resource\") === \"true\") {\n    return serverResponse;\n  }\n  if (!serverResponse.body) {\n    throw new Error(\"Missing body in server response\");\n  }\n  let serverResponseB = null;\n  if (hydrate) {\n    serverResponseB = serverResponse.clone();\n  }\n  const body = serverResponse.body;\n  let payloadPromise;\n  const getPayload = async () => {\n    if (payloadPromise) return payloadPromise;\n    payloadPromise = createFromReadableStream(body);\n    return payloadPromise;\n  };\n  try {\n    const html = await renderHTML(getPayload);\n    const headers = new Headers(serverResponse.headers);\n    headers.set(\"Content-Type\", \"text/html\");\n    if (!hydrate) {\n      return new Response(html, {\n        status: serverResponse.status,\n        headers\n      });\n    }\n    if (!serverResponseB?.body) {\n      throw new Error(\"Failed to clone server response\");\n    }\n    const body2 = html.pipeThrough(injectRSCPayload(serverResponseB.body));\n    return new Response(body2, {\n      status: serverResponse.status,\n      headers\n    });\n  } catch (reason) {\n    if (reason instanceof Response) {\n      return reason;\n    }\n    throw reason;\n  }\n}\nfunction RSCStaticRouter({ getPayload }) {\n  const payload = React5.use(getPayload());\n  if (payload.type === \"redirect\") {\n    throw new Response(null, {\n      status: payload.status,\n      headers: {\n        Location: payload.location\n      }\n    });\n  }\n  if (payload.type !== \"render\") return null;\n  let patchedLoaderData = { ...payload.loaderData };\n  for (const match of payload.matches) {\n    if (shouldHydrateRouteLoader(\n      match.id,\n      match.clientLoader,\n      match.hasLoader,\n      false\n    ) && (match.hydrateFallbackElement || !match.hasLoader)) {\n      delete patchedLoaderData[match.id];\n    }\n  }\n  const context = {\n    actionData: payload.actionData,\n    actionHeaders: {},\n    basename: payload.basename,\n    errors: payload.errors,\n    loaderData: patchedLoaderData,\n    loaderHeaders: {},\n    location: payload.location,\n    statusCode: 200,\n    matches: payload.matches.map((match) => ({\n      params: match.params,\n      pathname: match.pathname,\n      pathnameBase: match.pathnameBase,\n      route: {\n        id: match.id,\n        action: match.hasAction || !!match.clientAction,\n        handle: match.handle,\n        hasErrorBoundary: match.hasErrorBoundary,\n        loader: match.hasLoader || !!match.clientLoader,\n        index: match.index,\n        path: match.path,\n        shouldRevalidate: match.shouldRevalidate\n      }\n    }))\n  };\n  const router = createStaticRouter(\n    payload.matches.reduceRight((previous, match) => {\n      const route = {\n        id: match.id,\n        action: match.hasAction || !!match.clientAction,\n        element: match.element,\n        errorElement: match.errorElement,\n        handle: match.handle,\n        hasErrorBoundary: !!match.errorElement,\n        hydrateFallbackElement: match.hydrateFallbackElement,\n        index: match.index,\n        loader: match.hasLoader || !!match.clientLoader,\n        path: match.path,\n        shouldRevalidate: match.shouldRevalidate\n      };\n      if (previous.length > 0) {\n        route.children = previous;\n      }\n      return [route];\n    }, []),\n    context\n  );\n  const frameworkContext = {\n    future: {\n      // These flags have no runtime impact so can always be false.  If we add\n      // flags that drive runtime behavior they'll need to be proxied through.\n      unstable_middleware: false,\n      unstable_subResourceIntegrity: false\n    },\n    isSpaMode: false,\n    ssr: true,\n    criticalCss: \"\",\n    manifest: {\n      routes: {},\n      version: \"1\",\n      url: \"\",\n      entry: {\n        module: \"\",\n        imports: []\n      }\n    },\n    routeDiscovery: { mode: \"lazy\", manifestPath: \"/__manifest\" },\n    routeModules: {}\n  };\n  return /* @__PURE__ */ React5.createElement(RSCRouterContext.Provider, { value: true }, /* @__PURE__ */ React5.createElement(RSCRouterGlobalErrorBoundary, { location: payload.location }, /* @__PURE__ */ React5.createElement(FrameworkContext.Provider, { value: frameworkContext }, /* @__PURE__ */ React5.createElement(\n    StaticRouterProvider,\n    {\n      context,\n      router,\n      hydrate: false,\n      nonce: payload.nonce\n    }\n  ))));\n}\nfunction isReactServerRequest(url) {\n  return url.pathname.endsWith(\".rsc\");\n}\nfunction isManifestRequest(url) {\n  return url.pathname.endsWith(\".manifest\");\n}\n\n// lib/rsc/html-stream/browser.ts\nfunction getRSCStream() {\n  let encoder3 = new TextEncoder();\n  let streamController = null;\n  let rscStream = new ReadableStream({\n    start(controller) {\n      if (typeof window === \"undefined\") {\n        return;\n      }\n      let handleChunk = (chunk) => {\n        if (typeof chunk === \"string\") {\n          controller.enqueue(encoder3.encode(chunk));\n        } else {\n          controller.enqueue(chunk);\n        }\n      };\n      window.__FLIGHT_DATA || (window.__FLIGHT_DATA = []);\n      window.__FLIGHT_DATA.forEach(handleChunk);\n      window.__FLIGHT_DATA.push = (chunk) => {\n        handleChunk(chunk);\n        return 0;\n      };\n      streamController = controller;\n    }\n  });\n  if (typeof document !== \"undefined\" && document.readyState === \"loading\") {\n    document.addEventListener(\"DOMContentLoaded\", () => {\n      streamController?.close();\n    });\n  } else {\n    streamController?.close();\n  }\n  return rscStream;\n}\n\n// lib/dom/ssr/errors.ts\nfunction deserializeErrors(errors) {\n  if (!errors) return null;\n  let entries = Object.entries(errors);\n  let serialized = {};\n  for (let [key, val] of entries) {\n    if (val && val.__type === \"RouteErrorResponse\") {\n      serialized[key] = new ErrorResponseImpl(\n        val.status,\n        val.statusText,\n        val.data,\n        val.internal === true\n      );\n    } else if (val && val.__type === \"Error\") {\n      if (val.__subType) {\n        let ErrorConstructor = window[val.__subType];\n        if (typeof ErrorConstructor === \"function\") {\n          try {\n            let error = new ErrorConstructor(val.message);\n            error.stack = val.stack;\n            serialized[key] = error;\n          } catch (e) {\n          }\n        }\n      }\n      if (serialized[key] == null) {\n        let error = new Error(val.message);\n        error.stack = val.stack;\n        serialized[key] = error;\n      }\n    } else {\n      serialized[key] = val;\n    }\n  }\n  return serialized;\n}\n\nexport {\n  ServerRouter,\n  createRoutesStub,\n  createCookie,\n  isCookie,\n  ServerMode,\n  setDevServerHooks,\n  createRequestHandler,\n  createSession,\n  isSession,\n  createSessionStorage,\n  createCookieSessionStorage,\n  createMemorySessionStorage,\n  href,\n  getHydrationData,\n  RSCDefaultRootErrorBoundary,\n  createCallServer,\n  RSCHydratedRouter,\n  routeRSCServerRequest,\n  RSCStaticRouter,\n  getRSCStream,\n  deserializeErrors\n};\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SACEA,mBAAmB,EACnBC,iBAAiB,EACjBC,gBAAgB,EAChBC,oBAAoB,EACpBC,MAAM,EACNC,gBAAgB,EAChBC,kBAAkB,EAClBC,cAAc,EACdC,4BAA4B,EAC5BC,yBAAyB,EACzBC,oBAAoB,EACpBC,cAAc,EACdC,yBAAyB,EACzBC,oBAAoB,EACpBC,kBAAkB,EAClBC,iBAAiB,EACjBC,YAAY,EACZC,kBAAkB,EAClBC,mBAAmB,EACnBC,kBAAkB,EAClBC,oBAAoB,EACpBC,MAAM,EACNC,eAAe,EACfC,8BAA8B,EAC9BC,yBAAyB,EACzBC,SAAS,EACTC,sBAAsB,EACtBC,gBAAgB,EAChBC,kBAAkB,EAClBC,oBAAoB,EACpBC,UAAU,EACVC,oBAAoB,EACpBC,WAAW,EACXC,oBAAoB,EACpBC,QAAQ,EACRC,gBAAgB,EAChBC,OAAO,EACPC,wBAAwB,EACxBC,cAAc,EACdC,aAAa,EACbC,eAAe,EACfC,8BAA8B,EAC9BC,sBAAsB,EACtBC,aAAa,EACbC,QAAQ,EACRC,kBAAkB,EAClBC,sBAAsB,EACtBC,wBAAwB,QACnB,sBAAsB;;AAE7B;AACA,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,YAAYA,CAAC;EACpBC,OAAO;EACPC,GAAG;EACHC;AACF,CAAC,EAAE;EACD,IAAI,OAAOD,GAAG,KAAK,QAAQ,EAAE;IAC3BA,GAAG,GAAG,IAAIE,GAAG,CAACF,GAAG,CAAC;EACpB;EACA,IAAI;IAAEG,QAAQ;IAAEC,YAAY;IAAEC,WAAW;IAAEC;EAAoB,CAAC,GAAGP,OAAO;EAC1E,IAAIQ,MAAM,GAAGzC,kBAAkB,CAC7BqC,QAAQ,CAACI,MAAM,EACfH,YAAY,EACZL,OAAO,CAACS,MAAM,EACdT,OAAO,CAACU,SACV,CAAC;EACDV,OAAO,CAACW,oBAAoB,CAACC,UAAU,GAAG;IACxC,GAAGZ,OAAO,CAACW,oBAAoB,CAACC;EAClC,CAAC;EACD,KAAK,IAAIC,KAAK,IAAIb,OAAO,CAACW,oBAAoB,CAACG,OAAO,EAAE;IACtD,IAAIC,OAAO,GAAGF,KAAK,CAACG,KAAK,CAACC,EAAE;IAC5B,IAAID,KAAK,GAAGX,YAAY,CAACU,OAAO,CAAC;IACjC,IAAIG,aAAa,GAAGlB,OAAO,CAACI,QAAQ,CAACI,MAAM,CAACO,OAAO,CAAC;IACpD,IAAIC,KAAK,IAAIE,aAAa,IAAI/B,wBAAwB,CACpD4B,OAAO,EACPC,KAAK,CAACG,YAAY,EAClBD,aAAa,CAACE,SAAS,EACvBpB,OAAO,CAACU,SACV,CAAC,KAAKM,KAAK,CAACK,eAAe,IAAI,CAACH,aAAa,CAACE,SAAS,CAAC,EAAE;MACxD,OAAOpB,OAAO,CAACW,oBAAoB,CAACC,UAAU,CAACG,OAAO,CAAC;IACzD;EACF;EACA,IAAIO,MAAM,GAAGrD,kBAAkB,CAACuC,MAAM,EAAER,OAAO,CAACW,oBAAoB,CAAC;EACrE,OAAO,eAAgBb,KAAK,CAACyB,aAAa,CAACzB,KAAK,CAAC0B,QAAQ,EAAE,IAAI,EAAE,eAAgB1B,KAAK,CAACyB,aAAa,CAClGvE,gBAAgB,CAACyE,QAAQ,EACzB;IACEC,KAAK,EAAE;MACLtB,QAAQ;MACRC,YAAY;MACZC,WAAW;MACXC,mBAAmB;MACnBE,MAAM,EAAET,OAAO,CAACS,MAAM;MACtBkB,GAAG,EAAE3B,OAAO,CAAC2B,GAAG;MAChBjB,SAAS,EAAEV,OAAO,CAACU,SAAS;MAC5BkB,cAAc,EAAE5B,OAAO,CAAC4B,cAAc;MACtCC,cAAc,EAAE7B,OAAO,CAAC6B,cAAc;MACtCC,UAAU,EAAE9B,OAAO,CAAC8B;IACtB;EACF,CAAC,EACD,eAAgBhC,KAAK,CAACyB,aAAa,CAACnE,kBAAkB,EAAE;IAAE2E,QAAQ,EAAET,MAAM,CAACU,KAAK,CAACD;EAAS,CAAC,EAAE,eAAgBjC,KAAK,CAACyB,aAAa,CAC9H/D,oBAAoB,EACpB;IACE8D,MAAM;IACNtB,OAAO,EAAEA,OAAO,CAACW,oBAAoB;IACrCsB,OAAO,EAAE;EACX,CACF,CAAC,CACH,CAAC,EAAEjC,OAAO,CAACkC,mBAAmB,GAAG,eAAgBpC,KAAK,CAACyB,aAAa,CAACzB,KAAK,CAACqC,QAAQ,EAAE,IAAI,EAAE,eAAgBrC,KAAK,CAACyB,aAAa,CAC5H9D,cAAc,EACd;IACEuC,OAAO;IACPoC,UAAU,EAAE,CAAC;IACbC,MAAM,EAAErC,OAAO,CAACkC,mBAAmB,CAACI,SAAS,CAAC,CAAC;IAC/CC,WAAW,EAAE,IAAIC,WAAW,CAAC,CAAC;IAC9BtC;EACF,CACF,CAAC,CAAC,GAAG,IAAI,CAAC;AACZ;;AAEA;AACA,OAAO,KAAKuC,MAAM,MAAM,OAAO;AAC/B,SAASC,gBAAgBA,CAAClC,MAAM,EAAEmC,QAAQ,EAAE;EAC1C,OAAO,SAASC,cAAcA,CAAC;IAC7BC,cAAc;IACdC,YAAY;IACZC,aAAa;IACbtC;EACF,CAAC,EAAE;IACD,IAAIuC,SAAS,GAAGP,MAAM,CAACQ,MAAM,CAAC,CAAC;IAC/B,IAAIC,mBAAmB,GAAGT,MAAM,CAACQ,MAAM,CAAC,CAAC;IACzC,IAAID,SAAS,CAACG,OAAO,IAAI,IAAI,EAAE;MAC7BD,mBAAmB,CAACC,OAAO,GAAG;QAC5B1C,MAAM,EAAE;UACN2C,6BAA6B,EAAE3C,MAAM,EAAE2C,6BAA6B,KAAK,IAAI;UAC7EC,mBAAmB,EAAE5C,MAAM,EAAE4C,mBAAmB,KAAK;QACvD,CAAC;QACDjD,QAAQ,EAAE;UACRI,MAAM,EAAE,CAAC,CAAC;UACV8C,KAAK,EAAE;YAAEC,OAAO,EAAE,EAAE;YAAEC,MAAM,EAAE;UAAG,CAAC;UAClCvD,GAAG,EAAE,EAAE;UACPwD,OAAO,EAAE;QACX,CAAC;QACDpD,YAAY,EAAE,CAAC,CAAC;QAChBsB,GAAG,EAAE,KAAK;QACVjB,SAAS,EAAE,KAAK;QAChBkB,cAAc,EAAE;UAAE8B,IAAI,EAAE,MAAM;UAAEC,YAAY,EAAE;QAAc;MAC9D,CAAC;MACD,IAAIC,OAAO,GAAGC,aAAa;MACzB;MACA;MACAnG,yBAAyB,CAAC8C,MAAM,EAAGsD,CAAC,IAAKA,CAAC,CAAC,EAC3CnB,QAAQ,KAAK,KAAK,CAAC,GAAGA,QAAQ,GAAGlC,MAAM,EAAE4C,mBAAmB,GAAG,IAAI9D,8BAA8B,CAAC,CAAC,GAAG,CAAC,CAAC,EACxG2D,mBAAmB,CAACC,OAAO,CAAC/C,QAAQ,EACpC8C,mBAAmB,CAACC,OAAO,CAAC9C,YAC9B,CAAC;MACD2C,SAAS,CAACG,OAAO,GAAGvF,kBAAkB,CAACgG,OAAO,EAAE;QAC9Cf,cAAc;QACdC,YAAY;QACZC;MACF,CAAC,CAAC;IACJ;IACA,OAAO,eAAgBN,MAAM,CAAClB,aAAa,CAACvE,gBAAgB,CAACyE,QAAQ,EAAE;MAAEC,KAAK,EAAEwB,mBAAmB,CAACC;IAAQ,CAAC,EAAE,eAAgBV,MAAM,CAAClB,aAAa,CAAClE,cAAc,EAAE;MAAEiE,MAAM,EAAE0B,SAAS,CAACG;IAAQ,CAAC,CAAC,CAAC;EACrM,CAAC;AACH;AACA,SAASU,aAAaA,CAACrD,MAAM,EAAER,OAAO,EAAEI,QAAQ,EAAEC,YAAY,EAAE0D,QAAQ,EAAE;EACxE,OAAOvD,MAAM,CAACwD,GAAG,CAAEhD,KAAK,IAAK;IAC3B,IAAI,CAACA,KAAK,CAACC,EAAE,EAAE;MACb,MAAM,IAAIgD,KAAK,CACb,8DACF,CAAC;IACH;IACA,IAAIC,QAAQ,GAAG;MACbjD,EAAE,EAAED,KAAK,CAACC,EAAE;MACZkD,IAAI,EAAEnD,KAAK,CAACmD,IAAI;MAChBC,KAAK,EAAEpD,KAAK,CAACoD,KAAK;MAClBC,SAAS,EAAErD,KAAK,CAACqD,SAAS,GAAG1E,kBAAkB,CAACqB,KAAK,CAACqD,SAAS,CAAC,GAAG,KAAK,CAAC;MACzEhD,eAAe,EAAEL,KAAK,CAACK,eAAe,GAAGxB,wBAAwB,CAACmB,KAAK,CAACK,eAAe,CAAC,GAAG,KAAK,CAAC;MACjGiD,aAAa,EAAEtD,KAAK,CAACsD,aAAa,GAAG1E,sBAAsB,CAACoB,KAAK,CAACsD,aAAa,CAAC,GAAG,KAAK,CAAC;MACzFC,MAAM,EAAEvD,KAAK,CAACuD,MAAM,GAAIC,IAAI,IAAKxD,KAAK,CAACuD,MAAM,CAAC;QAAE,GAAGC,IAAI;QAAExE;MAAQ,CAAC,CAAC,GAAG,KAAK,CAAC;MAC5EyE,MAAM,EAAEzD,KAAK,CAACyD,MAAM,GAAID,IAAI,IAAKxD,KAAK,CAACyD,MAAM,CAAC;QAAE,GAAGD,IAAI;QAAExE;MAAQ,CAAC,CAAC,GAAG,KAAK,CAAC;MAC5E0E,MAAM,EAAE1D,KAAK,CAAC0D,MAAM;MACpBC,gBAAgB,EAAE3D,KAAK,CAAC2D;IAC1B,CAAC;IACD,IAAIC,UAAU,GAAG;MACf3D,EAAE,EAAED,KAAK,CAACC,EAAE;MACZkD,IAAI,EAAEnD,KAAK,CAACmD,IAAI;MAChBC,KAAK,EAAEpD,KAAK,CAACoD,KAAK;MAClBL,QAAQ;MACRc,SAAS,EAAE7D,KAAK,CAACuD,MAAM,IAAI,IAAI;MAC/BnD,SAAS,EAAEJ,KAAK,CAACyD,MAAM,IAAI,IAAI;MAC/B;MACA;MACA;MACAK,eAAe,EAAE,KAAK;MACtBC,eAAe,EAAE,KAAK;MACtBC,mBAAmB,EAAE,KAAK;MAC1BC,gBAAgB,EAAEjE,KAAK,CAACsD,aAAa,IAAI,IAAI;MAC7C;MACAd,MAAM,EAAE,8BAA8B;MACtC0B,kBAAkB,EAAE,KAAK,CAAC;MAC1BC,kBAAkB,EAAE,KAAK,CAAC;MAC1BC,sBAAsB,EAAE,KAAK,CAAC;MAC9BC,qBAAqB,EAAE,KAAK;IAC9B,CAAC;IACDjF,QAAQ,CAACI,MAAM,CAAC0D,QAAQ,CAACjD,EAAE,CAAC,GAAG2D,UAAU;IACzCvE,YAAY,CAACW,KAAK,CAACC,EAAE,CAAC,GAAG;MACvBqE,OAAO,EAAEpB,QAAQ,CAACG,SAAS,IAAInH,MAAM;MACrCoH,aAAa,EAAEJ,QAAQ,CAACI,aAAa,IAAI,KAAK,CAAC;MAC/CI,MAAM,EAAE1D,KAAK,CAAC0D,MAAM;MACpBa,KAAK,EAAEvE,KAAK,CAACuE,KAAK;MAClBC,IAAI,EAAExE,KAAK,CAACwE,IAAI;MAChBb,gBAAgB,EAAE3D,KAAK,CAAC2D;IAC1B,CAAC;IACD,IAAI3D,KAAK,CAACyE,QAAQ,EAAE;MAClBvB,QAAQ,CAACuB,QAAQ,GAAG5B,aAAa,CAC/B7C,KAAK,CAACyE,QAAQ,EACdzF,OAAO,EACPI,QAAQ,EACRC,YAAY,EACZ6D,QAAQ,CAACjD,EACX,CAAC;IACH;IACA,OAAOiD,QAAQ;EACjB,CAAC,CAAC;AACJ;;AAEA;AACA,SAASwB,KAAK,EAAEC,SAAS,QAAQ,QAAQ;;AAEzC;AACA,IAAIC,OAAO,GAAG,eAAgB,IAAIC,WAAW,CAAC,CAAC;AAC/C,IAAIC,IAAI,GAAG,MAAAA,CAAOpE,KAAK,EAAEqE,MAAM,KAAK;EAClC,IAAIC,KAAK,GAAGJ,OAAO,CAACzH,MAAM,CAACuD,KAAK,CAAC;EACjC,IAAIuE,GAAG,GAAG,MAAMC,SAAS,CAACH,MAAM,EAAE,CAAC,MAAM,CAAC,CAAC;EAC3C,IAAII,SAAS,GAAG,MAAMC,MAAM,CAACC,MAAM,CAACP,IAAI,CAAC,MAAM,EAAEG,GAAG,EAAED,KAAK,CAAC;EAC5D,IAAIM,IAAI,GAAGC,IAAI,CAACC,MAAM,CAACC,YAAY,CAAC,GAAG,IAAIC,UAAU,CAACP,SAAS,CAAC,CAAC,CAAC,CAACjH,OAAO,CACxE,KAAK,EACL,EACF,CAAC;EACD,OAAOwC,KAAK,GAAG,GAAG,GAAG4E,IAAI;AAC3B,CAAC;AACD,IAAIK,MAAM,GAAG,MAAAA,CAAOC,MAAM,EAAEb,MAAM,KAAK;EACrC,IAAI3B,KAAK,GAAGwC,MAAM,CAACC,WAAW,CAAC,GAAG,CAAC;EACnC,IAAInF,KAAK,GAAGkF,MAAM,CAACE,KAAK,CAAC,CAAC,EAAE1C,KAAK,CAAC;EAClC,IAAIkC,IAAI,GAAGM,MAAM,CAACE,KAAK,CAAC1C,KAAK,GAAG,CAAC,CAAC;EAClC,IAAI4B,KAAK,GAAGJ,OAAO,CAACzH,MAAM,CAACuD,KAAK,CAAC;EACjC,IAAIuE,GAAG,GAAG,MAAMC,SAAS,CAACH,MAAM,EAAE,CAAC,QAAQ,CAAC,CAAC;EAC7C,IAAI;IACF,IAAII,SAAS,GAAGY,sBAAsB,CAACC,IAAI,CAACV,IAAI,CAAC,CAAC;IAClD,IAAIW,KAAK,GAAG,MAAMb,MAAM,CAACC,MAAM,CAACa,MAAM,CAAC,MAAM,EAAEjB,GAAG,EAAEE,SAAS,EAAEH,KAAK,CAAC;IACrE,OAAOiB,KAAK,GAAGvF,KAAK,GAAG,KAAK;EAC9B,CAAC,CAAC,OAAOyF,KAAK,EAAE;IACd,OAAO,KAAK;EACd;AACF,CAAC;AACD,IAAIjB,SAAS,GAAG,MAAAA,CAAOH,MAAM,EAAEqB,MAAM,KAAKhB,MAAM,CAACC,MAAM,CAACgB,SAAS,CAC/D,KAAK,EACLzB,OAAO,CAACzH,MAAM,CAAC4H,MAAM,CAAC,EACtB;EAAEuB,IAAI,EAAE,MAAM;EAAEhB,IAAI,EAAE;AAAU,CAAC,EACjC,KAAK,EACLc,MACF,CAAC;AACD,SAASL,sBAAsBA,CAACQ,UAAU,EAAE;EAC1C,IAAIC,KAAK,GAAG,IAAId,UAAU,CAACa,UAAU,CAACE,MAAM,CAAC;EAC7C,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,UAAU,CAACE,MAAM,EAAEC,CAAC,EAAE,EAAE;IAC1CF,KAAK,CAACE,CAAC,CAAC,GAAGH,UAAU,CAACI,UAAU,CAACD,CAAC,CAAC;EACrC;EACA,OAAOF,KAAK;AACd;;AAEA;AACA,IAAII,YAAY,GAAGA,CAACN,IAAI,EAAEO,aAAa,GAAG,CAAC,CAAC,KAAK;EAC/C,IAAI;IAAEC,OAAO,GAAG,EAAE;IAAE,GAAGC;EAAQ,CAAC,GAAG;IACjC5D,IAAI,EAAE,GAAG;IACT6D,QAAQ,EAAE,KAAK;IACf,GAAGH;EACL,CAAC;EACDI,0BAA0B,CAACX,IAAI,EAAES,OAAO,CAACG,OAAO,CAAC;EACjD,OAAO;IACL,IAAIZ,IAAIA,CAAA,EAAG;MACT,OAAOA,IAAI;IACb,CAAC;IACD,IAAIa,QAAQA,CAAA,EAAG;MACb,OAAOL,OAAO,CAACL,MAAM,GAAG,CAAC;IAC3B,CAAC;IACD,IAAIS,OAAOA,CAAA,EAAG;MACZ,OAAO,OAAOH,OAAO,CAACK,MAAM,KAAK,WAAW,GAAG,IAAIC,IAAI,CAACA,IAAI,CAACC,GAAG,CAAC,CAAC,GAAGP,OAAO,CAACK,MAAM,GAAG,GAAG,CAAC,GAAGL,OAAO,CAACG,OAAO;IAC9G,CAAC;IACD,MAAMxC,KAAKA,CAAC6C,YAAY,EAAEC,YAAY,EAAE;MACtC,IAAI,CAACD,YAAY,EAAE,OAAO,IAAI;MAC9B,IAAIE,OAAO,GAAG/C,KAAK,CAAC6C,YAAY,EAAE;QAAE,GAAGR,OAAO;QAAE,GAAGS;MAAa,CAAC,CAAC;MAClE,IAAIlB,IAAI,IAAImB,OAAO,EAAE;QACnB,IAAI/G,KAAK,GAAG+G,OAAO,CAACnB,IAAI,CAAC;QACzB,IAAI,OAAO5F,KAAK,KAAK,QAAQ,IAAIA,KAAK,KAAK,EAAE,EAAE;UAC7C,IAAIgH,OAAO,GAAG,MAAMC,iBAAiB,CAACjH,KAAK,EAAEoG,OAAO,CAAC;UACrD,OAAOY,OAAO;QAChB,CAAC,MAAM;UACL,OAAO,EAAE;QACX;MACF,CAAC,MAAM;QACL,OAAO,IAAI;MACb;IACF,CAAC;IACD,MAAM/C,SAASA,CAACjE,KAAK,EAAEkH,gBAAgB,EAAE;MACvC,OAAOjD,SAAS,CACd2B,IAAI,EACJ5F,KAAK,KAAK,EAAE,GAAG,EAAE,GAAG,MAAMmH,iBAAiB,CAACnH,KAAK,EAAEoG,OAAO,CAAC,EAC3D;QACE,GAAGC,OAAO;QACV,GAAGa;MACL,CACF,CAAC;IACH;EACF,CAAC;AACH,CAAC;AACD,IAAIE,QAAQ,GAAIC,MAAM,IAAK;EACzB,OAAOA,MAAM,IAAI,IAAI,IAAI,OAAOA,MAAM,CAACzB,IAAI,KAAK,QAAQ,IAAI,OAAOyB,MAAM,CAACZ,QAAQ,KAAK,SAAS,IAAI,OAAOY,MAAM,CAACrD,KAAK,KAAK,UAAU,IAAI,OAAOqD,MAAM,CAACpD,SAAS,KAAK,UAAU;AAClL,CAAC;AACD,eAAekD,iBAAiBA,CAACnH,KAAK,EAAEoG,OAAO,EAAE;EAC/C,IAAIkB,OAAO,GAAGC,UAAU,CAACvH,KAAK,CAAC;EAC/B,IAAIoG,OAAO,CAACL,MAAM,GAAG,CAAC,EAAE;IACtBuB,OAAO,GAAG,MAAMlD,IAAI,CAACkD,OAAO,EAAElB,OAAO,CAAC,CAAC,CAAC,CAAC;EAC3C;EACA,OAAOkB,OAAO;AAChB;AACA,eAAeL,iBAAiBA,CAACjH,KAAK,EAAEoG,OAAO,EAAE;EAC/C,IAAIA,OAAO,CAACL,MAAM,GAAG,CAAC,EAAE;IACtB,KAAK,IAAI1B,MAAM,IAAI+B,OAAO,EAAE;MAC1B,IAAIoB,aAAa,GAAG,MAAMvC,MAAM,CAACjF,KAAK,EAAEqE,MAAM,CAAC;MAC/C,IAAImD,aAAa,KAAK,KAAK,EAAE;QAC3B,OAAOC,UAAU,CAACD,aAAa,CAAC;MAClC;IACF;IACA,OAAO,IAAI;EACb;EACA,OAAOC,UAAU,CAACzH,KAAK,CAAC;AAC1B;AACA,SAASuH,UAAUA,CAACvH,KAAK,EAAE;EACzB,OAAO6E,IAAI,CAAC6C,UAAU,CAACC,kBAAkB,CAACC,IAAI,CAACC,SAAS,CAAC7H,KAAK,CAAC,CAAC,CAAC,CAAC;AACpE;AACA,SAASyH,UAAUA,CAACzH,KAAK,EAAE;EACzB,IAAI;IACF,OAAO4H,IAAI,CAAC5D,KAAK,CAAC8D,kBAAkB,CAACC,QAAQ,CAACzC,IAAI,CAACtF,KAAK,CAAC,CAAC,CAAC,CAAC;EAC9D,CAAC,CAAC,OAAOyF,KAAK,EAAE;IACd,OAAO,CAAC,CAAC;EACX;AACF;AACA,SAASsC,QAAQA,CAAC/H,KAAK,EAAE;EACvB,IAAIgI,GAAG,GAAGhI,KAAK,CAACiI,QAAQ,CAAC,CAAC;EAC1B,IAAIC,MAAM,GAAG,EAAE;EACf,IAAIxF,KAAK,GAAG,CAAC;EACb,IAAIyF,GAAG,EAAEC,IAAI;EACb,OAAO1F,KAAK,GAAGsF,GAAG,CAACjC,MAAM,EAAE;IACzBoC,GAAG,GAAGH,GAAG,CAACK,MAAM,CAAC3F,KAAK,EAAE,CAAC;IACzB,IAAI,aAAa,CAAC4F,IAAI,CAACH,GAAG,CAAC,EAAE;MAC3BD,MAAM,IAAIC,GAAG;IACf,CAAC,MAAM;MACLC,IAAI,GAAGD,GAAG,CAAClC,UAAU,CAAC,CAAC,CAAC;MACxB,IAAImC,IAAI,GAAG,GAAG,EAAE;QACdF,MAAM,IAAI,GAAG,GAAGK,GAAG,CAACH,IAAI,EAAE,CAAC,CAAC;MAC9B,CAAC,MAAM;QACLF,MAAM,IAAI,IAAI,GAAGK,GAAG,CAACH,IAAI,EAAE,CAAC,CAAC,CAACI,WAAW,CAAC,CAAC;MAC7C;IACF;EACF;EACA,OAAON,MAAM;AACf;AACA,SAASK,GAAGA,CAACH,IAAI,EAAErC,MAAM,EAAE;EACzB,IAAImC,MAAM,GAAGE,IAAI,CAACH,QAAQ,CAAC,EAAE,CAAC;EAC9B,OAAOC,MAAM,CAACnC,MAAM,GAAGA,MAAM,EAAEmC,MAAM,GAAG,GAAG,GAAGA,MAAM;EACpD,OAAOA,MAAM;AACf;AACA,SAASR,UAAUA,CAAC1H,KAAK,EAAE;EACzB,IAAIgI,GAAG,GAAGhI,KAAK,CAACiI,QAAQ,CAAC,CAAC;EAC1B,IAAIC,MAAM,GAAG,EAAE;EACf,IAAIxF,KAAK,GAAG,CAAC;EACb,IAAIyF,GAAG,EAAEM,IAAI;EACb,OAAO/F,KAAK,GAAGsF,GAAG,CAACjC,MAAM,EAAE;IACzBoC,GAAG,GAAGH,GAAG,CAACK,MAAM,CAAC3F,KAAK,EAAE,CAAC;IACzB,IAAIyF,GAAG,KAAK,GAAG,EAAE;MACf,IAAIH,GAAG,CAACK,MAAM,CAAC3F,KAAK,CAAC,KAAK,GAAG,EAAE;QAC7B+F,IAAI,GAAGT,GAAG,CAAC5C,KAAK,CAAC1C,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAG,CAAC,CAAC;QACtC,IAAI,eAAe,CAAC4F,IAAI,CAACG,IAAI,CAAC,EAAE;UAC9BP,MAAM,IAAIpD,MAAM,CAACC,YAAY,CAAC2D,QAAQ,CAACD,IAAI,EAAE,EAAE,CAAC,CAAC;UACjD/F,KAAK,IAAI,CAAC;UACV;QACF;MACF,CAAC,MAAM;QACL+F,IAAI,GAAGT,GAAG,CAAC5C,KAAK,CAAC1C,KAAK,EAAEA,KAAK,GAAG,CAAC,CAAC;QAClC,IAAI,eAAe,CAAC4F,IAAI,CAACG,IAAI,CAAC,EAAE;UAC9BP,MAAM,IAAIpD,MAAM,CAACC,YAAY,CAAC2D,QAAQ,CAACD,IAAI,EAAE,EAAE,CAAC,CAAC;UACjD/F,KAAK,IAAI,CAAC;UACV;QACF;MACF;IACF;IACAwF,MAAM,IAAIC,GAAG;EACf;EACA,OAAOD,MAAM;AACf;AACA,SAAS3B,0BAA0BA,CAACX,IAAI,EAAEY,OAAO,EAAE;EACjDxI,QAAQ,CACN,CAACwI,OAAO,EACR,QAAQZ,IAAI,6WACd,CAAC;AACH;;AAEA;AACA,SAAS+C,uBAAuBA,CAACjK,QAAQ,EAAE;EACzC,OAAOkK,MAAM,CAACC,IAAI,CAACnK,QAAQ,CAAC,CAACoK,MAAM,CAAC,CAACC,IAAI,EAAE1J,OAAO,KAAK;IACrD,IAAIC,KAAK,GAAGZ,QAAQ,CAACW,OAAO,CAAC;IAC7B,IAAIC,KAAK,EAAE;MACTyJ,IAAI,CAAC1J,OAAO,CAAC,GAAGC,KAAK,CAACwC,MAAM;IAC9B;IACA,OAAOiH,IAAI;EACb,CAAC,EAAE,CAAC,CAAC,CAAC;AACR;;AAEA;AACA,IAAIC,UAAU,GAAG,eAAgB,CAAEC,WAAW,IAAK;EACjDA,WAAW,CAAC,aAAa,CAAC,GAAG,aAAa;EAC1CA,WAAW,CAAC,YAAY,CAAC,GAAG,YAAY;EACxCA,WAAW,CAAC,MAAM,CAAC,GAAG,MAAM;EAC5B,OAAOA,WAAW;AACpB,CAAC,EAAED,UAAU,IAAI,CAAC,CAAC,CAAC;AACpB,SAASE,YAAYA,CAAClJ,KAAK,EAAE;EAC3B,OAAOA,KAAK,KAAK,aAAa,CAAC,qBAAqBA,KAAK,KAAK,YAAY,CAAC,oBAAoBA,KAAK,KAAK,MAAM,CAAC;AAClH;;AAEA;AACA,SAASmJ,aAAaA,CAAC1D,KAAK,EAAE2D,UAAU,EAAE;EACxC,IAAI3D,KAAK,YAAYlD,KAAK,IAAI6G,UAAU,KAAK,aAAa,CAAC,mBAAmB;IAC5E,IAAIC,SAAS,GAAG,IAAI9G,KAAK,CAAC,yBAAyB,CAAC;IACpD8G,SAAS,CAACC,KAAK,GAAG,KAAK,CAAC;IACxB,OAAOD,SAAS;EAClB;EACA,OAAO5D,KAAK;AACd;AACA,SAAS8D,cAAcA,CAACC,MAAM,EAAEJ,UAAU,EAAE;EAC1C,OAAOR,MAAM,CAACa,OAAO,CAACD,MAAM,CAAC,CAACV,MAAM,CAAC,CAACY,GAAG,EAAE,CAACrK,OAAO,EAAEoG,KAAK,CAAC,KAAK;IAC9D,OAAOmD,MAAM,CAACe,MAAM,CAACD,GAAG,EAAE;MAAE,CAACrK,OAAO,GAAG8J,aAAa,CAAC1D,KAAK,EAAE2D,UAAU;IAAE,CAAC,CAAC;EAC5E,CAAC,EAAE,CAAC,CAAC,CAAC;AACR;AACA,SAASjJ,cAAcA,CAACsF,KAAK,EAAE2D,UAAU,EAAE;EACzC,IAAIC,SAAS,GAAGF,aAAa,CAAC1D,KAAK,EAAE2D,UAAU,CAAC;EAChD,OAAO;IACLQ,OAAO,EAAEP,SAAS,CAACO,OAAO;IAC1BN,KAAK,EAAED,SAAS,CAACC;EACnB,CAAC;AACH;AACA,SAASO,eAAeA,CAACL,MAAM,EAAEJ,UAAU,EAAE;EAC3C,IAAI,CAACI,MAAM,EAAE,OAAO,IAAI;EACxB,IAAIC,OAAO,GAAGb,MAAM,CAACa,OAAO,CAACD,MAAM,CAAC;EACpC,IAAIM,UAAU,GAAG,CAAC,CAAC;EACnB,KAAK,IAAI,CAACvF,GAAG,EAAEwF,GAAG,CAAC,IAAIN,OAAO,EAAE;IAC9B,IAAItM,oBAAoB,CAAC4M,GAAG,CAAC,EAAE;MAC7BD,UAAU,CAACvF,GAAG,CAAC,GAAG;QAAE,GAAGwF,GAAG;QAAEC,MAAM,EAAE;MAAqB,CAAC;IAC5D,CAAC,MAAM,IAAID,GAAG,YAAYxH,KAAK,EAAE;MAC/B,IAAI8G,SAAS,GAAGF,aAAa,CAACY,GAAG,EAAEX,UAAU,CAAC;MAC9CU,UAAU,CAACvF,GAAG,CAAC,GAAG;QAChBqF,OAAO,EAAEP,SAAS,CAACO,OAAO;QAC1BN,KAAK,EAAED,SAAS,CAACC,KAAK;QACtBU,MAAM,EAAE,OAAO;QACf;QACA;QACA;QACA;QACA,IAAGX,SAAS,CAACzD,IAAI,KAAK,OAAO,GAAG;UAC9BqE,SAAS,EAAEZ,SAAS,CAACzD;QACvB,CAAC,GAAG,CAAC,CAAC;MACR,CAAC;IACH,CAAC,MAAM;MACLkE,UAAU,CAACvF,GAAG,CAAC,GAAGwF,GAAG;IACvB;EACF;EACA,OAAOD,UAAU;AACnB;;AAEA;AACA,SAASI,iBAAiBA,CAACpL,MAAM,EAAEqL,QAAQ,EAAEC,QAAQ,EAAE;EACrD,IAAIhL,OAAO,GAAGhC,WAAW,CACvB0B,MAAM,EACNqL,QAAQ,EACRC,QACF,CAAC;EACD,IAAI,CAAChL,OAAO,EAAE,OAAO,IAAI;EACzB,OAAOA,OAAO,CAACkD,GAAG,CAAEnD,KAAK,KAAM;IAC7BkL,MAAM,EAAElL,KAAK,CAACkL,MAAM;IACpBF,QAAQ,EAAEhL,KAAK,CAACgL,QAAQ;IACxB7K,KAAK,EAAEH,KAAK,CAACG;EACf,CAAC,CAAC,CAAC;AACL;;AAEA;AACA,eAAegL,gBAAgBA,CAACC,OAAO,EAAEzH,IAAI,EAAE;EAC7C,IAAIoF,MAAM,GAAG,MAAMqC,OAAO,CAAC;IACzBC,OAAO,EAAEC,gBAAgB,CAACC,gBAAgB,CAAC5H,IAAI,CAAC0H,OAAO,CAAC,CAAC;IACzDH,MAAM,EAAEvH,IAAI,CAACuH,MAAM;IACnB/L,OAAO,EAAEwE,IAAI,CAACxE;EAChB,CAAC,CAAC;EACF,IAAIxB,sBAAsB,CAACoL,MAAM,CAAC,IAAIA,MAAM,CAACyC,IAAI,IAAIzC,MAAM,CAACyC,IAAI,CAACC,MAAM,IAAI3N,oBAAoB,CAACiL,MAAM,CAACyC,IAAI,CAACC,MAAM,CAAC,EAAE;IACnH,MAAM,IAAIC,QAAQ,CAAC,IAAI,EAAE3C,MAAM,CAACyC,IAAI,CAAC;EACvC;EACA,OAAOzC,MAAM;AACf;AACA,SAASwC,gBAAgBA,CAACF,OAAO,EAAE;EACjC,IAAIjM,GAAG,GAAG,IAAIE,GAAG,CAAC+L,OAAO,CAACjM,GAAG,CAAC;EAC9B,IAAIuM,WAAW,GAAGvM,GAAG,CAACwM,YAAY,CAACC,MAAM,CAAC,OAAO,CAAC;EAClDzM,GAAG,CAACwM,YAAY,CAACE,MAAM,CAAC,OAAO,CAAC;EAChC,IAAIC,iBAAiB,GAAG,EAAE;EAC1B,KAAK,IAAIC,UAAU,IAAIL,WAAW,EAAE;IAClC,IAAIK,UAAU,EAAE;MACdD,iBAAiB,CAACE,IAAI,CAACD,UAAU,CAAC;IACpC;EACF;EACA,KAAK,IAAIE,MAAM,IAAIH,iBAAiB,EAAE;IACpC3M,GAAG,CAACwM,YAAY,CAACO,MAAM,CAAC,OAAO,EAAED,MAAM,CAAC;EAC1C;EACA,IAAIV,IAAI,GAAG;IACTY,MAAM,EAAEf,OAAO,CAACe,MAAM;IACtBC,IAAI,EAAEhB,OAAO,CAACgB,IAAI;IAClBC,OAAO,EAAEjB,OAAO,CAACiB,OAAO;IACxBC,MAAM,EAAElB,OAAO,CAACkB;EAClB,CAAC;EACD,IAAIf,IAAI,CAACa,IAAI,EAAE;IACbb,IAAI,CAACgB,MAAM,GAAG,MAAM;EACtB;EACA,OAAO,IAAIC,OAAO,CAACrN,GAAG,CAACsN,IAAI,EAAElB,IAAI,CAAC;AACpC;AACA,SAASF,gBAAgBA,CAACD,OAAO,EAAE;EACjC,IAAIjM,GAAG,GAAG,IAAIE,GAAG,CAAC+L,OAAO,CAACjM,GAAG,CAAC;EAC9BA,GAAG,CAACwM,YAAY,CAACE,MAAM,CAAC,SAAS,CAAC;EAClC,IAAIN,IAAI,GAAG;IACTY,MAAM,EAAEf,OAAO,CAACe,MAAM;IACtBC,IAAI,EAAEhB,OAAO,CAACgB,IAAI;IAClBC,OAAO,EAAEjB,OAAO,CAACiB,OAAO;IACxBC,MAAM,EAAElB,OAAO,CAACkB;EAClB,CAAC;EACD,IAAIf,IAAI,CAACa,IAAI,EAAE;IACbb,IAAI,CAACgB,MAAM,GAAG,MAAM;EACtB;EACA,OAAO,IAAIC,OAAO,CAACrN,GAAG,CAACsN,IAAI,EAAElB,IAAI,CAAC;AACpC;;AAEA;AACA,SAASmB,UAAUA,CAAC9L,KAAK,EAAE4J,OAAO,EAAE;EAClC,IAAI5J,KAAK,KAAK,KAAK,IAAIA,KAAK,KAAK,IAAI,IAAI,OAAOA,KAAK,KAAK,WAAW,EAAE;IACrE+L,OAAO,CAACtG,KAAK,CACX,iIACF,CAAC;IACD,MAAM,IAAIlD,KAAK,CAACqH,OAAO,CAAC;EAC1B;AACF;;AAEA;AACA,IAAIoC,uBAAuB,GAAG,6BAA6B;AAC3D,SAASC,iBAAiBA,CAACC,cAAc,EAAE;EACzCC,UAAU,CAACH,uBAAuB,CAAC,GAAGE,cAAc;AACtD;AACA,SAASE,iBAAiBA,CAAA,EAAG;EAC3B,OAAOD,UAAU,CAACH,uBAAuB,CAAC;AAC5C;AACA,SAASK,kBAAkBA,CAAC7B,OAAO,EAAE8B,UAAU,EAAE;EAC/C,IAAI,OAAOC,OAAO,KAAK,WAAW,EAAE;IAClC,IAAI;MACF,IAAIA,OAAO,CAACC,GAAG,EAAEC,mBAAmB,KAAK,KAAK,EAAE;QAC9C,OAAOjC,OAAO,CAACiB,OAAO,CAACiB,GAAG,CAACJ,UAAU,CAAC;MACxC;IACF,CAAC,CAAC,OAAOK,CAAC,EAAE,CACZ;EACF;EACA,OAAO,IAAI;AACb;;AAEA;AACA,SAASC,qBAAqBA,CAAClO,QAAQ,EAAE;EACvC,IAAII,MAAM,GAAG,CAAC,CAAC;EACf8J,MAAM,CAACiE,MAAM,CAACnO,QAAQ,CAAC,CAACoO,OAAO,CAAExN,KAAK,IAAK;IACzC,IAAIA,KAAK,EAAE;MACT,IAAI+C,QAAQ,GAAG/C,KAAK,CAAC+C,QAAQ,IAAI,EAAE;MACnC,IAAI,CAACvD,MAAM,CAACuD,QAAQ,CAAC,EAAE;QACrBvD,MAAM,CAACuD,QAAQ,CAAC,GAAG,EAAE;MACvB;MACAvD,MAAM,CAACuD,QAAQ,CAAC,CAAC+I,IAAI,CAAC9L,KAAK,CAAC;IAC9B;EACF,CAAC,CAAC;EACF,OAAOR,MAAM;AACf;AACA,SAASiO,YAAYA,CAACrO,QAAQ,EAAE2D,QAAQ,GAAG,EAAE,EAAE2K,gBAAgB,GAAGJ,qBAAqB,CAAClO,QAAQ,CAAC,EAAE;EACjG,OAAO,CAACsO,gBAAgB,CAAC3K,QAAQ,CAAC,IAAI,EAAE,EAAEC,GAAG,CAAEhD,KAAK,KAAM;IACxD,GAAGA,KAAK;IACRyE,QAAQ,EAAEgJ,YAAY,CAACrO,QAAQ,EAAEY,KAAK,CAACC,EAAE,EAAEyN,gBAAgB;EAC7D,CAAC,CAAC,CAAC;AACL;AACA,SAASC,6BAA6BA,CAACvO,QAAQ,EAAEK,MAAM,EAAEsD,QAAQ,GAAG,EAAE,EAAE2K,gBAAgB,GAAGJ,qBAAqB,CAAClO,QAAQ,CAAC,EAAE;EAC1H,OAAO,CAACsO,gBAAgB,CAAC3K,QAAQ,CAAC,IAAI,EAAE,EAAEC,GAAG,CAAEhD,KAAK,IAAK;IACvD,IAAI4N,WAAW,GAAG;MAChB;MACA3J,gBAAgB,EAAEjE,KAAK,CAACC,EAAE,KAAK,MAAM,IAAID,KAAK,CAACwC,MAAM,CAACc,aAAa,IAAI,IAAI;MAC3ErD,EAAE,EAAED,KAAK,CAACC,EAAE;MACZkD,IAAI,EAAEnD,KAAK,CAACmD,IAAI;MAChBd,mBAAmB,EAAErC,KAAK,CAACwC,MAAM,CAACH,mBAAmB;MACrD;MACA;MACAoB,MAAM,EAAEzD,KAAK,CAACwC,MAAM,CAACiB,MAAM,GAAG,MAAOD,IAAI,IAAK;QAC5C,IAAIqK,eAAe,GAAGd,kBAAkB,CACtCvJ,IAAI,CAAC0H,OAAO,EACZ,+BACF,CAAC;QACD,IAAI2C,eAAe,IAAI,IAAI,EAAE;UAC3B,IAAI7F,OAAO,GAAG6F,eAAe,GAAGC,SAAS,CAACD,eAAe,CAAC,GAAGA,eAAe;UAC5ErB,UAAU,CAACxE,OAAO,EAAE,oCAAoC,CAAC;UACzD,IAAI+F,UAAU,GAAG,IAAIlJ,WAAW,CAAC,CAAC,CAAC1H,MAAM,CAAC6K,OAAO,CAAC;UAClD,IAAIgG,MAAM,GAAG,IAAIC,cAAc,CAAC;YAC9BC,KAAKA,CAACC,UAAU,EAAE;cAChBA,UAAU,CAACC,OAAO,CAACL,UAAU,CAAC;cAC9BI,UAAU,CAACE,KAAK,CAAC,CAAC;YACpB;UACF,CAAC,CAAC;UACF,IAAI3G,OAAO,GAAG,MAAMxK,oBAAoB,CAAC8Q,MAAM,EAAEM,MAAM,CAAC;UACxD,IAAItJ,KAAK,GAAG0C,OAAO,CAAChH,KAAK;UACzB,IAAIsE,KAAK,IAAIzI,yBAAyB,IAAIyI,KAAK,EAAE;YAC/C,IAAI4D,MAAM,GAAG5D,KAAK,CAACzI,yBAAyB,CAAC;YAC7C,IAAI8O,IAAI,GAAG;cAAEC,MAAM,EAAE1C,MAAM,CAAC0C;YAAO,CAAC;YACpC,IAAI1C,MAAM,CAAC2F,MAAM,EAAE;cACjB,MAAMtQ,gBAAgB,CAAC2K,MAAM,CAAC5K,QAAQ,EAAEqN,IAAI,CAAC;YAC/C,CAAC,MAAM,IAAIzC,MAAM,CAAC1K,OAAO,EAAE;cACzB,MAAMA,OAAO,CAAC0K,MAAM,CAAC5K,QAAQ,EAAEqN,IAAI,CAAC;YACtC,CAAC,MAAM;cACL,MAAMrN,QAAQ,CAAC4K,MAAM,CAAC5K,QAAQ,EAAEqN,IAAI,CAAC;YACvC;UACF,CAAC,MAAM;YACLmB,UAAU,CACRxH,KAAK,IAAIhF,KAAK,CAACC,EAAE,IAAI+E,KAAK,EAC1B,mCACF,CAAC;YACD,IAAI4D,MAAM,GAAG5D,KAAK,CAAChF,KAAK,CAACC,EAAE,CAAC;YAC5BuM,UAAU,CACR,MAAM,IAAI5D,MAAM,EAChB,oCACF,CAAC;YACD,OAAOA,MAAM,CAAC4F,IAAI;UACpB;QACF;QACA,IAAI/D,GAAG,GAAG,MAAMO,gBAAgB,CAAChL,KAAK,CAACwC,MAAM,CAACiB,MAAM,EAAED,IAAI,CAAC;QAC3D,OAAOiH,GAAG;MACZ,CAAC,GAAG,KAAK,CAAC;MACVlH,MAAM,EAAEvD,KAAK,CAACwC,MAAM,CAACe,MAAM,GAAIC,IAAI,IAAKwH,gBAAgB,CAAChL,KAAK,CAACwC,MAAM,CAACe,MAAM,EAAEC,IAAI,CAAC,GAAG,KAAK,CAAC;MAC5FE,MAAM,EAAE1D,KAAK,CAACwC,MAAM,CAACkB;IACvB,CAAC;IACD,OAAO1D,KAAK,CAACoD,KAAK,GAAG;MACnBA,KAAK,EAAE,IAAI;MACX,GAAGwK;IACL,CAAC,GAAG;MACFa,aAAa,EAAEzO,KAAK,CAACyO,aAAa;MAClChK,QAAQ,EAAEkJ,6BAA6B,CACrCvO,QAAQ,EACRK,MAAM,EACNO,KAAK,CAACC,EAAE,EACRyN,gBACF,CAAC;MACD,GAAGE;IACL,CAAC;EACH,CAAC,CAAC;AACJ;;AAEA;AACA,IAAIc,aAAa,GAAG;EAClB,GAAG,EAAE,SAAS;EACd,GAAG,EAAE,SAAS;EACd,GAAG,EAAE,SAAS;EACd,QAAQ,EAAE,SAAS;EACnB,QAAQ,EAAE;AACZ,CAAC;AACD,IAAIC,YAAY,GAAG,oBAAoB;AACvC,SAASC,UAAUA,CAACC,IAAI,EAAE;EACxB,OAAOA,IAAI,CAAC3Q,OAAO,CAACyQ,YAAY,EAAG9O,KAAK,IAAK6O,aAAa,CAAC7O,KAAK,CAAC,CAAC;AACpE;;AAEA;AACA,SAASiP,yBAAyBA,CAACC,aAAa,EAAE;EAChD,OAAOH,UAAU,CAACtG,IAAI,CAACC,SAAS,CAACwG,aAAa,CAAC,CAAC;AAClD;;AAEA;AACA,SAASC,kBAAkB,QAAQ,mBAAmB;AACtD,SAASC,kBAAkBA,CAACjQ,OAAO,EAAEkQ,KAAK,EAAE;EAC1C,OAAOC,sBAAsB,CAACnQ,OAAO,EAAGoQ,CAAC,IAAK;IAC5C,IAAIpP,KAAK,GAAGkP,KAAK,CAAC1P,MAAM,CAAC4P,CAAC,CAACpP,KAAK,CAACC,EAAE,CAAC;IACpCuM,UAAU,CAACxM,KAAK,EAAE,kBAAkBoP,CAAC,CAACpP,KAAK,CAACC,EAAE,sBAAsB,CAAC;IACrE,OAAOD,KAAK,CAACwC,MAAM,CAAC2J,OAAO;EAC7B,CAAC,CAAC;AACJ;AACA,SAASgD,sBAAsBA,CAACnQ,OAAO,EAAEqQ,iBAAiB,EAAE;EAC1D,IAAIC,WAAW,GAAGtQ,OAAO,CAACkL,MAAM,GAAGlL,OAAO,CAACc,OAAO,CAACyP,SAAS,CAAEH,CAAC,IAAKpQ,OAAO,CAACkL,MAAM,CAACkF,CAAC,CAACpP,KAAK,CAACC,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC;EACpG,IAAIH,OAAO,GAAGwP,WAAW,IAAI,CAAC,GAAGtQ,OAAO,CAACc,OAAO,CAACgG,KAAK,CAAC,CAAC,EAAEwJ,WAAW,GAAG,CAAC,CAAC,GAAGtQ,OAAO,CAACc,OAAO;EAC5F,IAAI0P,YAAY;EAChB,IAAIF,WAAW,IAAI,CAAC,EAAE;IACpB,IAAI;MAAEG,aAAa;MAAEC,UAAU;MAAEC,aAAa;MAAE/P;IAAW,CAAC,GAAGZ,OAAO;IACtEA,OAAO,CAACc,OAAO,CAACgG,KAAK,CAACwJ,WAAW,CAAC,CAACM,IAAI,CAAE/P,KAAK,IAAK;MACjD,IAAII,EAAE,GAAGJ,KAAK,CAACG,KAAK,CAACC,EAAE;MACvB,IAAIwP,aAAa,CAACxP,EAAE,CAAC,KAAK,CAACyP,UAAU,IAAI,CAACA,UAAU,CAACG,cAAc,CAAC5P,EAAE,CAAC,CAAC,EAAE;QACxEuP,YAAY,GAAGC,aAAa,CAACxP,EAAE,CAAC;MAClC,CAAC,MAAM,IAAI0P,aAAa,CAAC1P,EAAE,CAAC,IAAI,CAACL,UAAU,CAACiQ,cAAc,CAAC5P,EAAE,CAAC,EAAE;QAC9DuP,YAAY,GAAGG,aAAa,CAAC1P,EAAE,CAAC;MAClC;MACA,OAAOuP,YAAY,IAAI,IAAI;IAC7B,CAAC,CAAC;EACJ;EACA,OAAO1P,OAAO,CAAC0J,MAAM,CAAC,CAACsG,aAAa,EAAEjQ,KAAK,EAAEkQ,GAAG,KAAK;IACnD,IAAI;MAAE9P;IAAG,CAAC,GAAGJ,KAAK,CAACG,KAAK;IACxB,IAAI2P,aAAa,GAAG3Q,OAAO,CAAC2Q,aAAa,CAAC1P,EAAE,CAAC,IAAI,IAAI+P,OAAO,CAAC,CAAC;IAC9D,IAAIP,aAAa,GAAGzQ,OAAO,CAACyQ,aAAa,CAACxP,EAAE,CAAC,IAAI,IAAI+P,OAAO,CAAC,CAAC;IAC9D,IAAIC,mBAAmB,GAAGT,YAAY,IAAI,IAAI,IAAIO,GAAG,KAAKjQ,OAAO,CAAC2G,MAAM,GAAG,CAAC;IAC5E,IAAIyJ,mBAAmB,GAAGD,mBAAmB,IAAIT,YAAY,KAAKG,aAAa,IAAIH,YAAY,KAAKC,aAAa;IACjH,IAAIU,SAAS,GAAGd,iBAAiB,CAACxP,KAAK,CAAC;IACxC,IAAIsQ,SAAS,IAAI,IAAI,EAAE;MACrB,IAAIC,QAAQ,GAAG,IAAIJ,OAAO,CAACF,aAAa,CAAC;MACzC,IAAII,mBAAmB,EAAE;QACvBG,cAAc,CAACb,YAAY,EAAEY,QAAQ,CAAC;MACxC;MACAC,cAAc,CAACZ,aAAa,EAAEW,QAAQ,CAAC;MACvCC,cAAc,CAACV,aAAa,EAAES,QAAQ,CAAC;MACvC,OAAOA,QAAQ;IACjB;IACA,IAAIjE,OAAO,GAAG,IAAI6D,OAAO,CACvB,OAAOG,SAAS,KAAK,UAAU,GAAGA,SAAS,CAAC;MAC1CR,aAAa;MACbG,aAAa;MACbL,aAAa;MACbD,YAAY,EAAES,mBAAmB,GAAGT,YAAY,GAAG,KAAK;IAC1D,CAAC,CAAC,GAAGW,SACP,CAAC;IACD,IAAID,mBAAmB,EAAE;MACvBG,cAAc,CAACb,YAAY,EAAErD,OAAO,CAAC;IACvC;IACAkE,cAAc,CAACZ,aAAa,EAAEtD,OAAO,CAAC;IACtCkE,cAAc,CAACV,aAAa,EAAExD,OAAO,CAAC;IACtCkE,cAAc,CAACP,aAAa,EAAE3D,OAAO,CAAC;IACtC,OAAOA,OAAO;EAChB,CAAC,EAAE,IAAI6D,OAAO,CAAC,CAAC,CAAC;AACnB;AACA,SAASK,cAAcA,CAACP,aAAa,EAAEQ,YAAY,EAAE;EACnD,IAAIC,qBAAqB,GAAGT,aAAa,CAAC1C,GAAG,CAAC,YAAY,CAAC;EAC3D,IAAImD,qBAAqB,EAAE;IACzB,IAAI9I,OAAO,GAAGuH,kBAAkB,CAACuB,qBAAqB,CAAC;IACvD,IAAIC,YAAY,GAAG,IAAIC,GAAG,CAACH,YAAY,CAACI,YAAY,CAAC,CAAC,CAAC;IACvDjJ,OAAO,CAAC+F,OAAO,CAAE5H,MAAM,IAAK;MAC1B,IAAI,CAAC4K,YAAY,CAACG,GAAG,CAAC/K,MAAM,CAAC,EAAE;QAC7B0K,YAAY,CAACtE,MAAM,CAAC,YAAY,EAAEpG,MAAM,CAAC;MAC3C;IACF,CAAC,CAAC;EACJ;AACF;;AAEA;AACA,IAAIgL,2BAA2B,GAAG,eAAgB,IAAIH,GAAG,CAAC,CACxD,GAAGxU,oBAAoB,EACvB,GAAG,CACJ,CAAC;AACF,eAAe4U,iBAAiBA,CAAC3B,KAAK,EAAEpF,UAAU,EAAEgH,aAAa,EAAE5F,OAAO,EAAE6F,UAAU,EAAEC,WAAW,EAAEC,WAAW,EAAE;EAChH,IAAI;IACF,IAAIC,QAAQ,GAAG,SAAAA,CAASlS,OAAO,EAAE;MAC/B,IAAImN,OAAO,GAAG8C,kBAAkB,CAACjQ,OAAO,EAAEkQ,KAAK,CAAC;MAChD,IAAIvR,oBAAoB,CAACqB,OAAO,CAACmS,UAAU,CAAC,IAAIhF,OAAO,CAACwE,GAAG,CAAC,UAAU,CAAC,EAAE;QACvE,OAAOS,2BAA2B,CAAClG,OAAO,EAAEgE,KAAK,EAAEpF,UAAU,EAAE;UAC7DlB,MAAM,EAAEyI,sBAAsB,CAC5BrS,OAAO,CAACmS,UAAU,EAClBhF,OAAO,EACP+C,KAAK,CAACpE,QACR,CAAC;UACDqB,OAAO;UACPb,MAAM,EAAEhP;QACV,CAAC,CAAC;MACJ;MACA,IAAI0C,OAAO,CAACkL,MAAM,EAAE;QAClBZ,MAAM,CAACiE,MAAM,CAACvO,OAAO,CAACkL,MAAM,CAAC,CAACsD,OAAO,CAAE8D,GAAG,IAAK;UAC7C,IAAI,CAACzT,oBAAoB,CAACyT,GAAG,CAAC,IAAIA,GAAG,CAACnL,KAAK,EAAE;YAC3C8K,WAAW,CAACK,GAAG,CAAC;UAClB;QACF,CAAC,CAAC;QACFtS,OAAO,CAACkL,MAAM,GAAGD,cAAc,CAACjL,OAAO,CAACkL,MAAM,EAAEJ,UAAU,CAAC;MAC7D;MACA,IAAIyH,iBAAiB;MACrB,IAAIvS,OAAO,CAACkL,MAAM,EAAE;QAClBqH,iBAAiB,GAAG;UAAEpL,KAAK,EAAEmD,MAAM,CAACiE,MAAM,CAACvO,OAAO,CAACkL,MAAM,CAAC,CAAC,CAAC;QAAE,CAAC;MACjE,CAAC,MAAM;QACLqH,iBAAiB,GAAG;UAClB/C,IAAI,EAAElF,MAAM,CAACiE,MAAM,CAACvO,OAAO,CAAC0Q,UAAU,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;QACjD,CAAC;MACH;MACA,OAAO0B,2BAA2B,CAAClG,OAAO,EAAEgE,KAAK,EAAEpF,UAAU,EAAE;QAC7DlB,MAAM,EAAE2I,iBAAiB;QACzBpF,OAAO;QACPb,MAAM,EAAEtM,OAAO,CAACmS;MAClB,CAAC,CAAC;IACJ,CAAC;IACD,IAAIK,OAAO,GAAGN,QAAQ;IACtB,IAAIO,cAAc,GAAG,IAAInF,OAAO,CAACyE,UAAU,EAAE;MAC3C9E,MAAM,EAAEf,OAAO,CAACe,MAAM;MACtBC,IAAI,EAAEhB,OAAO,CAACgB,IAAI;MAClBC,OAAO,EAAEjB,OAAO,CAACiB,OAAO;MACxBC,MAAM,EAAElB,OAAO,CAACkB,MAAM;MACtB,IAAGlB,OAAO,CAACgB,IAAI,GAAG;QAAEG,MAAM,EAAE;MAAO,CAAC,GAAG,KAAK,CAAC;IAC/C,CAAC,CAAC;IACF,IAAIzD,MAAM,GAAG,MAAMkI,aAAa,CAACY,KAAK,CAACD,cAAc,EAAE;MACrDE,cAAc,EAAEX,WAAW;MAC3BY,uBAAuB,EAAE,IAAI;MAC7BC,gBAAgB,EAAE,IAAI;MACtBC,gBAAgB,EAAEZ;IACpB,CAAC,CAAC;IACF,IAAI,CAACtT,UAAU,CAACgL,MAAM,CAAC,EAAE;MACvBA,MAAM,GAAGsI,QAAQ,CAACtI,MAAM,CAAC;IAC3B;IACA,IAAIlL,kBAAkB,CAACkL,MAAM,CAAC,EAAE;MAC9B,OAAOwI,2BAA2B,CAAClG,OAAO,EAAEgE,KAAK,EAAEpF,UAAU,EAAE;QAC7DlB,MAAM,EAAEyI,sBAAsB,CAC5BzI,MAAM,CAAC0C,MAAM,EACb1C,MAAM,CAACuD,OAAO,EACd+C,KAAK,CAACpE,QACR,CAAC;QACDqB,OAAO,EAAEvD,MAAM,CAACuD,OAAO;QACvBb,MAAM,EAAEhP;MACV,CAAC,CAAC;IACJ;IACA,OAAOsM,MAAM;EACf,CAAC,CAAC,OAAOzC,KAAK,EAAE;IACd8K,WAAW,CAAC9K,KAAK,CAAC;IAClB,OAAOiL,2BAA2B,CAAClG,OAAO,EAAEgE,KAAK,EAAEpF,UAAU,EAAE;MAC7DlB,MAAM,EAAE;QAAEzC;MAAM,CAAC;MACjBgG,OAAO,EAAE,IAAI6D,OAAO,CAAC,CAAC;MACtB1E,MAAM,EAAE;IACV,CAAC,CAAC;EACJ;AACF;AACA,eAAeyG,kBAAkBA,CAAC7C,KAAK,EAAEpF,UAAU,EAAEgH,aAAa,EAAE5F,OAAO,EAAE6F,UAAU,EAAEC,WAAW,EAAEC,WAAW,EAAE;EACjH,IAAI;IACF,IAAIC,QAAQ,GAAG,SAAAA,CAASlS,OAAO,EAAE;MAC/B,IAAImN,OAAO,GAAG8C,kBAAkB,CAACjQ,OAAO,EAAEkQ,KAAK,CAAC;MAChD,IAAIvR,oBAAoB,CAACqB,OAAO,CAACmS,UAAU,CAAC,IAAIhF,OAAO,CAACwE,GAAG,CAAC,UAAU,CAAC,EAAE;QACvE,OAAOS,2BAA2B,CAAClG,OAAO,EAAEgE,KAAK,EAAEpF,UAAU,EAAE;UAC7DlB,MAAM,EAAE;YACN,CAACrM,yBAAyB,GAAG8U,sBAAsB,CACjDrS,OAAO,CAACmS,UAAU,EAClBhF,OAAO,EACP+C,KAAK,CAACpE,QACR;UACF,CAAC;UACDqB,OAAO;UACPb,MAAM,EAAEhP;QACV,CAAC,CAAC;MACJ;MACA,IAAI0C,OAAO,CAACkL,MAAM,EAAE;QAClBZ,MAAM,CAACiE,MAAM,CAACvO,OAAO,CAACkL,MAAM,CAAC,CAACsD,OAAO,CAAE8D,GAAG,IAAK;UAC7C,IAAI,CAACzT,oBAAoB,CAACyT,GAAG,CAAC,IAAIA,GAAG,CAACnL,KAAK,EAAE;YAC3C8K,WAAW,CAACK,GAAG,CAAC;UAClB;QACF,CAAC,CAAC;QACFtS,OAAO,CAACkL,MAAM,GAAGD,cAAc,CAACjL,OAAO,CAACkL,MAAM,EAAEJ,UAAU,CAAC;MAC7D;MACA,IAAIkI,OAAO,GAAG,CAAC,CAAC;MAChB,IAAIC,aAAa,GAAG,IAAIxB,GAAG,CACzBzR,OAAO,CAACc,OAAO,CAACoS,MAAM,CACnB9C,CAAC,IAAK+C,YAAY,GAAGA,YAAY,CAACxB,GAAG,CAACvB,CAAC,CAACpP,KAAK,CAACC,EAAE,CAAC,GAAGmP,CAAC,CAACpP,KAAK,CAACyD,MAAM,IAAI,IACzE,CAAC,CAACT,GAAG,CAAEoM,CAAC,IAAKA,CAAC,CAACpP,KAAK,CAACC,EAAE,CACzB,CAAC;MACD,IAAIjB,OAAO,CAACkL,MAAM,EAAE;QAClB,KAAK,IAAI,CAACjK,EAAE,EAAEkG,KAAK,CAAC,IAAImD,MAAM,CAACa,OAAO,CAACnL,OAAO,CAACkL,MAAM,CAAC,EAAE;UACtD8H,OAAO,CAAC/R,EAAE,CAAC,GAAG;YAAEkG;UAAM,CAAC;QACzB;MACF;MACA,KAAK,IAAI,CAAClG,EAAE,EAAE+E,KAAK,CAAC,IAAIsE,MAAM,CAACa,OAAO,CAACnL,OAAO,CAACY,UAAU,CAAC,EAAE;QAC1D,IAAI,EAAEK,EAAE,IAAI+R,OAAO,CAAC,IAAIC,aAAa,CAACtB,GAAG,CAAC1Q,EAAE,CAAC,EAAE;UAC7C+R,OAAO,CAAC/R,EAAE,CAAC,GAAG;YAAEuO,IAAI,EAAExJ;UAAM,CAAC;QAC/B;MACF;MACA,OAAOoM,2BAA2B,CAAClG,OAAO,EAAEgE,KAAK,EAAEpF,UAAU,EAAE;QAC7DlB,MAAM,EAAEoJ,OAAO;QACf7F,OAAO;QACPb,MAAM,EAAEtM,OAAO,CAACmS;MAClB,CAAC,CAAC;IACJ,CAAC;IACD,IAAIK,OAAO,GAAGN,QAAQ;IACtB,IAAIO,cAAc,GAAG,IAAInF,OAAO,CAACyE,UAAU,EAAE;MAC3C5E,OAAO,EAAEjB,OAAO,CAACiB,OAAO;MACxBC,MAAM,EAAElB,OAAO,CAACkB;IAClB,CAAC,CAAC;IACF,IAAIgG,WAAW,GAAG,IAAIjT,GAAG,CAAC+L,OAAO,CAACjM,GAAG,CAAC,CAACwM,YAAY,CAAC2B,GAAG,CAAC,SAAS,CAAC;IAClE,IAAI+E,YAAY,GAAGC,WAAW,GAAG,IAAI3B,GAAG,CAAC2B,WAAW,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,IAAI;IACvE,IAAIzJ,MAAM,GAAG,MAAMkI,aAAa,CAACY,KAAK,CAACD,cAAc,EAAE;MACrDE,cAAc,EAAEX,WAAW;MAC3BsB,mBAAmB,EAAGlD,CAAC,IAAK,CAAC+C,YAAY,IAAIA,YAAY,CAACxB,GAAG,CAACvB,CAAC,CAACpP,KAAK,CAACC,EAAE,CAAC;MACzE2R,uBAAuB,EAAE,IAAI;MAC7BE,gBAAgB,EAAEZ;IACpB,CAAC,CAAC;IACF,IAAI,CAACtT,UAAU,CAACgL,MAAM,CAAC,EAAE;MACvBA,MAAM,GAAGsI,QAAQ,CAACtI,MAAM,CAAC;IAC3B;IACA,IAAIlL,kBAAkB,CAACkL,MAAM,CAAC,EAAE;MAC9B,OAAOwI,2BAA2B,CAAClG,OAAO,EAAEgE,KAAK,EAAEpF,UAAU,EAAE;QAC7DlB,MAAM,EAAE;UACN,CAACrM,yBAAyB,GAAG8U,sBAAsB,CACjDzI,MAAM,CAAC0C,MAAM,EACb1C,MAAM,CAACuD,OAAO,EACd+C,KAAK,CAACpE,QACR;QACF,CAAC;QACDqB,OAAO,EAAEvD,MAAM,CAACuD,OAAO;QACvBb,MAAM,EAAEhP;MACV,CAAC,CAAC;IACJ;IACA,OAAOsM,MAAM;EACf,CAAC,CAAC,OAAOzC,KAAK,EAAE;IACd8K,WAAW,CAAC9K,KAAK,CAAC;IAClB,OAAOiL,2BAA2B,CAAClG,OAAO,EAAEgE,KAAK,EAAEpF,UAAU,EAAE;MAC7DlB,MAAM,EAAE;QAAE2J,IAAI,EAAE;UAAEpM;QAAM;MAAE,CAAC;MAC3BgG,OAAO,EAAE,IAAI6D,OAAO,CAAC,CAAC;MACtB1E,MAAM,EAAE;IACV,CAAC,CAAC;EACJ;AACF;AACA,SAAS8F,2BAA2BA,CAAClG,OAAO,EAAEgE,KAAK,EAAEpF,UAAU,EAAE;EAC/DlB,MAAM;EACNuD,OAAO;EACPb;AACF,CAAC,EAAE;EACD,IAAIkH,aAAa,GAAG,IAAIxC,OAAO,CAAC7D,OAAO,CAAC;EACxCqG,aAAa,CAACC,GAAG,CAAC,kBAAkB,EAAE,KAAK,CAAC;EAC5C,IAAI7B,2BAA2B,CAACD,GAAG,CAACrF,MAAM,CAAC,EAAE;IAC3C,OAAO,IAAIC,QAAQ,CAAC,IAAI,EAAE;MAAED,MAAM;MAAEa,OAAO,EAAEqG;IAAc,CAAC,CAAC;EAC/D;EACAA,aAAa,CAACC,GAAG,CAAC,cAAc,EAAE,eAAe,CAAC;EAClDD,aAAa,CAAC7G,MAAM,CAAC,gBAAgB,CAAC;EACtC,OAAO,IAAIJ,QAAQ,CACjBmH,oBAAoB,CAClB9J,MAAM,EACNsC,OAAO,CAACkB,MAAM,EACd8C,KAAK,CAAC5M,KAAK,CAACE,MAAM,CAACmQ,aAAa,EAChC7I,UACF,CAAC,EACD;IACEwB,MAAM,EAAEA,MAAM,IAAI,GAAG;IACrBa,OAAO,EAAEqG;EACX,CACF,CAAC;AACH;AACA,SAASnB,sBAAsBA,CAAC/F,MAAM,EAAEa,OAAO,EAAErB,QAAQ,EAAE;EACzD,IAAI8H,SAAS,GAAGzG,OAAO,CAACiB,GAAG,CAAC,UAAU,CAAC;EACvC,IAAItC,QAAQ,EAAE;IACZ8H,SAAS,GAAGvU,aAAa,CAACuU,SAAS,EAAE9H,QAAQ,CAAC,IAAI8H,SAAS;EAC7D;EACA,OAAO;IACL5U,QAAQ,EAAE4U,SAAS;IACnBtH,MAAM;IACNuH,UAAU;IACR;IACA;IACA;IACA;IACA;IACA;IACA;IACA1G,OAAO,CAACwE,GAAG,CAAC,oBAAoB,CAAC,IAAIxE,OAAO,CAACwE,GAAG,CAAC,YAAY,CAC9D;IACDpC,MAAM,EAAEpC,OAAO,CAACwE,GAAG,CAAC,yBAAyB,CAAC;IAC9CzS,OAAO,EAAEiO,OAAO,CAACwE,GAAG,CAAC,iBAAiB;EACxC,CAAC;AACH;AACA,SAAS+B,oBAAoBA,CAAC1N,KAAK,EAAE8N,aAAa,EAAEH,aAAa,EAAE7I,UAAU,EAAE;EAC7E,IAAIqE,UAAU,GAAG,IAAI4E,eAAe,CAAC,CAAC;EACtC,IAAIC,SAAS,GAAGC,UAAU,CACxB,MAAM9E,UAAU,CAAC+E,KAAK,CAAC,IAAIjQ,KAAK,CAAC,gBAAgB,CAAC,CAAC,EACnD,OAAO0P,aAAa,KAAK,QAAQ,GAAGA,aAAa,GAAG,IACtD,CAAC;EACDG,aAAa,CAACK,gBAAgB,CAAC,OAAO,EAAE,MAAMC,YAAY,CAACJ,SAAS,CAAC,CAAC;EACtE,OAAO7V,MAAM,CAAC6H,KAAK,EAAE;IACnBoH,MAAM,EAAE+B,UAAU,CAAC/B,MAAM;IACzBiH,OAAO,EAAE,CACN3S,KAAK,IAAK;MACT,IAAIA,KAAK,YAAYuC,KAAK,EAAE;QAC1B,IAAI;UAAEqD,IAAI;UAAEgE,OAAO;UAAEN;QAAM,CAAC,GAAGF,UAAU,KAAK,YAAY,CAAC,mBAAmBD,aAAa,CAACnJ,KAAK,EAAEoJ,UAAU,CAAC,GAAGpJ,KAAK;QACtH,OAAO,CAAC,gBAAgB,EAAE4F,IAAI,EAAEgE,OAAO,EAAEN,KAAK,CAAC;MACjD;MACA,IAAItJ,KAAK,YAAY3E,iBAAiB,EAAE;QACtC,IAAI;UAAEyS,IAAI,EAAE8E,KAAK;UAAEhI,MAAM;UAAEiI;QAAW,CAAC,GAAG7S,KAAK;QAC/C,OAAO,CAAC,eAAe,EAAE4S,KAAK,EAAEhI,MAAM,EAAEiI,UAAU,CAAC;MACrD;MACA,IAAI7S,KAAK,IAAI,OAAOA,KAAK,KAAK,QAAQ,IAAInE,yBAAyB,IAAImE,KAAK,EAAE;QAC5E,OAAO,CAAC,qBAAqB,EAAEA,KAAK,CAACnE,yBAAyB,CAAC,CAAC;MAClE;IACF,CAAC,CACF;IACDiX,WAAW,EAAE,CACV9S,KAAK,IAAK;MACT,IAAI,CAACA,KAAK,EAAE;MACZ,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;MAC/B,OAAO,CACL,0BAA0B,EAC1B4I,MAAM,CAACmK,WAAW,CAACnK,MAAM,CAACa,OAAO,CAACzJ,KAAK,CAAC,CAAC,CAC1C;IACH,CAAC,EACD,MAAM,CAAC,qBAAqB,CAAC;EAEjC,CAAC,CAAC;AACJ;;AAEA;AACA,SAASgT,MAAMA,CAACxE,KAAK,EAAExM,IAAI,EAAE;EAC3B,IAAIlD,MAAM,GAAGiO,YAAY,CAACyB,KAAK,CAAC1P,MAAM,CAAC;EACvC,IAAImU,UAAU,GAAGhG,6BAA6B,CAACuB,KAAK,CAAC1P,MAAM,EAAE0P,KAAK,CAACzP,MAAM,CAAC;EAC1E,IAAIqK,UAAU,GAAGF,YAAY,CAAClH,IAAI,CAAC,GAAGA,IAAI,GAAG,YAAY,CAAC;EAC1D,IAAIoO,aAAa,GAAG9T,mBAAmB,CAAC2W,UAAU,EAAE;IAClD7I,QAAQ,EAAEoE,KAAK,CAACpE;EAClB,CAAC,CAAC;EACF,IAAI8I,YAAY,GAAG1E,KAAK,CAAC5M,KAAK,CAACE,MAAM,CAACyO,WAAW,KAAK,CAAC9K,KAAK,EAAE;IAAE+E;EAAQ,CAAC,KAAK;IAC5E,IAAIpB,UAAU,KAAK,MAAM,CAAC,cAAc,CAACoB,OAAO,CAACkB,MAAM,CAACyH,OAAO,EAAE;MAC/DpH,OAAO,CAACtG,KAAK;MACX;MACAtI,oBAAoB,CAACsI,KAAK,CAAC,IAAIA,KAAK,CAACA,KAAK,GAAGA,KAAK,CAACA,KAAK,GAAGA,KAC7D,CAAC;IACH;EACF,CAAC,CAAC;EACF,OAAO;IACL3G,MAAM;IACNmU,UAAU;IACV7J,UAAU;IACVgH,aAAa;IACb8C;EACF,CAAC;AACH;AACA,IAAIE,oBAAoB,GAAGA,CAAC5E,KAAK,EAAExM,IAAI,KAAK;EAC1C,IAAIqR,MAAM;EACV,IAAIvU,MAAM;EACV,IAAIsK,UAAU;EACd,IAAIgH,aAAa;EACjB,IAAI8C,YAAY;EAChB,OAAO,eAAeI,cAAcA,CAAC9I,OAAO,EAAE+I,cAAc,EAAE;IAC5DF,MAAM,GAAG,OAAO7E,KAAK,KAAK,UAAU,GAAG,MAAMA,KAAK,CAAC,CAAC,GAAGA,KAAK;IAC5D,IAAI,OAAOA,KAAK,KAAK,UAAU,EAAE;MAC/B,IAAIgF,OAAO,GAAGR,MAAM,CAACK,MAAM,EAAErR,IAAI,CAAC;MAClClD,MAAM,GAAG0U,OAAO,CAAC1U,MAAM;MACvBsK,UAAU,GAAGoK,OAAO,CAACpK,UAAU;MAC/BgH,aAAa,GAAGoD,OAAO,CAACpD,aAAa;MACrC8C,YAAY,GAAGM,OAAO,CAACN,YAAY;IACrC,CAAC,MAAM,IAAI,CAACpU,MAAM,IAAI,CAACsK,UAAU,IAAI,CAACgH,aAAa,IAAI,CAAC8C,YAAY,EAAE;MACpE,IAAIM,OAAO,GAAGR,MAAM,CAACK,MAAM,EAAErR,IAAI,CAAC;MAClClD,MAAM,GAAG0U,OAAO,CAAC1U,MAAM;MACvBsK,UAAU,GAAGoK,OAAO,CAACpK,UAAU;MAC/BgH,aAAa,GAAGoD,OAAO,CAACpD,aAAa;MACrC8C,YAAY,GAAGM,OAAO,CAACN,YAAY;IACrC;IACA,IAAI7I,MAAM,GAAG,CAAC,CAAC;IACf,IAAIiG,WAAW;IACf,IAAIC,WAAW,GAAI9K,KAAK,IAAK;MAC3B,IAAIzD,IAAI,KAAK,aAAa,CAAC,mBAAmB;QAC5CoK,iBAAiB,CAAC,CAAC,EAAEqH,mBAAmB,GAAGhO,KAAK,CAAC;MACnD;MACAyN,YAAY,CAACzN,KAAK,EAAE;QAClBnH,OAAO,EAAEgS,WAAW;QACpBjG,MAAM;QACNG;MACF,CAAC,CAAC;IACJ,CAAC;IACD,IAAI6I,MAAM,CAACtU,MAAM,CAAC4C,mBAAmB,EAAE;MACrC,IAAI4R,cAAc,IAAI,IAAI,EAAE;QAC1BjD,WAAW,GAAG,IAAIzS,8BAA8B,CAAC,CAAC;MACpD,CAAC,MAAM;QACL,IAAI;UACFyS,WAAW,GAAG,IAAIzS,8BAA8B,CAC9C0V,cACF,CAAC;QACH,CAAC,CAAC,OAAO5G,CAAC,EAAE;UACV,IAAIlH,KAAK,GAAG,IAAIlD,KAAK,CACnB;AACZ;AACA,SAASoK,CAAC,YAAYpK,KAAK,GAAGoK,CAAC,CAAC1E,QAAQ,CAAC,CAAC,GAAG0E,CAAC,EACpC,CAAC;UACD4D,WAAW,CAAC9K,KAAK,CAAC;UAClB,OAAOiO,6BAA6B,CAACjO,KAAK,EAAE2D,UAAU,CAAC;QACzD;MACF;IACF,CAAC,MAAM;MACLkH,WAAW,GAAGiD,cAAc,IAAI,CAAC,CAAC;IACpC;IACA,IAAIhV,GAAG,GAAG,IAAIE,GAAG,CAAC+L,OAAO,CAACjM,GAAG,CAAC;IAC9B,IAAIoV,kBAAkB,GAAGN,MAAM,CAACjJ,QAAQ,IAAI,GAAG;IAC/C,IAAIwJ,cAAc,GAAGrV,GAAG,CAAC4L,QAAQ;IACjC,IAAIxM,aAAa,CAACiW,cAAc,EAAED,kBAAkB,CAAC,KAAK,aAAa,EAAE;MACvEC,cAAc,GAAGD,kBAAkB;IACrC,CAAC,MAAM,IAAIC,cAAc,CAACC,QAAQ,CAAC,OAAO,CAAC,EAAE;MAC3CD,cAAc,GAAGA,cAAc,CAACpW,OAAO,CAAC,SAAS,EAAE,EAAE,CAAC;IACxD;IACA,IAAIG,aAAa,CAACiW,cAAc,EAAED,kBAAkB,CAAC,KAAK,GAAG,IAAIC,cAAc,CAACC,QAAQ,CAAC,GAAG,CAAC,EAAE;MAC7FD,cAAc,GAAGA,cAAc,CAACxO,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IAC9C;IACA,IAAIpG,SAAS,GAAGqN,kBAAkB,CAAC7B,OAAO,EAAE,yBAAyB,CAAC,KAAK,KAAK;IAChF,IAAI,CAAC6I,MAAM,CAACpT,GAAG,EAAE;MACf,IAAI6T,WAAW,GAAG1G,SAAS,CAACwG,cAAc,CAAC;MAC3C,IAAIP,MAAM,CAACU,SAAS,CAAChO,MAAM,KAAK,CAAC,EAAE;QACjC/G,SAAS,GAAG,IAAI;MAClB,CAAC,MAAM,IAAI,CAACqU,MAAM,CAACU,SAAS,CAACC,QAAQ,CAACF,WAAW,CAAC,IAAI,CAACT,MAAM,CAACU,SAAS,CAACC,QAAQ,CAACF,WAAW,GAAG,GAAG,CAAC,EAAE;QACnG,IAAIvV,GAAG,CAAC4L,QAAQ,CAAC0J,QAAQ,CAAC,OAAO,CAAC,EAAE;UAClCX,YAAY,CACV,IAAI7X,iBAAiB,CACnB,GAAG,EACH,WAAW,EACX,8BAA8ByY,WAAW,oIAC3C,CAAC,EACD;YACExV,OAAO,EAAEgS,WAAW;YACpBjG,MAAM;YACNG;UACF,CACF,CAAC;UACD,OAAO,IAAIK,QAAQ,CAAC,WAAW,EAAE;YAC/BD,MAAM,EAAE,GAAG;YACXiI,UAAU,EAAE;UACd,CAAC,CAAC;QACJ,CAAC,MAAM;UACL7T,SAAS,GAAG,IAAI;QAClB;MACF;IACF;IACA,IAAIiV,WAAW,GAAGvX,eAAe,CAC/B2W,MAAM,CAACnT,cAAc,CAAC+B,YAAY,EAClC0R,kBACF,CAAC;IACD,IAAIpV,GAAG,CAAC4L,QAAQ,KAAK8J,WAAW,EAAE;MAChC,IAAI;QACF,IAAIC,GAAG,GAAG,MAAMC,qBAAqB,CAACd,MAAM,EAAEvU,MAAM,EAAEP,GAAG,CAAC;QAC1D,OAAO2V,GAAG;MACZ,CAAC,CAAC,OAAOvH,CAAC,EAAE;QACV4D,WAAW,CAAC5D,CAAC,CAAC;QACd,OAAO,IAAI9B,QAAQ,CAAC,sBAAsB,EAAE;UAAED,MAAM,EAAE;QAAI,CAAC,CAAC;MAC9D;IACF;IACA,IAAIxL,OAAO,GAAG8K,iBAAiB,CAACpL,MAAM,EAAE8U,cAAc,EAAEP,MAAM,CAACjJ,QAAQ,CAAC;IACxE,IAAIhL,OAAO,IAAIA,OAAO,CAAC2G,MAAM,GAAG,CAAC,EAAE;MACjC6C,MAAM,CAACe,MAAM,CAACU,MAAM,EAAEjL,OAAO,CAAC,CAAC,CAAC,CAACiL,MAAM,CAAC;IAC1C;IACA,IAAI+J,QAAQ;IACZ,IAAI7V,GAAG,CAAC4L,QAAQ,CAAC0J,QAAQ,CAAC,OAAO,CAAC,EAAE;MAClC,IAAIxD,UAAU,GAAG,IAAI5R,GAAG,CAAC+L,OAAO,CAACjM,GAAG,CAAC;MACrC8R,UAAU,CAAClG,QAAQ,GAAGyJ,cAAc;MACpC,IAAIS,kBAAkB,GAAGnK,iBAAiB,CACxCpL,MAAM,EACNuR,UAAU,CAAClG,QAAQ,EACnBkJ,MAAM,CAACjJ,QACT,CAAC;MACDgK,QAAQ,GAAG,MAAME,wBAAwB,CACvClL,UAAU,EACViK,MAAM,EACNjD,aAAa,EACb5F,OAAO,EACP6F,UAAU,EACVC,WAAW,EACXC,WACF,CAAC;MACD,IAAI8C,MAAM,CAACzR,KAAK,CAACE,MAAM,CAACyS,iBAAiB,EAAE;QACzCH,QAAQ,GAAG,MAAMf,MAAM,CAACzR,KAAK,CAACE,MAAM,CAACyS,iBAAiB,CAACH,QAAQ,EAAE;UAC/D9V,OAAO,EAAEgS,WAAW;UACpBjG,MAAM,EAAEgK,kBAAkB,GAAGA,kBAAkB,CAAC,CAAC,CAAC,CAAChK,MAAM,GAAG,CAAC,CAAC;UAC9DG;QACF,CAAC,CAAC;QACF,IAAIxN,kBAAkB,CAACoX,QAAQ,CAAC,EAAE;UAChC,IAAIlM,MAAM,GAAGyI,sBAAsB,CACjCyD,QAAQ,CAACxJ,MAAM,EACfwJ,QAAQ,CAAC3I,OAAO,EAChB4H,MAAM,CAACjJ,QACT,CAAC;UACD,IAAII,OAAO,CAACe,MAAM,KAAK,KAAK,EAAE;YAC5BrD,MAAM,GAAG;cACP,CAACrM,yBAAyB,GAAGqM;YAC/B,CAAC;UACH;UACA,IAAIuD,OAAO,GAAG,IAAI6D,OAAO,CAAC8E,QAAQ,CAAC3I,OAAO,CAAC;UAC3CA,OAAO,CAACsG,GAAG,CAAC,cAAc,EAAE,eAAe,CAAC;UAC5C,OAAO,IAAIlH,QAAQ,CACjBmH,oBAAoB,CAClB9J,MAAM,EACNsC,OAAO,CAACkB,MAAM,EACd2H,MAAM,CAACzR,KAAK,CAACE,MAAM,CAACmQ,aAAa,EACjC7I,UACF,CAAC,EACD;YACEwB,MAAM,EAAEhP,4BAA4B;YACpC6P;UACF,CACF,CAAC;QACH;MACF;IACF,CAAC,MAAM,IAAI,CAACzM,SAAS,IAAII,OAAO,IAAIA,OAAO,CAACA,OAAO,CAAC2G,MAAM,GAAG,CAAC,CAAC,CAACzG,KAAK,CAACwC,MAAM,CAAC8B,OAAO,IAAI,IAAI,IAAIxE,OAAO,CAACA,OAAO,CAAC2G,MAAM,GAAG,CAAC,CAAC,CAACzG,KAAK,CAACwC,MAAM,CAACc,aAAa,IAAI,IAAI,EAAE;MAC9JwR,QAAQ,GAAG,MAAMI,qBAAqB,CACpCpL,UAAU,EACViK,MAAM,EACNjD,aAAa,EACbhR,OAAO,CAACgG,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC9F,KAAK,CAACC,EAAE,EAC7BiL,OAAO,EACP8F,WAAW,EACXC,WACF,CAAC;IACH,CAAC,MAAM;MACL,IAAI;QAAEpG;MAAS,CAAC,GAAG5L,GAAG;MACtB,IAAIK,WAAW,GAAG,KAAK,CAAC;MACxB,IAAIyU,MAAM,CAACoB,uBAAuB,EAAE;QAClC7V,WAAW,GAAG,MAAMyU,MAAM,CAACoB,uBAAuB,CAAC;UAAEtK;QAAS,CAAC,CAAC;MAClE,CAAC,MAAM,IAAInI,IAAI,KAAK,aAAa,CAAC,qBAAqBoK,iBAAiB,CAAC,CAAC,EAAEsI,cAAc,EAAE;QAC1F9V,WAAW,GAAG,MAAMwN,iBAAiB,CAAC,CAAC,EAAEsI,cAAc,GAAGvK,QAAQ,CAAC;MACrE;MACAiK,QAAQ,GAAG,MAAMO,qBAAqB,CACpCvL,UAAU,EACViK,MAAM,EACNjD,aAAa,EACb5F,OAAO,EACP8F,WAAW,EACXC,WAAW,EACXvR,SAAS,EACTJ,WACF,CAAC;IACH;IACA,IAAI4L,OAAO,CAACe,MAAM,KAAK,MAAM,EAAE;MAC7B,OAAO,IAAIV,QAAQ,CAAC,IAAI,EAAE;QACxBY,OAAO,EAAE2I,QAAQ,CAAC3I,OAAO;QACzBb,MAAM,EAAEwJ,QAAQ,CAACxJ,MAAM;QACvBiI,UAAU,EAAEuB,QAAQ,CAACvB;MACvB,CAAC,CAAC;IACJ;IACA,OAAOuB,QAAQ;EACjB,CAAC;AACH,CAAC;AACD,eAAeD,qBAAqBA,CAAC3F,KAAK,EAAE1P,MAAM,EAAEP,GAAG,EAAE;EACvD,IAAIiQ,KAAK,CAACoG,MAAM,CAAC7S,OAAO,KAAKxD,GAAG,CAACwM,YAAY,CAAC2B,GAAG,CAAC,SAAS,CAAC,EAAE;IAC5D,OAAO,IAAI7B,QAAQ,CAAC,IAAI,EAAE;MACxBD,MAAM,EAAE,GAAG;MACXa,OAAO,EAAE;QACP,yBAAyB,EAAE;MAC7B;IACF,CAAC,CAAC;EACJ;EACA,IAAIoJ,OAAO,GAAG,CAAC,CAAC;EAChB,IAAItW,GAAG,CAACwM,YAAY,CAACkF,GAAG,CAAC,GAAG,CAAC,EAAE;IAC7B,IAAI6E,KAAK,GAAG,eAAgB,IAAI/E,GAAG,CAAC,CAAC;IACrCxR,GAAG,CAACwM,YAAY,CAACC,MAAM,CAAC,GAAG,CAAC,CAAC8B,OAAO,CAAErK,IAAI,IAAK;MAC7C,IAAI,CAACA,IAAI,CAACsS,UAAU,CAAC,GAAG,CAAC,EAAE;QACzBtS,IAAI,GAAG,IAAIA,IAAI,EAAE;MACnB;MACA,IAAIuS,QAAQ,GAAGvS,IAAI,CAACkP,KAAK,CAAC,GAAG,CAAC,CAACvM,KAAK,CAAC,CAAC,CAAC;MACvC4P,QAAQ,CAAClI,OAAO,CAAC,CAACmI,CAAC,EAAEjP,CAAC,KAAK;QACzB,IAAIkP,WAAW,GAAGF,QAAQ,CAAC5P,KAAK,CAAC,CAAC,EAAEY,CAAC,GAAG,CAAC,CAAC,CAACmP,IAAI,CAAC,GAAG,CAAC;QACpDL,KAAK,CAACM,GAAG,CAAC,IAAIF,WAAW,EAAE,CAAC;MAC9B,CAAC,CAAC;IACJ,CAAC,CAAC;IACF,KAAK,IAAIzS,IAAI,IAAIqS,KAAK,EAAE;MACtB,IAAI1V,OAAO,GAAG8K,iBAAiB,CAACpL,MAAM,EAAE2D,IAAI,EAAE+L,KAAK,CAACpE,QAAQ,CAAC;MAC7D,IAAIhL,OAAO,EAAE;QACX,KAAK,IAAID,KAAK,IAAIC,OAAO,EAAE;UACzB,IAAIC,OAAO,GAAGF,KAAK,CAACG,KAAK,CAACC,EAAE;UAC5B,IAAID,KAAK,GAAGkP,KAAK,CAACoG,MAAM,CAAC9V,MAAM,CAACO,OAAO,CAAC;UACxC,IAAIC,KAAK,EAAE;YACTuV,OAAO,CAACxV,OAAO,CAAC,GAAGC,KAAK;UAC1B;QACF;MACF;IACF;IACA,OAAOuL,QAAQ,CAACwK,IAAI,CAACR,OAAO,EAAE;MAC5BpJ,OAAO,EAAE;QACP,eAAe,EAAE;MACnB;IACF,CAAC,CAAC;EACJ;EACA,OAAO,IAAIZ,QAAQ,CAAC,iBAAiB,EAAE;IAAED,MAAM,EAAE;EAAI,CAAC,CAAC;AACzD;AACA,eAAe0J,wBAAwBA,CAAClL,UAAU,EAAEoF,KAAK,EAAE4B,aAAa,EAAE5F,OAAO,EAAE6F,UAAU,EAAEC,WAAW,EAAEC,WAAW,EAAE;EACvH,IAAI6D,QAAQ,GAAG5J,OAAO,CAACe,MAAM,KAAK,KAAK,GAAG,MAAM4E,iBAAiB,CAC/D3B,KAAK,EACLpF,UAAU,EACVgH,aAAa,EACb5F,OAAO,EACP6F,UAAU,EACVC,WAAW,EACXC,WACF,CAAC,GAAG,MAAMc,kBAAkB,CAC1B7C,KAAK,EACLpF,UAAU,EACVgH,aAAa,EACb5F,OAAO,EACP6F,UAAU,EACVC,WAAW,EACXC,WACF,CAAC;EACD,OAAO6D,QAAQ;AACjB;AACA,eAAeO,qBAAqBA,CAACvL,UAAU,EAAEoF,KAAK,EAAE4B,aAAa,EAAE5F,OAAO,EAAE8F,WAAW,EAAEC,WAAW,EAAEvR,SAAS,EAAEJ,WAAW,EAAE;EAChI,IAAI;IACF,IAAIwV,QAAQ,GAAG,MAAMhE,aAAa,CAACY,KAAK,CAACxG,OAAO,EAAE;MAChDyG,cAAc,EAAEX,WAAW;MAC3Bc,gBAAgB,EAAE5C,KAAK,CAACzP,MAAM,CAAC4C,mBAAmB,GAAI2T,GAAG,IAAKC,UAAU,CAACD,GAAG,EAAEtW,SAAS,CAAC,GAAG,KAAK;IAClG,CAAC,CAAC;IACF,OAAO9B,UAAU,CAACkX,QAAQ,CAAC,GAAGA,QAAQ,GAAGmB,UAAU,CAACnB,QAAQ,EAAEpV,SAAS,CAAC;EAC1E,CAAC,CAAC,OAAOyG,KAAK,EAAE;IACd8K,WAAW,CAAC9K,KAAK,CAAC;IAClB,OAAO,IAAIoF,QAAQ,CAAC,IAAI,EAAE;MAAED,MAAM,EAAE;IAAI,CAAC,CAAC;EAC5C;EACA,eAAe2K,UAAUA,CAACjX,OAAO,EAAEkX,UAAU,EAAE;IAC7C,IAAItY,UAAU,CAACoB,OAAO,CAAC,EAAE;MACvB,OAAOA,OAAO;IAChB;IACA,IAAImN,OAAO,GAAG8C,kBAAkB,CAACjQ,OAAO,EAAEkQ,KAAK,CAAC;IAChD,IAAI0B,2BAA2B,CAACD,GAAG,CAAC3R,OAAO,CAACmS,UAAU,CAAC,EAAE;MACvD,OAAO,IAAI5F,QAAQ,CAAC,IAAI,EAAE;QAAED,MAAM,EAAEtM,OAAO,CAACmS,UAAU;QAAEhF;MAAQ,CAAC,CAAC;IACpE;IACA,IAAInN,OAAO,CAACkL,MAAM,EAAE;MAClBZ,MAAM,CAACiE,MAAM,CAACvO,OAAO,CAACkL,MAAM,CAAC,CAACsD,OAAO,CAAE8D,GAAG,IAAK;QAC7C,IAAI,CAACzT,oBAAoB,CAACyT,GAAG,CAAC,IAAIA,GAAG,CAACnL,KAAK,EAAE;UAC3C8K,WAAW,CAACK,GAAG,CAAC;QAClB;MACF,CAAC,CAAC;MACFtS,OAAO,CAACkL,MAAM,GAAGD,cAAc,CAACjL,OAAO,CAACkL,MAAM,EAAEJ,UAAU,CAAC;IAC7D;IACA,IAAI9I,KAAK,GAAG;MACVpB,UAAU,EAAEZ,OAAO,CAACY,UAAU;MAC9B8P,UAAU,EAAE1Q,OAAO,CAAC0Q,UAAU;MAC9BxF,MAAM,EAAEK,eAAe,CAACvL,OAAO,CAACkL,MAAM,EAAEJ,UAAU;IACpD,CAAC;IACD,IAAIqM,iBAAiB,GAAG;MACtBrL,QAAQ,EAAEoE,KAAK,CAACpE,QAAQ;MACxBrL,MAAM,EAAEyP,KAAK,CAACzP,MAAM;MACpBmB,cAAc,EAAEsO,KAAK,CAACtO,cAAc;MACpCD,GAAG,EAAEuO,KAAK,CAACvO,GAAG;MACdjB,SAAS,EAAEwW;IACb,CAAC;IACD,IAAIE,YAAY,GAAG;MACjBhX,QAAQ,EAAE8P,KAAK,CAACoG,MAAM;MACtBjW,YAAY,EAAEgK,uBAAuB,CAAC6F,KAAK,CAAC1P,MAAM,CAAC;MACnDG,oBAAoB,EAAEX,OAAO;MAC7BM,WAAW;MACXC,mBAAmB,EAAEuP,yBAAyB,CAAC;QAC7C,GAAGqH,iBAAiB;QACpB7W;MACF,CAAC,CAAC;MACF4B,mBAAmB,EAAEwR,oBAAoB,CACvC1R,KAAK,EACLkK,OAAO,CAACkB,MAAM,EACd8C,KAAK,CAAC5M,KAAK,CAACE,MAAM,CAACmQ,aAAa,EAChC7I,UACF,CAAC;MACDhJ,UAAU,EAAE,CAAC,CAAC;MACdrB,MAAM,EAAEyP,KAAK,CAACzP,MAAM;MACpBkB,GAAG,EAAEuO,KAAK,CAACvO,GAAG;MACdC,cAAc,EAAEsO,KAAK,CAACtO,cAAc;MACpClB,SAAS,EAAEwW,UAAU;MACrBrV,cAAc,EAAGyQ,GAAG,IAAKzQ,cAAc,CAACyQ,GAAG,EAAExH,UAAU;IACzD,CAAC;IACD,IAAIuM,6BAA6B,GAAGnH,KAAK,CAAC5M,KAAK,CAACE,MAAM,CAAC8B,OAAO;IAC9D,IAAI;MACF,OAAO,MAAM+R,6BAA6B,CACxCnL,OAAO,EACPlM,OAAO,CAACmS,UAAU,EAClBhF,OAAO,EACPiK,YAAY,EACZpF,WACF,CAAC;IACH,CAAC,CAAC,OAAO7K,KAAK,EAAE;MACd8K,WAAW,CAAC9K,KAAK,CAAC;MAClB,IAAImQ,oBAAoB,GAAGnQ,KAAK;MAChC,IAAIvI,UAAU,CAACuI,KAAK,CAAC,EAAE;QACrB,IAAI;UACF,IAAInB,KAAK,GAAG,MAAMuR,cAAc,CAACpQ,KAAK,CAAC;UACvCmQ,oBAAoB,GAAG,IAAIva,iBAAiB,CAC1CoK,KAAK,CAACmF,MAAM,EACZnF,KAAK,CAACoN,UAAU,EAChBvO,KACF,CAAC;QACH,CAAC,CAAC,OAAOqI,CAAC,EAAE,CACZ;MACF;MACArO,OAAO,GAAG1B,yBAAyB,CACjCwT,aAAa,CAAC6C,UAAU,EACxB3U,OAAO,EACPsX,oBACF,CAAC;MACD,IAAItX,OAAO,CAACkL,MAAM,EAAE;QAClBlL,OAAO,CAACkL,MAAM,GAAGD,cAAc,CAACjL,OAAO,CAACkL,MAAM,EAAEJ,UAAU,CAAC;MAC7D;MACA,IAAI0M,MAAM,GAAG;QACX5W,UAAU,EAAEZ,OAAO,CAACY,UAAU;QAC9B8P,UAAU,EAAE1Q,OAAO,CAAC0Q,UAAU;QAC9BxF,MAAM,EAAEK,eAAe,CAACvL,OAAO,CAACkL,MAAM,EAAEJ,UAAU;MACpD,CAAC;MACDsM,YAAY,GAAG;QACb,GAAGA,YAAY;QACfzW,oBAAoB,EAAEX,OAAO;QAC7BO,mBAAmB,EAAEuP,yBAAyB,CAACqH,iBAAiB,CAAC;QACjEjV,mBAAmB,EAAEwR,oBAAoB,CACvC8D,MAAM,EACNtL,OAAO,CAACkB,MAAM,EACd8C,KAAK,CAAC5M,KAAK,CAACE,MAAM,CAACmQ,aAAa,EAChC7I,UACF,CAAC;QACDhJ,UAAU,EAAE,CAAC;MACf,CAAC;MACD,IAAI;QACF,OAAO,MAAMuV,6BAA6B,CACxCnL,OAAO,EACPlM,OAAO,CAACmS,UAAU,EAClBhF,OAAO,EACPiK,YAAY,EACZpF,WACF,CAAC;MACH,CAAC,CAAC,OAAOyF,MAAM,EAAE;QACfxF,WAAW,CAACwF,MAAM,CAAC;QACnB,OAAOrC,6BAA6B,CAACqC,MAAM,EAAE3M,UAAU,CAAC;MAC1D;IACF;EACF;AACF;AACA,eAAeoL,qBAAqBA,CAACpL,UAAU,EAAEoF,KAAK,EAAE4B,aAAa,EAAE/Q,OAAO,EAAEmL,OAAO,EAAE8F,WAAW,EAAEC,WAAW,EAAE;EACjH,IAAI;IACF,IAAI6D,QAAQ,GAAG,MAAMhE,aAAa,CAAC4F,UAAU,CAACxL,OAAO,EAAE;MACrDnL,OAAO;MACP4R,cAAc,EAAEX,WAAW;MAC3Bc,gBAAgB,EAAE5C,KAAK,CAACzP,MAAM,CAAC4C,mBAAmB,GAAI2T,GAAG,IAAKA,GAAG,GAAG,KAAK;IAC3E,CAAC,CAAC;IACF,IAAIpY,UAAU,CAACkX,QAAQ,CAAC,EAAE;MACxB,OAAOA,QAAQ;IACjB;IACA,IAAI,OAAOA,QAAQ,KAAK,QAAQ,EAAE;MAChC,OAAO,IAAIvJ,QAAQ,CAACuJ,QAAQ,CAAC;IAC/B;IACA,OAAOvJ,QAAQ,CAACwK,IAAI,CAACjB,QAAQ,CAAC;EAChC,CAAC,CAAC,OAAO3O,KAAK,EAAE;IACd,IAAIvI,UAAU,CAACuI,KAAK,CAAC,EAAE;MACrBA,KAAK,CAACgG,OAAO,CAACsG,GAAG,CAAC,eAAe,EAAE,KAAK,CAAC;MACzC,OAAOtM,KAAK;IACd;IACA,IAAItI,oBAAoB,CAACsI,KAAK,CAAC,EAAE;MAC/B,IAAIA,KAAK,EAAE;QACT8K,WAAW,CAAC9K,KAAK,CAAC;MACpB;MACA,OAAOwQ,mBAAmB,CAACxQ,KAAK,EAAE2D,UAAU,CAAC;IAC/C;IACA,IAAI3D,KAAK,YAAYlD,KAAK,IAAIkD,KAAK,CAACmE,OAAO,KAAK,qCAAqC,EAAE;MACrF,IAAIsM,QAAQ,GAAG,IAAI3T,KAAK,CACtB,gEACF,CAAC;MACDgO,WAAW,CAAC2F,QAAQ,CAAC;MACrB,OAAOxC,6BAA6B,CAACwC,QAAQ,EAAE9M,UAAU,CAAC;IAC5D;IACAmH,WAAW,CAAC9K,KAAK,CAAC;IAClB,OAAOiO,6BAA6B,CAACjO,KAAK,EAAE2D,UAAU,CAAC;EACzD;AACF;AACA,SAAS6M,mBAAmBA,CAACE,aAAa,EAAE/M,UAAU,EAAE;EACtD,OAAOyB,QAAQ,CAACwK,IAAI,CAClBlV,cAAc;EACZ;EACAgW,aAAa,CAAC1Q,KAAK,IAAI,IAAIlD,KAAK,CAAC,yBAAyB,CAAC,EAC3D6G,UACF,CAAC,EACD;IACEwB,MAAM,EAAEuL,aAAa,CAACvL,MAAM;IAC5BiI,UAAU,EAAEsD,aAAa,CAACtD,UAAU;IACpCpH,OAAO,EAAE;MACP,eAAe,EAAE;IACnB;EACF,CACF,CAAC;AACH;AACA,SAASiI,6BAA6BA,CAACjO,KAAK,EAAE2D,UAAU,EAAE;EACxD,IAAIQ,OAAO,GAAG,yBAAyB;EACvC,IAAIR,UAAU,KAAK,YAAY,CAAC,kBAAkB;IAChDQ,OAAO,IAAI;AACf;AACA,EAAE9E,MAAM,CAACW,KAAK,CAAC,EAAE;EACf;EACA,OAAO,IAAIoF,QAAQ,CAACjB,OAAO,EAAE;IAC3BgB,MAAM,EAAE,GAAG;IACXa,OAAO,EAAE;MACP,cAAc,EAAE;IAClB;EACF,CAAC,CAAC;AACJ;AACA,SAASoK,cAAcA,CAACzB,QAAQ,EAAE;EAChC,IAAIgC,WAAW,GAAGhC,QAAQ,CAAC3I,OAAO,CAACiB,GAAG,CAAC,cAAc,CAAC;EACtD,OAAO0J,WAAW,IAAI,uBAAuB,CAACC,IAAI,CAACD,WAAW,CAAC,GAAGhC,QAAQ,CAAC5I,IAAI,IAAI,IAAI,GAAG,IAAI,GAAG4I,QAAQ,CAACiB,IAAI,CAAC,CAAC,GAAGjB,QAAQ,CAACkC,IAAI,CAAC,CAAC;AACpI;;AAEA;AACA,SAASC,KAAKA,CAAC3Q,IAAI,EAAE;EACnB,OAAO,WAAWA,IAAI,IAAI;AAC5B;AACA,IAAI4Q,aAAa,GAAGA,CAACC,WAAW,GAAG,CAAC,CAAC,EAAElX,EAAE,GAAG,EAAE,KAAK;EACjD,IAAI+C,GAAG,GAAG,IAAIoU,GAAG,CAAC9N,MAAM,CAACa,OAAO,CAACgN,WAAW,CAAC,CAAC;EAC9C,OAAO;IACL,IAAIlX,EAAEA,CAAA,EAAG;MACP,OAAOA,EAAE;IACX,CAAC;IACD,IAAIuO,IAAIA,CAAA,EAAG;MACT,OAAOlF,MAAM,CAACmK,WAAW,CAACzQ,GAAG,CAAC;IAChC,CAAC;IACD2N,GAAGA,CAACrK,IAAI,EAAE;MACR,OAAOtD,GAAG,CAAC2N,GAAG,CAACrK,IAAI,CAAC,IAAItD,GAAG,CAAC2N,GAAG,CAACsG,KAAK,CAAC3Q,IAAI,CAAC,CAAC;IAC9C,CAAC;IACD8G,GAAGA,CAAC9G,IAAI,EAAE;MACR,IAAItD,GAAG,CAAC2N,GAAG,CAACrK,IAAI,CAAC,EAAE,OAAOtD,GAAG,CAACoK,GAAG,CAAC9G,IAAI,CAAC;MACvC,IAAI+Q,SAAS,GAAGJ,KAAK,CAAC3Q,IAAI,CAAC;MAC3B,IAAItD,GAAG,CAAC2N,GAAG,CAAC0G,SAAS,CAAC,EAAE;QACtB,IAAI3W,KAAK,GAAGsC,GAAG,CAACoK,GAAG,CAACiK,SAAS,CAAC;QAC9BrU,GAAG,CAAC2I,MAAM,CAAC0L,SAAS,CAAC;QACrB,OAAO3W,KAAK;MACd;MACA,OAAO,KAAK,CAAC;IACf,CAAC;IACD+R,GAAGA,CAACnM,IAAI,EAAE5F,KAAK,EAAE;MACfsC,GAAG,CAACyP,GAAG,CAACnM,IAAI,EAAE5F,KAAK,CAAC;IACtB,CAAC;IACDuW,KAAKA,CAAC3Q,IAAI,EAAE5F,KAAK,EAAE;MACjBsC,GAAG,CAACyP,GAAG,CAACwE,KAAK,CAAC3Q,IAAI,CAAC,EAAE5F,KAAK,CAAC;IAC7B,CAAC;IACD4W,KAAKA,CAAChR,IAAI,EAAE;MACVtD,GAAG,CAAC2I,MAAM,CAACrF,IAAI,CAAC;IAClB;EACF,CAAC;AACH,CAAC;AACD,IAAIiR,SAAS,GAAIxP,MAAM,IAAK;EAC1B,OAAOA,MAAM,IAAI,IAAI,IAAI,OAAOA,MAAM,CAAC9H,EAAE,KAAK,QAAQ,IAAI,OAAO8H,MAAM,CAACyG,IAAI,KAAK,WAAW,IAAI,OAAOzG,MAAM,CAAC4I,GAAG,KAAK,UAAU,IAAI,OAAO5I,MAAM,CAACqF,GAAG,KAAK,UAAU,IAAI,OAAOrF,MAAM,CAAC0K,GAAG,KAAK,UAAU,IAAI,OAAO1K,MAAM,CAACkP,KAAK,KAAK,UAAU,IAAI,OAAOlP,MAAM,CAACuP,KAAK,KAAK,UAAU;AACtR,CAAC;AACD,SAASE,oBAAoBA,CAAC;EAC5B5R,MAAM,EAAE6R,SAAS;EACjBC,UAAU;EACVC,QAAQ;EACRC,UAAU;EACVC;AACF,CAAC,EAAE;EACD,IAAIjS,MAAM,GAAGkC,QAAQ,CAAC2P,SAAS,CAAC,GAAGA,SAAS,GAAG7Q,YAAY,CAAC6Q,SAAS,EAAEnR,IAAI,IAAI,WAAW,EAAEmR,SAAS,CAAC;EACtGK,iCAAiC,CAAClS,MAAM,CAAC;EACzC,OAAO;IACL,MAAMmS,UAAUA,CAACxQ,YAAY,EAAER,OAAO,EAAE;MACtC,IAAI9G,EAAE,GAAGsH,YAAY,KAAI,MAAM3B,MAAM,CAAClB,KAAK,CAAC6C,YAAY,EAAER,OAAO,CAAC;MAClE,IAAI/B,KAAK,GAAG/E,EAAE,KAAI,MAAM0X,QAAQ,CAAC1X,EAAE,CAAC;MACpC,OAAOiX,aAAa,CAAClS,KAAK,IAAI,CAAC,CAAC,EAAE/E,EAAE,IAAI,EAAE,CAAC;IAC7C,CAAC;IACD,MAAM+X,aAAaA,CAACC,OAAO,EAAElR,OAAO,EAAE;MACpC,IAAI;QAAE9G,EAAE;QAAEuO,IAAI,EAAExJ;MAAM,CAAC,GAAGiT,OAAO;MACjC,IAAI/Q,OAAO,GAAGH,OAAO,EAAEK,MAAM,IAAI,IAAI,GAAG,IAAIC,IAAI,CAACA,IAAI,CAACC,GAAG,CAAC,CAAC,GAAGP,OAAO,CAACK,MAAM,GAAG,GAAG,CAAC,GAAGL,OAAO,EAAEG,OAAO,IAAI,IAAI,GAAGH,OAAO,CAACG,OAAO,GAAGtB,MAAM,CAACsB,OAAO;MACjJ,IAAIjH,EAAE,EAAE;QACN,MAAM2X,UAAU,CAAC3X,EAAE,EAAE+E,KAAK,EAAEkC,OAAO,CAAC;MACtC,CAAC,MAAM;QACLjH,EAAE,GAAG,MAAMyX,UAAU,CAAC1S,KAAK,EAAEkC,OAAO,CAAC;MACvC;MACA,OAAOtB,MAAM,CAACjB,SAAS,CAAC1E,EAAE,EAAE8G,OAAO,CAAC;IACtC,CAAC;IACD,MAAMmR,cAAcA,CAACD,OAAO,EAAElR,OAAO,EAAE;MACrC,MAAM8Q,UAAU,CAACI,OAAO,CAAChY,EAAE,CAAC;MAC5B,OAAO2F,MAAM,CAACjB,SAAS,CAAC,EAAE,EAAE;QAC1B,GAAGoC,OAAO;QACVK,MAAM,EAAE,KAAK,CAAC;QACdF,OAAO,EAAE,eAAgB,IAAIG,IAAI,CAAC,CAAC;MACrC,CAAC,CAAC;IACJ;EACF,CAAC;AACH;AACA,SAASyQ,iCAAiCA,CAAClS,MAAM,EAAE;EACjDlH,QAAQ,CACNkH,MAAM,CAACuB,QAAQ,EACf,QAAQvB,MAAM,CAACU,IAAI,6OACrB,CAAC;AACH;;AAEA;AACA,SAAS6R,0BAA0BA,CAAC;EAAEvS,MAAM,EAAE6R;AAAU,CAAC,GAAG,CAAC,CAAC,EAAE;EAC9D,IAAI7R,MAAM,GAAGkC,QAAQ,CAAC2P,SAAS,CAAC,GAAGA,SAAS,GAAG7Q,YAAY,CAAC6Q,SAAS,EAAEnR,IAAI,IAAI,WAAW,EAAEmR,SAAS,CAAC;EACtGK,iCAAiC,CAAClS,MAAM,CAAC;EACzC,OAAO;IACL,MAAMmS,UAAUA,CAACxQ,YAAY,EAAER,OAAO,EAAE;MACtC,OAAOmQ,aAAa,CAClB3P,YAAY,KAAI,MAAM3B,MAAM,CAAClB,KAAK,CAAC6C,YAAY,EAAER,OAAO,CAAC,KAAI,CAAC,CAChE,CAAC;IACH,CAAC;IACD,MAAMiR,aAAaA,CAACC,OAAO,EAAElR,OAAO,EAAE;MACpC,IAAIqR,gBAAgB,GAAG,MAAMxS,MAAM,CAACjB,SAAS,CAACsT,OAAO,CAACzJ,IAAI,EAAEzH,OAAO,CAAC;MACpE,IAAIqR,gBAAgB,CAAC3R,MAAM,GAAG,IAAI,EAAE;QAClC,MAAM,IAAIxD,KAAK,CACb,qDAAqD,GAAGmV,gBAAgB,CAAC3R,MAC3E,CAAC;MACH;MACA,OAAO2R,gBAAgB;IACzB,CAAC;IACD,MAAMF,cAAcA,CAACG,QAAQ,EAAEtR,OAAO,EAAE;MACtC,OAAOnB,MAAM,CAACjB,SAAS,CAAC,EAAE,EAAE;QAC1B,GAAGoC,OAAO;QACVK,MAAM,EAAE,KAAK,CAAC;QACdF,OAAO,EAAE,eAAgB,IAAIG,IAAI,CAAC,CAAC;MACrC,CAAC,CAAC;IACJ;EACF,CAAC;AACH;;AAEA;AACA,SAASiR,0BAA0BA,CAAC;EAAE1S;AAAO,CAAC,GAAG,CAAC,CAAC,EAAE;EACnD,IAAI5C,GAAG,GAAG,eAAgB,IAAIoU,GAAG,CAAC,CAAC;EACnC,OAAOI,oBAAoB,CAAC;IAC1B5R,MAAM;IACN,MAAM8R,UAAUA,CAAC1S,KAAK,EAAEkC,OAAO,EAAE;MAC/B,IAAIjH,EAAE,GAAGsY,IAAI,CAACC,MAAM,CAAC,CAAC,CAAC7P,QAAQ,CAAC,EAAE,CAAC,CAAC8P,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC;MACpDzV,GAAG,CAACyP,GAAG,CAACxS,EAAE,EAAE;QAAEuO,IAAI,EAAExJ,KAAK;QAAEkC;MAAQ,CAAC,CAAC;MACrC,OAAOjH,EAAE;IACX,CAAC;IACD,MAAM0X,QAAQA,CAAC1X,EAAE,EAAE;MACjB,IAAI+C,GAAG,CAAC2N,GAAG,CAAC1Q,EAAE,CAAC,EAAE;QACf,IAAI;UAAEuO,IAAI,EAAExJ,KAAK;UAAEkC;QAAQ,CAAC,GAAGlE,GAAG,CAACoK,GAAG,CAACnN,EAAE,CAAC;QAC1C,IAAI,CAACiH,OAAO,IAAIA,OAAO,GAAG,eAAgB,IAAIG,IAAI,CAAC,CAAC,EAAE;UACpD,OAAOrC,KAAK;QACd;QACA,IAAIkC,OAAO,EAAElE,GAAG,CAAC2I,MAAM,CAAC1L,EAAE,CAAC;MAC7B;MACA,OAAO,IAAI;IACb,CAAC;IACD,MAAM2X,UAAUA,CAAC3X,EAAE,EAAE+E,KAAK,EAAEkC,OAAO,EAAE;MACnClE,GAAG,CAACyP,GAAG,CAACxS,EAAE,EAAE;QAAEuO,IAAI,EAAExJ,KAAK;QAAEkC;MAAQ,CAAC,CAAC;IACvC,CAAC;IACD,MAAM2Q,UAAUA,CAAC5X,EAAE,EAAE;MACnB+C,GAAG,CAAC2I,MAAM,CAAC1L,EAAE,CAAC;IAChB;EACF,CAAC,CAAC;AACJ;;AAEA;AACA,SAASsM,IAAIA,CAACpJ,IAAI,EAAE,GAAGK,IAAI,EAAE;EAC3B,IAAIuH,MAAM,GAAGvH,IAAI,CAAC,CAAC,CAAC;EACpB,OAAOL,IAAI,CAACkP,KAAK,CAAC,GAAG,CAAC,CAACrP,GAAG,CAAE0V,OAAO,IAAK;IACtC,IAAIA,OAAO,KAAK,GAAG,EAAE;MACnB,OAAO3N,MAAM,GAAGA,MAAM,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC;IACtC;IACA,MAAMlL,KAAK,GAAG6Y,OAAO,CAAC7Y,KAAK,CAAC,iBAAiB,CAAC;IAC9C,IAAI,CAACA,KAAK,EAAE,OAAO6Y,OAAO;IAC1B,MAAMC,KAAK,GAAG9Y,KAAK,CAAC,CAAC,CAAC;IACtB,MAAMa,KAAK,GAAGqK,MAAM,GAAGA,MAAM,CAAC4N,KAAK,CAAC,GAAG,KAAK,CAAC;IAC7C,MAAMC,UAAU,GAAG/Y,KAAK,CAAC,CAAC,CAAC,KAAK,KAAK,CAAC;IACtC,IAAI+Y,UAAU,IAAIlY,KAAK,KAAK,KAAK,CAAC,EAAE;MAClC,MAAMuC,KAAK,CACT,SAASE,IAAI,qBAAqBwV,KAAK,2BACzC,CAAC;IACH;IACA,OAAOjY,KAAK;EACd,CAAC,CAAC,CAACwR,MAAM,CAAEwG,OAAO,IAAKA,OAAO,KAAK,KAAK,CAAC,CAAC,CAAC7C,IAAI,CAAC,GAAG,CAAC;AACtD;;AAEA;AACA,OAAO,KAAKgD,MAAM,MAAM,OAAO;AAC/B,OAAO,KAAKC,QAAQ,MAAM,WAAW;;AAErC;AACA,SAASC,gBAAgBA,CAAC/X,KAAK,EAAExB,MAAM,EAAEwZ,YAAY,EAAEC,SAAS,EAAEnO,QAAQ,EAAEpL,SAAS,EAAE;EACrF,IAAIqC,aAAa,GAAG;IAClB,GAAGf,KAAK;IACRpB,UAAU,EAAE;MAAE,GAAGoB,KAAK,CAACpB;IAAW;EACpC,CAAC;EACD,IAAIsZ,cAAc,GAAGpb,WAAW,CAAC0B,MAAM,EAAEyZ,SAAS,EAAEnO,QAAQ,CAAC;EAC7D,IAAIoO,cAAc,EAAE;IAClB,KAAK,IAAIrZ,KAAK,IAAIqZ,cAAc,EAAE;MAChC,IAAInZ,OAAO,GAAGF,KAAK,CAACG,KAAK,CAACC,EAAE;MAC5B,IAAIkZ,SAAS,GAAGH,YAAY,CAACjZ,OAAO,CAAC;MACrC,IAAI5B,wBAAwB,CAC1B4B,OAAO,EACPoZ,SAAS,CAAChZ,YAAY,EACtBgZ,SAAS,CAAC/Y,SAAS,EACnBV,SACF,CAAC,KAAKyZ,SAAS,CAACC,kBAAkB,IAAI,CAACD,SAAS,CAAC/Y,SAAS,CAAC,EAAE;QAC3D,OAAO2B,aAAa,CAACnC,UAAU,CAACG,OAAO,CAAC;MAC1C,CAAC,MAAM,IAAI,CAACoZ,SAAS,CAAC/Y,SAAS,EAAE;QAC/B2B,aAAa,CAACnC,UAAU,CAACG,OAAO,CAAC,GAAG,IAAI;MAC1C;IACF;EACF;EACA,OAAOgC,aAAa;AACtB;;AAEA;AACA,OAAOsX,MAAM,MAAM,OAAO;AAC1B,IAAIC,4BAA4B,GAAG,cAAcD,MAAM,CAAChW,SAAS,CAAC;EAChEkW,WAAWA,CAACC,KAAK,EAAE;IACjB,KAAK,CAACA,KAAK,CAAC;IACZ,IAAI,CAACxY,KAAK,GAAG;MAAEmF,KAAK,EAAE,IAAI;MAAEpF,QAAQ,EAAEyY,KAAK,CAACzY;IAAS,CAAC;EACxD;EACA,OAAO0Y,wBAAwBA,CAACtT,KAAK,EAAE;IACrC,OAAO;MAAEA;IAAM,CAAC;EAClB;EACA,OAAOuT,wBAAwBA,CAACF,KAAK,EAAExY,KAAK,EAAE;IAC5C,IAAIA,KAAK,CAACD,QAAQ,KAAKyY,KAAK,CAACzY,QAAQ,EAAE;MACrC,OAAO;QAAEoF,KAAK,EAAE,IAAI;QAAEpF,QAAQ,EAAEyY,KAAK,CAACzY;MAAS,CAAC;IAClD;IACA,OAAO;MAAEoF,KAAK,EAAEnF,KAAK,CAACmF,KAAK;MAAEpF,QAAQ,EAAEC,KAAK,CAACD;IAAS,CAAC;EACzD;EACA4Y,MAAMA,CAAA,EAAG;IACP,IAAI,IAAI,CAAC3Y,KAAK,CAACmF,KAAK,EAAE;MACpB,OAAO,eAAgBkT,MAAM,CAAC9Y,aAAa,CACzCqZ,+BAA+B,EAC/B;QACEzT,KAAK,EAAE,IAAI,CAACnF,KAAK,CAACmF,KAAK;QACvB0T,cAAc,EAAE;MAClB,CACF,CAAC;IACH,CAAC,MAAM;MACL,OAAO,IAAI,CAACL,KAAK,CAAC/U,QAAQ;IAC5B;EACF;AACF,CAAC;AACD,SAASqV,YAAYA,CAAC;EACpBD,cAAc;EACdE,KAAK;EACLtV;AACF,CAAC,EAAE;EACD,IAAI,CAACoV,cAAc,EAAE;IACnB,OAAOpV,QAAQ;EACjB;EACA,OAAO,eAAgB4U,MAAM,CAAC9Y,aAAa,CAAC,MAAM,EAAE;IAAEyZ,IAAI,EAAE;EAAK,CAAC,EAAE,eAAgBX,MAAM,CAAC9Y,aAAa,CAAC,MAAM,EAAE,IAAI,EAAE,eAAgB8Y,MAAM,CAAC9Y,aAAa,CAAC,MAAM,EAAE;IAAE0Z,OAAO,EAAE;EAAQ,CAAC,CAAC,EAAE,eAAgBZ,MAAM,CAAC9Y,aAAa,CAC7N,MAAM,EACN;IACE+F,IAAI,EAAE,UAAU;IAChB4T,OAAO,EAAE;EACX,CACF,CAAC,EAAE,eAAgBb,MAAM,CAAC9Y,aAAa,CAAC,OAAO,EAAE,IAAI,EAAEwZ,KAAK,CAAC,CAAC,EAAE,eAAgBV,MAAM,CAAC9Y,aAAa,CAAC,MAAM,EAAE,IAAI,EAAE,eAAgB8Y,MAAM,CAAC9Y,aAAa,CAAC,MAAM,EAAE;IAAE4Z,KAAK,EAAE;MAAEC,UAAU,EAAE,uBAAuB;MAAEC,OAAO,EAAE;IAAO;EAAE,CAAC,EAAE5V,QAAQ,CAAC,CAAC,CAAC;AAClP;AACA,SAASmV,+BAA+BA,CAAC;EACvCzT,KAAK;EACL0T;AACF,CAAC,EAAE;EACDpN,OAAO,CAACtG,KAAK,CAACA,KAAK,CAAC;EACpB,IAAImU,YAAY,GAAG,eAAgBjB,MAAM,CAAC9Y,aAAa,CACrD,QAAQ,EACR;IACEga,uBAAuB,EAAE;MACvBC,MAAM,EAAE;AAChB;AACA;AACA;AACA;IACM;EACF,CACF,CAAC;EACD,IAAI3c,oBAAoB,CAACsI,KAAK,CAAC,EAAE;IAC/B,OAAO,eAAgBkT,MAAM,CAAC9Y,aAAa,CACzCuZ,YAAY,EACZ;MACED,cAAc;MACdE,KAAK,EAAE;IACT,CAAC,EACD,eAAgBV,MAAM,CAAC9Y,aAAa,CAAC,IAAI,EAAE;MAAE4Z,KAAK,EAAE;QAAEM,QAAQ,EAAE;MAAO;IAAE,CAAC,EAAEtU,KAAK,CAACmF,MAAM,EAAE,GAAG,EAAEnF,KAAK,CAACoN,UAAU,CAAC,EAChHzX,mBAAmB,GAAGwe,YAAY,GAAG,IACvC,CAAC;EACH;EACA,IAAII,aAAa;EACjB,IAAIvU,KAAK,YAAYlD,KAAK,EAAE;IAC1ByX,aAAa,GAAGvU,KAAK;EACvB,CAAC,MAAM;IACL,IAAIwU,WAAW,GAAGxU,KAAK,IAAI,IAAI,GAAG,eAAe,GAAG,OAAOA,KAAK,KAAK,QAAQ,IAAI,UAAU,IAAIA,KAAK,GAAGA,KAAK,CAACwC,QAAQ,CAAC,CAAC,GAAGL,IAAI,CAACC,SAAS,CAACpC,KAAK,CAAC;IAC/IuU,aAAa,GAAG,IAAIzX,KAAK,CAAC0X,WAAW,CAAC;EACxC;EACA,OAAO,eAAgBtB,MAAM,CAAC9Y,aAAa,CAACuZ,YAAY,EAAE;IAAED,cAAc;IAAEE,KAAK,EAAE;EAAqB,CAAC,EAAE,eAAgBV,MAAM,CAAC9Y,aAAa,CAAC,IAAI,EAAE;IAAE4Z,KAAK,EAAE;MAAEM,QAAQ,EAAE;IAAO;EAAE,CAAC,EAAE,mBAAmB,CAAC,EAAE,eAAgBpB,MAAM,CAAC9Y,aAAa,CAC/O,KAAK,EACL;IACE4Z,KAAK,EAAE;MACLE,OAAO,EAAE,MAAM;MACfO,UAAU,EAAE,yBAAyB;MACrCC,KAAK,EAAE,KAAK;MACZC,QAAQ,EAAE;IACZ;EACF,CAAC,EACDJ,aAAa,CAAC1Q,KAChB,CAAC,EAAEsQ,YAAY,CAAC;AAClB;AACA,SAASS,2BAA2BA,CAAC;EACnCC;AACF,CAAC,EAAE;EACD,IAAI7U,KAAK,GAAG1H,aAAa,CAAC,CAAC;EAC3B,IAAIuc,aAAa,KAAK,KAAK,CAAC,EAAE;IAC5B,MAAM,IAAI/X,KAAK,CAAC,8BAA8B,CAAC;EACjD;EACA,OAAO,eAAgBoW,MAAM,CAAC9Y,aAAa,CACzCqZ,+BAA+B,EAC/B;IACEC,cAAc,EAAE,CAACmB,aAAa;IAC9B7U;EACF,CACF,CAAC;AACH;;AAEA;AACA,SAAS8U,gBAAgBA,CAAC;EACxBC,wBAAwB;EACxBC,2BAA2B;EAC3BC,WAAW;EACXC,KAAK,EAAEC,mBAAmB,GAAGD;AAC/B,CAAC,EAAE;EACD,MAAME,SAAS,GAAGC,MAAM;EACxB,IAAIC,cAAc,GAAG,CAAC;EACtB,OAAO,OAAOxb,EAAE,EAAEuD,IAAI,KAAK;IACzB,IAAIkY,QAAQ,GAAGH,SAAS,CAACI,gBAAgB,GAAG,CAACJ,SAAS,CAACI,gBAAgB,KAAKJ,SAAS,CAACI,gBAAgB,GAAG,CAAC,CAAC,IAAI,CAAC;IAChH,MAAMC,mBAAmB,GAAGT,2BAA2B,CAAC,CAAC;IACzD,MAAMrG,QAAQ,GAAG,MAAMwG,mBAAmB,CACxC,IAAIhP,OAAO,CAACvL,QAAQ,CAACwL,IAAI,EAAE;MACzBL,IAAI,EAAE,MAAMkP,WAAW,CAAC5X,IAAI,EAAE;QAAEoY;MAAoB,CAAC,CAAC;MACtD3P,MAAM,EAAE,MAAM;MACdE,OAAO,EAAE;QACP0P,MAAM,EAAE,kBAAkB;QAC1B,eAAe,EAAE5b;MACnB;IACF,CAAC,CACH,CAAC;IACD,IAAI,CAAC6U,QAAQ,CAAC5I,IAAI,EAAE;MAClB,MAAM,IAAIjJ,KAAK,CAAC,kBAAkB,CAAC;IACrC;IACA,MAAM6Y,OAAO,GAAG,MAAMZ,wBAAwB,CAACpG,QAAQ,CAAC5I,IAAI,EAAE;MAC5D0P;IACF,CAAC,CAAC;IACF,IAAIE,OAAO,CAACC,IAAI,KAAK,UAAU,EAAE;MAC/B,IAAID,OAAO,CAACvN,MAAM,EAAE;QAClBiN,MAAM,CAACza,QAAQ,CAACwL,IAAI,GAAGuP,OAAO,CAAC/a,QAAQ;QACvC;MACF;MACAwa,SAAS,CAACS,QAAQ,CAACC,QAAQ,CAACH,OAAO,CAAC/a,QAAQ,EAAE;QAC5C7C,OAAO,EAAE4d,OAAO,CAAC5d;MACnB,CAAC,CAAC;MACF,OAAO4d,OAAO,CAACI,YAAY;IAC7B;IACA,IAAIJ,OAAO,CAACC,IAAI,KAAK,QAAQ,EAAE;MAC7B,MAAM,IAAI9Y,KAAK,CAAC,yBAAyB,CAAC;IAC5C;IACA,IAAI6Y,OAAO,CAACK,QAAQ,EAAE;MACpBtD,MAAM,CAACuD,eAAe;MACpB;MACA,YAAY;QACV,MAAMD,QAAQ,GAAG,MAAML,OAAO,CAACK,QAAQ;QACvC,IAAI,CAACA,QAAQ,EAAE;QACf,IAAIV,cAAc,GAAGC,QAAQ,IAAIH,SAAS,CAACI,gBAAgB,IAAID,QAAQ,EAAE;UACvED,cAAc,GAAGC,QAAQ;UACzB,IAAIS,QAAQ,CAACJ,IAAI,KAAK,UAAU,EAAE;YAChC,IAAII,QAAQ,CAAC5N,MAAM,EAAE;cACnBiN,MAAM,CAACza,QAAQ,CAACwL,IAAI,GAAG4P,QAAQ,CAACpb,QAAQ;cACxC;YACF;YACAwa,SAAS,CAACS,QAAQ,CAACC,QAAQ,CAACE,QAAQ,CAACpb,QAAQ,EAAE;cAC7C7C,OAAO,EAAEie,QAAQ,CAACje;YACpB,CAAC,CAAC;YACF;UACF;UACA,IAAIme,SAAS;UACb,KAAK,MAAMxc,KAAK,IAAIsc,QAAQ,CAACrc,OAAO,EAAE;YACpCyb,SAAS,CAACS,QAAQ,CAACM,WAAW,CAC5BD,SAAS,EAAEpc,EAAE,IAAI,IAAI,EACrB,CAACsc,6BAA6B,CAAC1c,KAAK,CAAC,CAAC,EACtC,IACF,CAAC;YACDwc,SAAS,GAAGxc,KAAK;UACnB;UACA2b,MAAM,CAACQ,QAAQ,CAACQ,8CAA8C,CAAC,CAAC,CAAC,CAAC;UAClE3D,MAAM,CAACuD,eAAe,CAAC,MAAM;YAC3BZ,MAAM,CAACQ,QAAQ,CAACQ,8CAA8C,CAAC;cAC7D5c,UAAU,EAAE0J,MAAM,CAACe,MAAM,CACvB,CAAC,CAAC,EACFkR,SAAS,CAACS,QAAQ,CAAChb,KAAK,CAACpB,UAAU,EACnCuc,QAAQ,CAACvc,UACX,CAAC;cACDsK,MAAM,EAAEiS,QAAQ,CAACjS,MAAM,GAAGZ,MAAM,CAACe,MAAM,CACrC,CAAC,CAAC,EACFkR,SAAS,CAACS,QAAQ,CAAChb,KAAK,CAACkJ,MAAM,EAC/BiS,QAAQ,CAACjS,MACX,CAAC,GAAG;YACN,CAAC,CAAC;UACJ,CAAC,CAAC;QACJ;MACF,CACF,CAAC;IACH;IACA,OAAO4R,OAAO,CAACI,YAAY;EAC7B,CAAC;AACH;AACA,SAASO,uBAAuBA,CAAC;EAC/BnB,mBAAmB;EACnBJ,wBAAwB;EACxBwB,mBAAmB;EACnBZ;AACF,CAAC,EAAE;EACD,MAAMP,SAAS,GAAGC,MAAM;EACxB,IAAID,SAAS,CAACS,QAAQ,EAAE,OAAOT,SAAS,CAACS,QAAQ;EACjD,IAAIF,OAAO,CAACC,IAAI,KAAK,QAAQ,EAAE,MAAM,IAAI9Y,KAAK,CAAC,sBAAsB,CAAC;EACtE,IAAIsS,OAAO,GAAG,eAAgB,IAAI6B,GAAG,CAAC,CAAC;EACvC0E,OAAO,CAACvG,OAAO,EAAE/H,OAAO,CAAEmP,KAAK,IAAK;IAClCpf,SAAS,CAACof,KAAK,CAAC5Z,QAAQ,EAAE,wBAAwB,CAAC;IACnD,IAAI,CAACwS,OAAO,CAAC5E,GAAG,CAACgM,KAAK,CAAC5Z,QAAQ,CAAC,EAAE;MAChCwS,OAAO,CAAC9C,GAAG,CAACkK,KAAK,CAAC5Z,QAAQ,EAAE,EAAE,CAAC;IACjC;IACAwS,OAAO,CAACnI,GAAG,CAACuP,KAAK,CAAC5Z,QAAQ,CAAC,EAAE+I,IAAI,CAAC6Q,KAAK,CAAC;EAC1C,CAAC,CAAC;EACF,IAAInd,MAAM,GAAGsc,OAAO,CAAChc,OAAO,CAAC8c,WAAW,CAAC,CAACC,QAAQ,EAAEhd,KAAK,KAAK;IAC5D,MAAMG,KAAK,GAAGuc,6BAA6B,CACzC1c,KAAK,EACLic,OACF,CAAC;IACD,IAAIe,QAAQ,CAACpW,MAAM,GAAG,CAAC,EAAE;MACvBzG,KAAK,CAACyE,QAAQ,GAAGoY,QAAQ;MACzB,IAAIC,eAAe,GAAGvH,OAAO,CAACnI,GAAG,CAACvN,KAAK,CAACI,EAAE,CAAC;MAC3C,IAAI6c,eAAe,EAAE;QACnB9c,KAAK,CAACyE,QAAQ,CAACqH,IAAI,CACjB,GAAGgR,eAAe,CAAC9Z,GAAG,CAAEF,CAAC,IAAKyZ,6BAA6B,CAACzZ,CAAC,CAAC,CAChE,CAAC;MACH;IACF;IACA,OAAO,CAAC9C,KAAK,CAAC;EAChB,CAAC,EAAE,EAAE,CAAC;EACNub,SAAS,CAACS,QAAQ,GAAGlf,YAAY,CAAC;IAChC0C,MAAM;IACNkd,mBAAmB;IACnB5R,QAAQ,EAAEgR,OAAO,CAAChR,QAAQ;IAC1BiS,OAAO,EAAEpgB,oBAAoB,CAAC,CAAC;IAC/BoF,aAAa,EAAEgX,gBAAgB,CAC7B;MACEnZ,UAAU,EAAEkc,OAAO,CAAClc,UAAU;MAC9B8P,UAAU,EAAEoM,OAAO,CAACpM,UAAU;MAC9BxF,MAAM,EAAE4R,OAAO,CAAC5R;IAClB,CAAC,EACD1K,MAAM,EACLO,OAAO,IAAK;MACX,IAAIF,KAAK,GAAGic,OAAO,CAAChc,OAAO,CAACkd,IAAI,CAAE5N,CAAC,IAAKA,CAAC,CAACnP,EAAE,KAAKF,OAAO,CAAC;MACzDxC,SAAS,CAACsC,KAAK,EAAE,4BAA4B,CAAC;MAC9C,OAAO;QACLM,YAAY,EAAEN,KAAK,CAACM,YAAY;QAChCC,SAAS,EAAEP,KAAK,CAACO,SAAS;QAC1BgZ,kBAAkB,EAAEvZ,KAAK,CAACod,sBAAsB,IAAI;MACtD,CAAC;IACH,CAAC,EACDnB,OAAO,CAAC/a,QAAQ,EAChB,KAAK,CAAC,EACN,KACF,CAAC;IACD,MAAMmc,uBAAuBA,CAAC;MAAE/Z,IAAI;MAAEiJ;IAAO,CAAC,EAAE;MAC9C,IAAI+Q,eAAe,CAACxM,GAAG,CAACxN,IAAI,CAAC,EAAE;QAC7B;MACF;MACA,MAAMia,4BAA4B,CAChC,CAACja,IAAI,CAAC,EACN+X,wBAAwB,EACxBI,mBAAmB,EACnBlP,MACF,CAAC;IACH,CAAC;IACD;IACAiR,YAAY,EAAEC,6BAA6B,CACzC,MAAM/B,SAAS,CAACS,QAAQ,EACxB,IAAI,EACJF,OAAO,CAAChR,QAAQ,EAChBoQ,wBAAwB,EACxBI,mBACF;EACF,CAAC,CAAC;EACF,IAAIC,SAAS,CAACS,QAAQ,CAAChb,KAAK,CAACuc,WAAW,EAAE;IACxChC,SAAS,CAACiC,mBAAmB,GAAG,IAAI;IACpCjC,SAAS,CAACS,QAAQ,CAACyB,UAAU,CAAC,CAAC;EACjC,CAAC,MAAM;IACLlC,SAAS,CAACiC,mBAAmB,GAAG,KAAK;EACvC;EACA,IAAIE,cAAc,GAAG,KAAK,CAAC;EAC3BnC,SAAS,CAACS,QAAQ,CAAC2B,SAAS,CAAC,CAAC;IAAE/d,UAAU;IAAE8P;EAAW,CAAC,KAAK;IAC3D,IAAIgO,cAAc,KAAK9d,UAAU,EAAE;MACjC2b,SAAS,CAACI,gBAAgB,GAAG,CAACJ,SAAS,CAACI,gBAAgB,KAAKJ,SAAS,CAACI,gBAAgB,GAAG,CAAC,CAAC,IAAI,CAAC;IACnG;EACF,CAAC,CAAC;EACF,OAAOJ,SAAS,CAACS,QAAQ;AAC3B;AACA,IAAI4B,qBAAqB,GAAGpf,sBAAsB,CAAC,CAAC;AACpD,SAAS8e,6BAA6BA,CAACO,SAAS,EAAEld,GAAG,EAAEmK,QAAQ,EAAEoQ,wBAAwB,EAAEI,mBAAmB,EAAE;EAC9G,IAAI+B,YAAY,GAAGhgB,8BAA8B,CAC/CwgB,SAAS,EACRhe,KAAK,IAAK;IACT,IAAIie,CAAC,GAAGje,KAAK;IACb,OAAO;MACLO,SAAS,EAAE0d,CAAC,CAAC9d,KAAK,CAACI,SAAS;MAC5B2D,eAAe,EAAE+Z,CAAC,CAAC9d,KAAK,CAAC+D,eAAe;MACxCga,YAAY,EAAED,CAAC,CAAC9d,KAAK,CAAC+d,YAAY;MAClCla,SAAS,EAAEia,CAAC,CAAC9d,KAAK,CAAC6D,SAAS;MAC5BC,eAAe,EAAEga,CAAC,CAAC9d,KAAK,CAAC8D,eAAe;MACxCka,mBAAmB,EAAEF,CAAC,CAAC9d,KAAK,CAACge;IAC/B,CAAC;EACH,CAAC;EACD;EACAC,uBAAuB,CAAC/C,wBAAwB,EAAEI,mBAAmB,CAAC,EACtE3a,GAAG,EACHmK,QAAQ;EACR;EACA;EACA;EACCjL,KAAK,IAAK;IACT,IAAIie,CAAC,GAAGje,KAAK;IACb,OAAOie,CAAC,CAAC9d,KAAK,CAAC+d,YAAY,IAAI,CAACD,CAAC,CAAC9d,KAAK,CAACke,OAAO;EACjD,CACF,CAAC;EACD,OAAO,MAAO1a,IAAI,IAAKA,IAAI,CAAC2a,4BAA4B,CAAC,YAAY;IACnE,IAAInf,OAAO,GAAGwE,IAAI,CAACxE,OAAO;IAC1BA,OAAO,CAACyT,GAAG,CAACmL,qBAAqB,EAAE,EAAE,CAAC;IACtC,IAAI5L,OAAO,GAAG,MAAMqL,YAAY,CAAC7Z,IAAI,CAAC;IACtC,MAAM4a,kBAAkB,GAAG,eAAgB,IAAIhH,GAAG,CAAC,CAAC;IACpD,KAAK,MAAMpX,KAAK,IAAIhB,OAAO,CAACoO,GAAG,CAACwQ,qBAAqB,CAAC,EAAE;MACtD,IAAI,CAACQ,kBAAkB,CAACzN,GAAG,CAAC3Q,KAAK,CAACC,EAAE,CAAC,EAAE;QACrCme,kBAAkB,CAAC3L,GAAG,CAACzS,KAAK,CAACC,EAAE,EAAE,EAAE,CAAC;MACtC;MACAme,kBAAkB,CAAChR,GAAG,CAACpN,KAAK,CAACC,EAAE,CAAC,CAAC6L,IAAI,CAAC9L,KAAK,CAAC;IAC9C;IACA,KAAK,MAAMH,KAAK,IAAI2D,IAAI,CAAC1D,OAAO,EAAE;MAChC,MAAMue,cAAc,GAAGD,kBAAkB,CAAChR,GAAG,CAACvN,KAAK,CAACG,KAAK,CAACC,EAAE,CAAC;MAC7D,IAAIoe,cAAc,EAAE;QAClB,KAAK,MAAMC,QAAQ,IAAID,cAAc,EAAE;UACrC7C,MAAM,CAACQ,QAAQ,CAACM,WAAW,CACzBgC,QAAQ,CAACvb,QAAQ,IAAI,IAAI,EACzB,CAACwZ,6BAA6B,CAAC+B,QAAQ,CAAC,CAAC,EACzC,IACF,CAAC;QACH;MACF;IACF;IACA,OAAOtM,OAAO;EAChB,CAAC,CAAC;AACJ;AACA,SAASiM,uBAAuBA,CAAC/C,wBAAwB,EAAEI,mBAAmB,EAAE;EAC9E,OAAO,OAAO9X,IAAI,EAAEsH,QAAQ,EAAEyT,YAAY,KAAK;IAC7C,IAAI;MAAErT,OAAO;MAAElM;IAAQ,CAAC,GAAGwE,IAAI;IAC/B,IAAIvE,GAAG,GAAGb,cAAc,CAAC8M,OAAO,CAACjM,GAAG,EAAE6L,QAAQ,EAAE,KAAK,CAAC;IACtD,IAAII,OAAO,CAACe,MAAM,KAAK,KAAK,EAAE;MAC5BhN,GAAG,GAAGX,eAAe,CAACW,GAAG,CAAC;MAC1B,IAAIsf,YAAY,EAAE;QAChBtf,GAAG,CAACwM,YAAY,CAACgH,GAAG,CAAC,SAAS,EAAE8L,YAAY,CAAC1I,IAAI,CAAC,GAAG,CAAC,CAAC;MACzD;IACF;IACA,IAAIjB,GAAG,GAAG,MAAM0G,mBAAmB,CACjC,IAAIhP,OAAO,CAACrN,GAAG,EAAE,MAAMpC,iBAAiB,CAACqO,OAAO,CAAC,CACnD,CAAC;IACD,IAAI0J,GAAG,CAACtJ,MAAM,KAAK,GAAG,IAAI,CAACsJ,GAAG,CAACzI,OAAO,CAACwE,GAAG,CAAC,kBAAkB,CAAC,EAAE;MAC9D,MAAM,IAAI5U,iBAAiB,CAAC,GAAG,EAAE,WAAW,EAAE,IAAI,CAAC;IACrD;IACAwB,SAAS,CAACqX,GAAG,CAAC1I,IAAI,EAAE,4BAA4B,CAAC;IACjD,IAAI;MACF,MAAM4P,OAAO,GAAG,MAAMZ,wBAAwB,CAACtG,GAAG,CAAC1I,IAAI,EAAE;QACvD0P,mBAAmB,EAAE,KAAK;MAC5B,CAAC,CAAC;MACF,IAAIE,OAAO,CAACC,IAAI,KAAK,UAAU,EAAE;QAC/B,OAAO;UACLzQ,MAAM,EAAEsJ,GAAG,CAACtJ,MAAM;UAClBkD,IAAI,EAAE;YACJxQ,QAAQ,EAAE;cACRA,QAAQ,EAAE8d,OAAO,CAAC/a,QAAQ;cAC1BwN,MAAM,EAAEuN,OAAO,CAACvN,MAAM;cACtBrQ,OAAO,EAAE4d,OAAO,CAAC5d,OAAO;cACxB2U,UAAU,EAAE,KAAK;cACjBvH,MAAM,EAAEwQ,OAAO,CAACxQ;YAClB;UACF;QACF,CAAC;MACH;MACA,IAAIwQ,OAAO,CAACC,IAAI,KAAK,QAAQ,EAAE;QAC7B,MAAM,IAAI9Y,KAAK,CAAC,yBAAyB,CAAC;MAC5C;MACAjE,OAAO,CAACoO,GAAG,CAACwQ,qBAAqB,CAAC,CAAC9R,IAAI,CAAC,GAAGgQ,OAAO,CAAChc,OAAO,CAAC;MAC3D,IAAIkS,OAAO,GAAG;QAAExS,MAAM,EAAE,CAAC;MAAE,CAAC;MAC5B,MAAMgf,OAAO,GAAG/gB,gBAAgB,CAACyN,OAAO,CAACe,MAAM,CAAC,GAAG,YAAY,GAAG,YAAY;MAC9E,KAAK,IAAI,CAAClM,OAAO,EAAEiF,KAAK,CAAC,IAAIsE,MAAM,CAACa,OAAO,CAAC2R,OAAO,CAAC0C,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE;QACnExM,OAAO,CAACxS,MAAM,CAACO,OAAO,CAAC,GAAG;UAAEyO,IAAI,EAAExJ;QAAM,CAAC;MAC3C;MACA,IAAI8W,OAAO,CAAC5R,MAAM,EAAE;QAClB,KAAK,IAAI,CAACnK,OAAO,EAAEoG,KAAK,CAAC,IAAImD,MAAM,CAACa,OAAO,CAAC2R,OAAO,CAAC5R,MAAM,CAAC,EAAE;UAC3D8H,OAAO,CAACxS,MAAM,CAACO,OAAO,CAAC,GAAG;YAAEoG;UAAM,CAAC;QACrC;MACF;MACA,OAAO;QAAEmF,MAAM,EAAEsJ,GAAG,CAACtJ,MAAM;QAAEkD,IAAI,EAAEwD;MAAQ,CAAC;IAC9C,CAAC,CAAC,OAAO3E,CAAC,EAAE;MACV,MAAM,IAAIpK,KAAK,CAAC,+BAA+B,CAAC;IAClD;EACF,CAAC;AACH;AACA,SAASwb,iBAAiBA,CAAC;EACzBvD,wBAAwB;EACxBG,KAAK,EAAEC,mBAAmB,GAAGD,KAAK;EAClCS,OAAO;EACPlb,cAAc,GAAG,OAAO;EACxB8b;AACF,CAAC,EAAE;EACD,IAAIZ,OAAO,CAACC,IAAI,KAAK,QAAQ,EAAE,MAAM,IAAI9Y,KAAK,CAAC,sBAAsB,CAAC;EACtE,IAAI3C,MAAM,GAAGuY,MAAM,CAAC6F,OAAO,CACzB,MAAMjC,uBAAuB,CAAC;IAC5BX,OAAO;IACPR,mBAAmB;IACnBoB,mBAAmB;IACnBxB;EACF,CAAC,CAAC,EACF,CACEA,wBAAwB,EACxBY,OAAO,EACPR,mBAAmB,EACnBoB,mBAAmB,CAEvB,CAAC;EACD7D,MAAM,CAAC8F,eAAe,CAAC,MAAM;IAC3B,MAAMpD,SAAS,GAAGC,MAAM;IACxB,IAAI,CAACD,SAAS,CAACiC,mBAAmB,EAAE;MAClCjC,SAAS,CAACiC,mBAAmB,GAAG,IAAI;MACpCjC,SAAS,CAACS,QAAQ,CAACyB,UAAU,CAAC,CAAC;IACjC;EACF,CAAC,EAAE,EAAE,CAAC;EACN,IAAI,CAACxE,SAAS,EAAE2F,WAAW,CAAC,GAAG/F,MAAM,CAACgG,QAAQ,CAACve,MAAM,CAACU,KAAK,CAACD,QAAQ,CAAC;EACrE8X,MAAM,CAAC8F,eAAe,CACpB,MAAMre,MAAM,CAACqd,SAAS,CAAEmB,QAAQ,IAAK;IACnC,IAAIA,QAAQ,CAAC/d,QAAQ,KAAKkY,SAAS,EAAE;MACnC2F,WAAW,CAACE,QAAQ,CAAC/d,QAAQ,CAAC;IAChC;EACF,CAAC,CAAC,EACF,CAACT,MAAM,EAAE2Y,SAAS,CACpB,CAAC;EACDJ,MAAM,CAACkG,SAAS,CAAC,MAAM;IACrB,IAAIne,cAAc,KAAK,MAAM;IAAI;IACjC4a,MAAM,CAACwD,SAAS,EAAEC,UAAU,EAAEC,QAAQ,KAAK,IAAI,EAAE;MAC/C;IACF;IACA,SAASC,eAAeA,CAACC,EAAE,EAAE;MAC3B,IAAIjc,IAAI,GAAGic,EAAE,CAACC,OAAO,KAAK,MAAM,GAAGD,EAAE,CAACE,YAAY,CAAC,QAAQ,CAAC,GAAGF,EAAE,CAACE,YAAY,CAAC,MAAM,CAAC;MACtF,IAAI,CAACnc,IAAI,EAAE;QACT;MACF;MACA,IAAI0H,QAAQ,GAAGuU,EAAE,CAACC,OAAO,KAAK,GAAG,GAAGD,EAAE,CAACvU,QAAQ,GAAG,IAAI1L,GAAG,CAACgE,IAAI,EAAEqY,MAAM,CAACza,QAAQ,CAACwe,MAAM,CAAC,CAAC1U,QAAQ;MAChG,IAAI,CAACsS,eAAe,CAACxM,GAAG,CAAC9F,QAAQ,CAAC,EAAE;QAClC2U,SAAS,CAAC1J,GAAG,CAACjL,QAAQ,CAAC;MACzB;IACF;IACA,eAAe4U,YAAYA,CAAA,EAAG;MAC5BC,QAAQ,CAACC,gBAAgB,CAAC,uCAAuC,CAAC,CAACnS,OAAO,CAAC2R,eAAe,CAAC;MAC3F,IAAI3J,KAAK,GAAGoK,KAAK,CAACC,IAAI,CAACL,SAAS,CAACjW,IAAI,CAAC,CAAC,CAAC,CAAC2I,MAAM,CAAE/O,IAAI,IAAK;QACxD,IAAIga,eAAe,CAACxM,GAAG,CAACxN,IAAI,CAAC,EAAE;UAC7Bqc,SAAS,CAAC7T,MAAM,CAACxI,IAAI,CAAC;UACtB,OAAO,KAAK;QACd;QACA,OAAO,IAAI;MACb,CAAC,CAAC;MACF,IAAIqS,KAAK,CAAC/O,MAAM,KAAK,CAAC,EAAE;QACtB;MACF;MACA,IAAI;QACF,MAAM2W,4BAA4B,CAChC5H,KAAK,EACL0F,wBAAwB,EACxBI,mBACF,CAAC;MACH,CAAC,CAAC,OAAOjO,CAAC,EAAE;QACVZ,OAAO,CAACtG,KAAK,CAAC,kCAAkC,EAAEkH,CAAC,CAAC;MACtD;IACF;IACA,IAAIyS,qBAAqB,GAAGC,QAAQ,CAACN,YAAY,EAAE,GAAG,CAAC;IACvDA,YAAY,CAAC,CAAC;IACd,IAAIO,QAAQ,GAAG,IAAIC,gBAAgB,CAAC,MAAMH,qBAAqB,CAAC,CAAC,CAAC;IAClEE,QAAQ,CAACE,OAAO,CAACR,QAAQ,CAACS,eAAe,EAAE;MACzCC,OAAO,EAAE,IAAI;MACbC,SAAS,EAAE,IAAI;MACfC,UAAU,EAAE,IAAI;MAChBC,eAAe,EAAE,CAAC,eAAe,EAAE,MAAM,EAAE,QAAQ;IACrD,CAAC,CAAC;EACJ,CAAC,EAAE,CAAC3f,cAAc,EAAEsa,wBAAwB,EAAEI,mBAAmB,CAAC,CAAC;EACnE,MAAMkF,gBAAgB,GAAG;IACvB/gB,MAAM,EAAE;MACN;MACA;MACA4C,mBAAmB,EAAE,KAAK;MAC1BD,6BAA6B,EAAE;IACjC,CAAC;IACD1C,SAAS,EAAE,IAAI;IACfiB,GAAG,EAAE,IAAI;IACTrB,WAAW,EAAE,EAAE;IACfF,QAAQ,EAAE;MACRI,MAAM,EAAE,CAAC,CAAC;MACViD,OAAO,EAAE,GAAG;MACZxD,GAAG,EAAE,EAAE;MACPqD,KAAK,EAAE;QACLE,MAAM,EAAE,EAAE;QACVD,OAAO,EAAE;MACX;IACF,CAAC;IACD3B,cAAc,EAAE;MAAE8B,IAAI,EAAE,MAAM;MAAEC,YAAY,EAAE;IAAc,CAAC;IAC7DtD,YAAY,EAAE,CAAC;EACjB,CAAC;EACD,OAAO,eAAgBwZ,MAAM,CAACtY,aAAa,CAACpE,gBAAgB,CAACsE,QAAQ,EAAE;IAAEC,KAAK,EAAE;EAAK,CAAC,EAAE,eAAgBmY,MAAM,CAACtY,aAAa,CAAC+Y,4BAA4B,EAAE;IAAEvY,QAAQ,EAAEkY;EAAU,CAAC,EAAE,eAAgBJ,MAAM,CAACtY,aAAa,CAACvE,gBAAgB,CAACyE,QAAQ,EAAE;IAAEC,KAAK,EAAE8f;EAAiB,CAAC,EAAE,eAAgB3H,MAAM,CAACtY,aAAa,CAAClE,cAAc,EAAE;IAAEiE,MAAM;IAAEmgB,SAAS,EAAE3H,QAAQ,CAAC2H;EAAU,CAAC,CAAC,CAAC,CAAC,CAAC;AACrX;AACA,SAASlE,6BAA6BA,CAAC1c,KAAK,EAAEic,OAAO,EAAE;EACrD,IAAI4E,cAAc,GAAG5E,OAAO,IAAIjc,KAAK,CAACI,EAAE,IAAI6b,OAAO,CAAClc,UAAU;EAC9D,IAAIuX,WAAW,GAAG2E,OAAO,EAAElc,UAAU,CAACC,KAAK,CAACI,EAAE,CAAC;EAC/C,IAAI0gB,eAAe,GAAG7E,OAAO,EAAE5R,MAAM,IAAIrK,KAAK,CAACI,EAAE,IAAI6b,OAAO,CAAC5R,MAAM;EACnE,IAAI0W,YAAY,GAAG9E,OAAO,EAAE5R,MAAM,GAAGrK,KAAK,CAACI,EAAE,CAAC;EAC9C,IAAI4gB,kBAAkB,GAAGhhB,KAAK,CAACM,YAAY,EAAEc,OAAO,KAAK,IAAI,IAAI,CAACpB,KAAK,CAACO,SAAS;EAAI;EACrF;EACA;EACAP,KAAK,CAACke,YAAY,IAAI,CAACle,KAAK,CAACqe,OAAO;EACpC,IAAI4C,SAAS,GAAG;IACd7gB,EAAE,EAAEJ,KAAK,CAACI,EAAE;IACZie,OAAO,EAAEre,KAAK,CAACqe,OAAO;IACtB6C,YAAY,EAAElhB,KAAK,CAACkhB,YAAY;IAChCrd,MAAM,EAAE7D,KAAK,CAAC6D,MAAM;IACpBO,gBAAgB,EAAEpE,KAAK,CAACoE,gBAAgB;IACxCgZ,sBAAsB,EAAEpd,KAAK,CAACod,sBAAsB;IACpD7Z,KAAK,EAAEvD,KAAK,CAACuD,KAAK;IAClBK,MAAM,EAAE5D,KAAK,CAACM,YAAY,GAAG,OAAOqD,IAAI,EAAEwd,WAAW,KAAK;MACxD,IAAI;QACF,IAAIpY,MAAM,GAAG,MAAM/I,KAAK,CAACM,YAAY,CAAC;UACpC,GAAGqD,IAAI;UACPyd,YAAY,EAAEA,CAAA,KAAM;YAClBC,+BAA+B,CAC7B,QAAQ,EACRrhB,KAAK,CAACI,EAAE,EACRJ,KAAK,CAACO,SACR,CAAC;YACD,IAAIygB,kBAAkB,EAAE;cACtB,IAAIH,cAAc,EAAE;gBAClB,OAAOvJ,WAAW;cACpB;cACA,IAAIwJ,eAAe,EAAE;gBACnB,MAAMC,YAAY;cACpB;YACF;YACA,OAAOO,eAAe,CAACH,WAAW,CAAC;UACrC;QACF,CAAC,CAAC;QACF,OAAOpY,MAAM;MACf,CAAC,SAAS;QACRiY,kBAAkB,GAAG,KAAK;MAC5B;IACF,CAAC;IACC;IACA;IACA,CAAClL,CAAC,EAAEqL,WAAW,KAAKG,eAAe,CAACH,WAAW,CAChD;IACDzd,MAAM,EAAE1D,KAAK,CAACuhB,YAAY,GAAG,CAAC5d,IAAI,EAAEwd,WAAW,KAAKnhB,KAAK,CAACuhB,YAAY,CAAC;MACrE,GAAG5d,IAAI;MACP6d,YAAY,EAAE,MAAAA,CAAA,KAAY;QACxBH,+BAA+B,CAC7B,QAAQ,EACRrhB,KAAK,CAACI,EAAE,EACRJ,KAAK,CAACO,SACR,CAAC;QACD,OAAO,MAAM+gB,eAAe,CAACH,WAAW,CAAC;MAC3C;IACF,CAAC,CAAC,GAAGnhB,KAAK,CAACgE,SAAS,GAAG,CAAC8R,CAAC,EAAEqL,WAAW,KAAKG,eAAe,CAACH,WAAW,CAAC,GAAG,MAAM;MAC9E,MAAMjjB,oBAAoB,CAAC,QAAQ,EAAE8B,KAAK,CAACI,EAAE,CAAC;IAChD,CAAC;IACDkD,IAAI,EAAEtD,KAAK,CAACsD,IAAI;IAChBQ,gBAAgB,EAAE9D,KAAK,CAAC8D,gBAAgB;IACxC;IACA;IACAvD,SAAS,EAAE,IAAI;IACf2D,eAAe,EAAElE,KAAK,CAACM,YAAY,IAAI,IAAI;IAC3C0D,SAAS,EAAEhE,KAAK,CAACgE,SAAS;IAC1BC,eAAe,EAAEjE,KAAK,CAACuhB,YAAY,IAAI,IAAI;IAC3CpD,mBAAmB,EAAEne,KAAK,CAAC8D,gBAAgB,IAAI;EACjD,CAAC;EACD,IAAI,OAAOmd,SAAS,CAACrd,MAAM,KAAK,UAAU,EAAE;IAC1Cqd,SAAS,CAACrd,MAAM,CAACxC,OAAO,GAAG9C,wBAAwB,CACjD0B,KAAK,CAACI,EAAE,EACRJ,KAAK,CAACM,YAAY,EAClBN,KAAK,CAACO,SAAS,EACf,KACF,CAAC;EACH;EACA,OAAO0gB,SAAS;AAClB;AACA,SAASK,eAAeA,CAACH,WAAW,EAAE;EACpCzjB,SAAS,CAAC,OAAOyjB,WAAW,KAAK,UAAU,EAAE,+BAA+B,CAAC;EAC7E,OAAOA,WAAW,CAAC,CAAC;AACtB;AACA,SAASE,+BAA+BA,CAACnF,IAAI,EAAEhc,OAAO,EAAEuhB,UAAU,EAAE;EAClE,IAAI,CAACA,UAAU,EAAE;IACf,IAAIC,EAAE,GAAGxF,IAAI,KAAK,QAAQ,GAAG,gBAAgB,GAAG,gBAAgB;IAChE,IAAIyF,GAAG,GAAG,0BAA0BD,EAAE,2CAA2CxF,IAAI,eAAehc,OAAO,IAAI;IAC/G0M,OAAO,CAACtG,KAAK,CAACqb,GAAG,CAAC;IAClB,MAAM,IAAIzlB,iBAAiB,CAAC,GAAG,EAAE,aAAa,EAAE,IAAIkH,KAAK,CAACue,GAAG,CAAC,EAAE,IAAI,CAAC;EACvE;AACF;AACA,IAAIhC,SAAS,GAAG,eAAgB,IAAI/O,GAAG,CAAC,CAAC;AACzC,IAAIgR,sBAAsB,GAAG,GAAG;AAChC,IAAItE,eAAe,GAAG,eAAgB,IAAI1M,GAAG,CAAC,CAAC;AAC/C,IAAIiR,SAAS,GAAG,IAAI;AACpB,SAASC,cAAcA,CAACnM,KAAK,EAAE;EAC7B,IAAIA,KAAK,CAAC/O,MAAM,KAAK,CAAC,EAAE;IACtB,OAAO,IAAI;EACb;EACA,IAAI+O,KAAK,CAAC/O,MAAM,KAAK,CAAC,EAAE;IACtB,OAAO,IAAItH,GAAG,CAAC,GAAGqW,KAAK,CAAC,CAAC,CAAC,WAAW,EAAEgG,MAAM,CAACza,QAAQ,CAACwe,MAAM,CAAC;EAChE;EACA,MAAMhE,SAAS,GAAGC,MAAM;EACxB,IAAI1Q,QAAQ,GAAG,CAACyQ,SAAS,CAACS,QAAQ,CAAClR,QAAQ,IAAI,EAAE,EAAE5M,OAAO,CAAC,UAAU,EAAE,EAAE,CAAC;EAC1E,IAAIe,GAAG,GAAG,IAAIE,GAAG,CAAC,GAAG2L,QAAQ,YAAY,EAAE0Q,MAAM,CAACza,QAAQ,CAACwe,MAAM,CAAC;EAClE/J,KAAK,CAACoM,IAAI,CAAC,CAAC,CAACpU,OAAO,CAAErK,IAAI,IAAKlE,GAAG,CAACwM,YAAY,CAACO,MAAM,CAAC,GAAG,EAAE7I,IAAI,CAAC,CAAC;EAClE,OAAOlE,GAAG;AACZ;AACA,eAAeme,4BAA4BA,CAAC5H,KAAK,EAAE0F,wBAAwB,EAAEI,mBAAmB,EAAElP,MAAM,EAAE;EACxG,IAAInN,GAAG,GAAG0iB,cAAc,CAACnM,KAAK,CAAC;EAC/B,IAAIvW,GAAG,IAAI,IAAI,EAAE;IACf;EACF;EACA,IAAIA,GAAG,CAAC0J,QAAQ,CAAC,CAAC,CAAClC,MAAM,GAAGib,SAAS,EAAE;IACrClC,SAAS,CAACqC,KAAK,CAAC,CAAC;IACjB;EACF;EACA,IAAI/M,QAAQ,GAAG,MAAMwG,mBAAmB,CAAC,IAAIhP,OAAO,CAACrN,GAAG,EAAE;IAAEmN;EAAO,CAAC,CAAC,CAAC;EACtE,IAAI,CAAC0I,QAAQ,CAAC5I,IAAI,IAAI4I,QAAQ,CAACxJ,MAAM,GAAG,GAAG,IAAIwJ,QAAQ,CAACxJ,MAAM,IAAI,GAAG,EAAE;IACrE,MAAM,IAAIrI,KAAK,CAAC,mDAAmD,CAAC;EACtE;EACA,IAAI6Y,OAAO,GAAG,MAAMZ,wBAAwB,CAACpG,QAAQ,CAAC5I,IAAI,EAAE;IAC1D0P,mBAAmB,EAAE,KAAK;EAC5B,CAAC,CAAC;EACF,IAAIE,OAAO,CAACC,IAAI,KAAK,UAAU,EAAE;IAC/B,MAAM,IAAI9Y,KAAK,CAAC,wBAAwB,CAAC;EAC3C;EACAuS,KAAK,CAAChI,OAAO,CAAEsU,CAAC,IAAKC,cAAc,CAACD,CAAC,EAAE3E,eAAe,CAAC,CAAC;EACxDrB,OAAO,CAACvG,OAAO,CAAC/H,OAAO,CAAEsU,CAAC,IAAK;IAC7BtG,MAAM,CAACQ,QAAQ,CAACM,WAAW,CACzBwF,CAAC,CAAC/e,QAAQ,IAAI,IAAI,EAClB,CAACwZ,6BAA6B,CAACuF,CAAC,CAAC,CACnC,CAAC;EACH,CAAC,CAAC;AACJ;AACA,SAASC,cAAcA,CAAC5e,IAAI,EAAE6e,KAAK,EAAE;EACnC,IAAIA,KAAK,CAACC,IAAI,IAAIR,sBAAsB,EAAE;IACxC,IAAIS,KAAK,GAAGF,KAAK,CAACzU,MAAM,CAAC,CAAC,CAAC4U,IAAI,CAAC,CAAC,CAACzhB,KAAK;IACvCshB,KAAK,CAACrW,MAAM,CAACuW,KAAK,CAAC;EACrB;EACAF,KAAK,CAAClM,GAAG,CAAC3S,IAAI,CAAC;AACjB;AACA,SAAS4c,QAAQA,CAACqC,QAAQ,EAAEC,IAAI,EAAE;EAChC,IAAIrP,SAAS;EACb,OAAO,CAAC,GAAGxP,IAAI,KAAK;IAClBgY,MAAM,CAACpI,YAAY,CAACJ,SAAS,CAAC;IAC9BA,SAAS,GAAGwI,MAAM,CAACvI,UAAU,CAAC,MAAMmP,QAAQ,CAAC,GAAG5e,IAAI,CAAC,EAAE6e,IAAI,CAAC;EAC9D,CAAC;AACH;;AAEA;AACA,OAAO,KAAKC,MAAM,MAAM,OAAO;;AAE/B;AACA,IAAIC,QAAQ,GAAG,IAAI1d,WAAW,CAAC,CAAC;AAChC,IAAI2d,OAAO,GAAG,gBAAgB;AAC9B,SAASC,gBAAgBA,CAACC,SAAS,EAAE;EACnC,IAAIC,OAAO,GAAG,IAAInhB,WAAW,CAAC,CAAC;EAC/B,IAAIohB,wBAAwB;EAC5B,IAAIC,iBAAiB,GAAG,IAAIC,OAAO,CAChCC,OAAO,IAAKH,wBAAwB,GAAGG,OAC1C,CAAC;EACD,IAAIC,UAAU,GAAG,KAAK;EACtB,IAAIC,QAAQ,GAAG,EAAE;EACjB,IAAIC,OAAO,GAAG,IAAI;EAClB,SAASC,mBAAmBA,CAAChV,UAAU,EAAE;IACvC,KAAK,IAAIiV,KAAK,IAAIH,QAAQ,EAAE;MAC1B,IAAII,GAAG,GAAGV,OAAO,CAACW,MAAM,CAACF,KAAK,EAAE;QAAEpV,MAAM,EAAE;MAAK,CAAC,CAAC;MACjD,IAAIqV,GAAG,CAAC9O,QAAQ,CAACiO,OAAO,CAAC,EAAE;QACzBa,GAAG,GAAGA,GAAG,CAACvd,KAAK,CAAC,CAAC,EAAE,CAAC0c,OAAO,CAAC/b,MAAM,CAAC;MACrC;MACA0H,UAAU,CAACC,OAAO,CAACmU,QAAQ,CAACplB,MAAM,CAACkmB,GAAG,CAAC,CAAC;IAC1C;IACAJ,QAAQ,CAACxc,MAAM,GAAG,CAAC;IACnByc,OAAO,GAAG,IAAI;EAChB;EACA,OAAO,IAAIK,eAAe,CAAC;IACzBC,SAASA,CAACJ,KAAK,EAAEjV,UAAU,EAAE;MAC3B8U,QAAQ,CAACnX,IAAI,CAACsX,KAAK,CAAC;MACpB,IAAIF,OAAO,EAAE;QACX;MACF;MACAA,OAAO,GAAGjQ,UAAU,CAAC,YAAY;QAC/BkQ,mBAAmB,CAAChV,UAAU,CAAC;QAC/B,IAAI,CAAC6U,UAAU,EAAE;UACfA,UAAU,GAAG,IAAI;UACjBS,cAAc,CAACf,SAAS,EAAEvU,UAAU,CAAC,CAACuV,KAAK,CAAEpS,GAAG,IAAKnD,UAAU,CAAChI,KAAK,CAACmL,GAAG,CAAC,CAAC,CAACqS,IAAI,CAACf,wBAAwB,CAAC;QAC5G;MACF,CAAC,EAAE,CAAC,CAAC;IACP,CAAC;IACD,MAAMgB,KAAKA,CAACzV,UAAU,EAAE;MACtB,MAAM0U,iBAAiB;MACvB,IAAIK,OAAO,EAAE;QACX9P,YAAY,CAAC8P,OAAO,CAAC;QACrBC,mBAAmB,CAAChV,UAAU,CAAC;MACjC;MACAA,UAAU,CAACC,OAAO,CAACmU,QAAQ,CAACplB,MAAM,CAAC,gBAAgB,CAAC,CAAC;IACvD;EACF,CAAC,CAAC;AACJ;AACA,eAAesmB,cAAcA,CAACf,SAAS,EAAEvU,UAAU,EAAE;EACnD,IAAIwU,OAAO,GAAG,IAAInhB,WAAW,CAAC,OAAO,EAAE;IAAEqiB,KAAK,EAAE;EAAK,CAAC,CAAC;EACvD,MAAMxiB,MAAM,GAAGqhB,SAAS,CAACphB,SAAS,CAAC,CAAC;EACpC,IAAI;IACF,IAAIwiB,IAAI;IACR,OAAO,CAACA,IAAI,GAAG,MAAMziB,MAAM,CAACyiB,IAAI,CAAC,CAAC,KAAK,CAACA,IAAI,CAACC,IAAI,EAAE;MACjD,MAAMX,KAAK,GAAGU,IAAI,CAACpjB,KAAK;MACxB,IAAI;QACFsjB,UAAU,CACR1b,IAAI,CAACC,SAAS,CAACoa,OAAO,CAACW,MAAM,CAACF,KAAK,EAAE;UAAEpV,MAAM,EAAE;QAAK,CAAC,CAAC,CAAC,EACvDG,UACF,CAAC;MACH,CAAC,CAAC,OAAOmD,GAAG,EAAE;QACZ,IAAI2S,MAAM,GAAG3b,IAAI,CAACC,SAAS,CAAChD,IAAI,CAACC,MAAM,CAAC0e,aAAa,CAAC,GAAGd,KAAK,CAAC,CAAC,CAAC;QACjEY,UAAU,CACR,wBAAwBC,MAAM,2BAA2B,EACzD9V,UACF,CAAC;MACH;IACF;EACF,CAAC,SAAS;IACR9M,MAAM,CAAC8iB,WAAW,CAAC,CAAC;EACtB;EACA,IAAIC,SAAS,GAAGzB,OAAO,CAACW,MAAM,CAAC,CAAC;EAChC,IAAIc,SAAS,CAAC3d,MAAM,EAAE;IACpBud,UAAU,CAAC1b,IAAI,CAACC,SAAS,CAAC6b,SAAS,CAAC,EAAEjW,UAAU,CAAC;EACnD;AACF;AACA,SAAS6V,UAAUA,CAACZ,KAAK,EAAEjV,UAAU,EAAE;EACrCA,UAAU,CAACC,OAAO,CAChBmU,QAAQ,CAACplB,MAAM,CACb,WAAWknB,YAAY,CACrB,kCAAkCjB,KAAK,GACzC,CAAC,WACH,CACF,CAAC;AACH;AACA,SAASiB,YAAYA,CAACC,MAAM,EAAE;EAC5B,OAAOA,MAAM,CAACpmB,OAAO,CAAC,OAAO,EAAE,QAAQ,CAAC,CAACA,OAAO,CAAC,eAAe,EAAE,QAAQ,CAAC;AAC7E;;AAEA;AACA,eAAeqmB,qBAAqBA,CAAC;EACnCrZ,OAAO;EACPsZ,WAAW;EACXtJ,wBAAwB;EACxBuJ,UAAU;EACVxjB,OAAO,GAAG;AACZ,CAAC,EAAE;EACD,MAAMhC,GAAG,GAAG,IAAIE,GAAG,CAAC+L,OAAO,CAACjM,GAAG,CAAC;EAChC,MAAMylB,aAAa,GAAGC,oBAAoB,CAAC1lB,GAAG,CAAC;EAC/C,MAAM2lB,qBAAqB,GAAGF,aAAa,IAAIG,iBAAiB,CAAC5lB,GAAG,CAAC,IAAIiM,OAAO,CAACiB,OAAO,CAACwE,GAAG,CAAC,eAAe,CAAC;EAC7G,MAAMmU,cAAc,GAAG,MAAMN,WAAW,CAACtZ,OAAO,CAAC;EACjD,IAAI0Z,qBAAqB,IAAIE,cAAc,CAAC3Y,OAAO,CAACiB,GAAG,CAAC,uBAAuB,CAAC,KAAK,MAAM,EAAE;IAC3F,OAAO0X,cAAc;EACvB;EACA,IAAI,CAACA,cAAc,CAAC5Y,IAAI,EAAE;IACxB,MAAM,IAAIjJ,KAAK,CAAC,iCAAiC,CAAC;EACpD;EACA,IAAI8hB,eAAe,GAAG,IAAI;EAC1B,IAAI9jB,OAAO,EAAE;IACX8jB,eAAe,GAAGD,cAAc,CAACE,KAAK,CAAC,CAAC;EAC1C;EACA,MAAM9Y,IAAI,GAAG4Y,cAAc,CAAC5Y,IAAI;EAChC,IAAI+Y,cAAc;EAClB,MAAMC,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC7B,IAAID,cAAc,EAAE,OAAOA,cAAc;IACzCA,cAAc,GAAG/J,wBAAwB,CAAChP,IAAI,CAAC;IAC/C,OAAO+Y,cAAc;EACvB,CAAC;EACD,IAAI;IACF,MAAMpW,IAAI,GAAG,MAAM4V,UAAU,CAACS,UAAU,CAAC;IACzC,MAAM/Y,OAAO,GAAG,IAAI6D,OAAO,CAAC8U,cAAc,CAAC3Y,OAAO,CAAC;IACnDA,OAAO,CAACsG,GAAG,CAAC,cAAc,EAAE,WAAW,CAAC;IACxC,IAAI,CAACxR,OAAO,EAAE;MACZ,OAAO,IAAIsK,QAAQ,CAACsD,IAAI,EAAE;QACxBvD,MAAM,EAAEwZ,cAAc,CAACxZ,MAAM;QAC7Ba;MACF,CAAC,CAAC;IACJ;IACA,IAAI,CAAC4Y,eAAe,EAAE7Y,IAAI,EAAE;MAC1B,MAAM,IAAIjJ,KAAK,CAAC,iCAAiC,CAAC;IACpD;IACA,MAAMkiB,KAAK,GAAGtW,IAAI,CAACuW,WAAW,CAAC3C,gBAAgB,CAACsC,eAAe,CAAC7Y,IAAI,CAAC,CAAC;IACtE,OAAO,IAAIX,QAAQ,CAAC4Z,KAAK,EAAE;MACzB7Z,MAAM,EAAEwZ,cAAc,CAACxZ,MAAM;MAC7Ba;IACF,CAAC,CAAC;EACJ,CAAC,CAAC,OAAOkZ,MAAM,EAAE;IACf,IAAIA,MAAM,YAAY9Z,QAAQ,EAAE;MAC9B,OAAO8Z,MAAM;IACf;IACA,MAAMA,MAAM;EACd;AACF;AACA,SAASC,eAAeA,CAAC;EAAEJ;AAAW,CAAC,EAAE;EACvC,MAAMpJ,OAAO,GAAGwG,MAAM,CAACiD,GAAG,CAACL,UAAU,CAAC,CAAC,CAAC;EACxC,IAAIpJ,OAAO,CAACC,IAAI,KAAK,UAAU,EAAE;IAC/B,MAAM,IAAIxQ,QAAQ,CAAC,IAAI,EAAE;MACvBD,MAAM,EAAEwQ,OAAO,CAACxQ,MAAM;MACtBa,OAAO,EAAE;QACPqZ,QAAQ,EAAE1J,OAAO,CAAC/a;MACpB;IACF,CAAC,CAAC;EACJ;EACA,IAAI+a,OAAO,CAACC,IAAI,KAAK,QAAQ,EAAE,OAAO,IAAI;EAC1C,IAAI0J,iBAAiB,GAAG;IAAE,GAAG3J,OAAO,CAAClc;EAAW,CAAC;EACjD,KAAK,MAAMC,KAAK,IAAIic,OAAO,CAAChc,OAAO,EAAE;IACnC,IAAI3B,wBAAwB,CAC1B0B,KAAK,CAACI,EAAE,EACRJ,KAAK,CAACM,YAAY,EAClBN,KAAK,CAACO,SAAS,EACf,KACF,CAAC,KAAKP,KAAK,CAACod,sBAAsB,IAAI,CAACpd,KAAK,CAACO,SAAS,CAAC,EAAE;MACvD,OAAOqlB,iBAAiB,CAAC5lB,KAAK,CAACI,EAAE,CAAC;IACpC;EACF;EACA,MAAMjB,OAAO,GAAG;IACd0Q,UAAU,EAAEoM,OAAO,CAACpM,UAAU;IAC9BD,aAAa,EAAE,CAAC,CAAC;IACjB3E,QAAQ,EAAEgR,OAAO,CAAChR,QAAQ;IAC1BZ,MAAM,EAAE4R,OAAO,CAAC5R,MAAM;IACtBtK,UAAU,EAAE6lB,iBAAiB;IAC7B9V,aAAa,EAAE,CAAC,CAAC;IACjB5O,QAAQ,EAAE+a,OAAO,CAAC/a,QAAQ;IAC1BoQ,UAAU,EAAE,GAAG;IACfrR,OAAO,EAAEgc,OAAO,CAAChc,OAAO,CAACkD,GAAG,CAAEnD,KAAK,KAAM;MACvCkL,MAAM,EAAElL,KAAK,CAACkL,MAAM;MACpBF,QAAQ,EAAEhL,KAAK,CAACgL,QAAQ;MACxB6a,YAAY,EAAE7lB,KAAK,CAAC6lB,YAAY;MAChC1lB,KAAK,EAAE;QACLC,EAAE,EAAEJ,KAAK,CAACI,EAAE;QACZsD,MAAM,EAAE1D,KAAK,CAACgE,SAAS,IAAI,CAAC,CAAChE,KAAK,CAACuhB,YAAY;QAC/C1d,MAAM,EAAE7D,KAAK,CAAC6D,MAAM;QACpBO,gBAAgB,EAAEpE,KAAK,CAACoE,gBAAgB;QACxCR,MAAM,EAAE5D,KAAK,CAACO,SAAS,IAAI,CAAC,CAACP,KAAK,CAACM,YAAY;QAC/CiD,KAAK,EAAEvD,KAAK,CAACuD,KAAK;QAClBD,IAAI,EAAEtD,KAAK,CAACsD,IAAI;QAChBQ,gBAAgB,EAAE9D,KAAK,CAAC8D;MAC1B;IACF,CAAC,CAAC;EACJ,CAAC;EACD,MAAMrD,MAAM,GAAGrD,kBAAkB,CAC/B6e,OAAO,CAAChc,OAAO,CAAC8c,WAAW,CAAC,CAACC,QAAQ,EAAEhd,KAAK,KAAK;IAC/C,MAAMG,KAAK,GAAG;MACZC,EAAE,EAAEJ,KAAK,CAACI,EAAE;MACZsD,MAAM,EAAE1D,KAAK,CAACgE,SAAS,IAAI,CAAC,CAAChE,KAAK,CAACuhB,YAAY;MAC/ClD,OAAO,EAAEre,KAAK,CAACqe,OAAO;MACtB6C,YAAY,EAAElhB,KAAK,CAACkhB,YAAY;MAChCrd,MAAM,EAAE7D,KAAK,CAAC6D,MAAM;MACpBO,gBAAgB,EAAE,CAAC,CAACpE,KAAK,CAACkhB,YAAY;MACtC9D,sBAAsB,EAAEpd,KAAK,CAACod,sBAAsB;MACpD7Z,KAAK,EAAEvD,KAAK,CAACuD,KAAK;MAClBK,MAAM,EAAE5D,KAAK,CAACO,SAAS,IAAI,CAAC,CAACP,KAAK,CAACM,YAAY;MAC/CgD,IAAI,EAAEtD,KAAK,CAACsD,IAAI;MAChBQ,gBAAgB,EAAE9D,KAAK,CAAC8D;IAC1B,CAAC;IACD,IAAIkZ,QAAQ,CAACpW,MAAM,GAAG,CAAC,EAAE;MACvBzG,KAAK,CAACyE,QAAQ,GAAGoY,QAAQ;IAC3B;IACA,OAAO,CAAC7c,KAAK,CAAC;EAChB,CAAC,EAAE,EAAE,CAAC,EACNhB,OACF,CAAC;EACD,MAAMwhB,gBAAgB,GAAG;IACvB/gB,MAAM,EAAE;MACN;MACA;MACA4C,mBAAmB,EAAE,KAAK;MAC1BD,6BAA6B,EAAE;IACjC,CAAC;IACD1C,SAAS,EAAE,KAAK;IAChBiB,GAAG,EAAE,IAAI;IACTrB,WAAW,EAAE,EAAE;IACfF,QAAQ,EAAE;MACRI,MAAM,EAAE,CAAC,CAAC;MACViD,OAAO,EAAE,GAAG;MACZxD,GAAG,EAAE,EAAE;MACPqD,KAAK,EAAE;QACLE,MAAM,EAAE,EAAE;QACVD,OAAO,EAAE;MACX;IACF,CAAC;IACD3B,cAAc,EAAE;MAAE8B,IAAI,EAAE,MAAM;MAAEC,YAAY,EAAE;IAAc,CAAC;IAC7DtD,YAAY,EAAE,CAAC;EACjB,CAAC;EACD,OAAO,eAAgBijB,MAAM,CAAC/hB,aAAa,CAACpE,gBAAgB,CAACsE,QAAQ,EAAE;IAAEC,KAAK,EAAE;EAAK,CAAC,EAAE,eAAgB4hB,MAAM,CAAC/hB,aAAa,CAAC+Y,4BAA4B,EAAE;IAAEvY,QAAQ,EAAE+a,OAAO,CAAC/a;EAAS,CAAC,EAAE,eAAgBuhB,MAAM,CAAC/hB,aAAa,CAACvE,gBAAgB,CAACyE,QAAQ,EAAE;IAAEC,KAAK,EAAE8f;EAAiB,CAAC,EAAE,eAAgB8B,MAAM,CAAC/hB,aAAa,CAC1T/D,oBAAoB,EACpB;IACEwC,OAAO;IACPsB,MAAM;IACNW,OAAO,EAAE,KAAK;IACd/B,KAAK,EAAE4c,OAAO,CAAC5c;EACjB,CACF,CAAC,CAAC,CAAC,CAAC;AACN;AACA,SAASylB,oBAAoBA,CAAC1lB,GAAG,EAAE;EACjC,OAAOA,GAAG,CAAC4L,QAAQ,CAAC0J,QAAQ,CAAC,MAAM,CAAC;AACtC;AACA,SAASsQ,iBAAiBA,CAAC5lB,GAAG,EAAE;EAC9B,OAAOA,GAAG,CAAC4L,QAAQ,CAAC0J,QAAQ,CAAC,WAAW,CAAC;AAC3C;;AAEA;AACA,SAASoR,YAAYA,CAAA,EAAG;EACtB,IAAIC,QAAQ,GAAG,IAAI/gB,WAAW,CAAC,CAAC;EAChC,IAAIghB,gBAAgB,GAAG,IAAI;EAC3B,IAAInD,SAAS,GAAG,IAAIzU,cAAc,CAAC;IACjCC,KAAKA,CAACC,UAAU,EAAE;MAChB,IAAI,OAAOqN,MAAM,KAAK,WAAW,EAAE;QACjC;MACF;MACA,IAAIsK,WAAW,GAAI1C,KAAK,IAAK;QAC3B,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;UAC7BjV,UAAU,CAACC,OAAO,CAACwX,QAAQ,CAACzoB,MAAM,CAACimB,KAAK,CAAC,CAAC;QAC5C,CAAC,MAAM;UACLjV,UAAU,CAACC,OAAO,CAACgV,KAAK,CAAC;QAC3B;MACF,CAAC;MACD5H,MAAM,CAACuK,aAAa,KAAKvK,MAAM,CAACuK,aAAa,GAAG,EAAE,CAAC;MACnDvK,MAAM,CAACuK,aAAa,CAACvY,OAAO,CAACsY,WAAW,CAAC;MACzCtK,MAAM,CAACuK,aAAa,CAACja,IAAI,GAAIsX,KAAK,IAAK;QACrC0C,WAAW,CAAC1C,KAAK,CAAC;QAClB,OAAO,CAAC;MACV,CAAC;MACDyC,gBAAgB,GAAG1X,UAAU;IAC/B;EACF,CAAC,CAAC;EACF,IAAI,OAAOuR,QAAQ,KAAK,WAAW,IAAIA,QAAQ,CAACsG,UAAU,KAAK,SAAS,EAAE;IACxEtG,QAAQ,CAACvM,gBAAgB,CAAC,kBAAkB,EAAE,MAAM;MAClD0S,gBAAgB,EAAExX,KAAK,CAAC,CAAC;IAC3B,CAAC,CAAC;EACJ,CAAC,MAAM;IACLwX,gBAAgB,EAAExX,KAAK,CAAC,CAAC;EAC3B;EACA,OAAOqU,SAAS;AAClB;;AAEA;AACA,SAASuD,iBAAiBA,CAAC/b,MAAM,EAAE;EACjC,IAAI,CAACA,MAAM,EAAE,OAAO,IAAI;EACxB,IAAIC,OAAO,GAAGb,MAAM,CAACa,OAAO,CAACD,MAAM,CAAC;EACpC,IAAIM,UAAU,GAAG,CAAC,CAAC;EACnB,KAAK,IAAI,CAACvF,GAAG,EAAEwF,GAAG,CAAC,IAAIN,OAAO,EAAE;IAC9B,IAAIM,GAAG,IAAIA,GAAG,CAACC,MAAM,KAAK,oBAAoB,EAAE;MAC9CF,UAAU,CAACvF,GAAG,CAAC,GAAG,IAAIlJ,iBAAiB,CACrC0O,GAAG,CAACa,MAAM,EACVb,GAAG,CAAC8I,UAAU,EACd9I,GAAG,CAAC+D,IAAI,EACR/D,GAAG,CAACyb,QAAQ,KAAK,IACnB,CAAC;IACH,CAAC,MAAM,IAAIzb,GAAG,IAAIA,GAAG,CAACC,MAAM,KAAK,OAAO,EAAE;MACxC,IAAID,GAAG,CAACE,SAAS,EAAE;QACjB,IAAIwb,gBAAgB,GAAG3K,MAAM,CAAC/Q,GAAG,CAACE,SAAS,CAAC;QAC5C,IAAI,OAAOwb,gBAAgB,KAAK,UAAU,EAAE;UAC1C,IAAI;YACF,IAAIhgB,KAAK,GAAG,IAAIggB,gBAAgB,CAAC1b,GAAG,CAACH,OAAO,CAAC;YAC7CnE,KAAK,CAAC6D,KAAK,GAAGS,GAAG,CAACT,KAAK;YACvBQ,UAAU,CAACvF,GAAG,CAAC,GAAGkB,KAAK;UACzB,CAAC,CAAC,OAAOkH,CAAC,EAAE,CACZ;QACF;MACF;MACA,IAAI7C,UAAU,CAACvF,GAAG,CAAC,IAAI,IAAI,EAAE;QAC3B,IAAIkB,KAAK,GAAG,IAAIlD,KAAK,CAACwH,GAAG,CAACH,OAAO,CAAC;QAClCnE,KAAK,CAAC6D,KAAK,GAAGS,GAAG,CAACT,KAAK;QACvBQ,UAAU,CAACvF,GAAG,CAAC,GAAGkB,KAAK;MACzB;IACF,CAAC,MAAM;MACLqE,UAAU,CAACvF,GAAG,CAAC,GAAGwF,GAAG;IACvB;EACF;EACA,OAAOD,UAAU;AACnB;AAEA,SACEzL,YAAY,EACZ2C,gBAAgB,EAChBkF,YAAY,EACZkB,QAAQ,EACR4B,UAAU,EACViD,iBAAiB,EACjBmH,oBAAoB,EACpBoD,aAAa,EACbK,SAAS,EACTC,oBAAoB,EACpBW,0BAA0B,EAC1BG,0BAA0B,EAC1B/L,IAAI,EACJwM,gBAAgB,EAChBgC,2BAA2B,EAC3BE,gBAAgB,EAChBwD,iBAAiB,EACjB8F,qBAAqB,EACrBe,eAAe,EACfK,YAAY,EACZM,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}