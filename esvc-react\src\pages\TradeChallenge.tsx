import React from 'react';

const TradeChallenge: React.FC = () => {
  return (
    <main className="w-full bg-neutral-900 min-h-screen">
      <div className="container mx-auto px-4 py-20">
        <div className="text-center">
          <h1 className="text-4xl font-bold text-white mb-8">Trade Challenge</h1>
          <p className="text-neutral-300 text-lg mb-8">
            Join our exclusive trading challenge and compete with other traders for substantial rewards.
          </p>
          
          <div className="grid md:grid-cols-2 gap-8 max-w-4xl mx-auto">
            <div className="bg-neutral-800 rounded-lg p-6">
              <h3 className="text-xl font-semibold text-white mb-4">Challenge Overview</h3>
              <p className="text-neutral-300 mb-4">
                Participate in our monthly trading challenge where skilled traders compete for prizes 
                and recognition in the ESVC community.
              </p>
              <ul className="text-neutral-300 text-left space-y-2">
                <li>• Monthly competitions</li>
                <li>• Substantial prize pools</li>
                <li>• Professional trading tools</li>
                <li>• Community leaderboards</li>
              </ul>
            </div>
            
            <div className="bg-neutral-800 rounded-lg p-6">
              <h3 className="text-xl font-semibold text-white mb-4">How to Participate</h3>
              <p className="text-neutral-300 mb-4">
                Getting started with the trade challenge is simple and straightforward.
              </p>
              <ol className="text-neutral-300 text-left space-y-2">
                <li>1. Register for the challenge</li>
                <li>2. Complete your profile</li>
                <li>3. Start trading with virtual funds</li>
                <li>4. Climb the leaderboard</li>
              </ol>
            </div>
          </div>
          
          <div className="mt-12">
            <button className="bg-[#bf4129] hover:bg-[#a83a25] text-white py-3 px-8 rounded-full font-semibold text-lg transition-colors">
              Join Challenge
            </button>
          </div>
        </div>
      </div>
    </main>
  );
};

export default TradeChallenge;
