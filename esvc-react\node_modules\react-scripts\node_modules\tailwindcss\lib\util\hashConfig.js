"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
Object.defineProperty(exports, "default", {
    enumerable: true,
    get: function() {
        return hashConfig;
    }
});
const _objecthash = /*#__PURE__*/ _interop_require_default(require("object-hash"));
function _interop_require_default(obj) {
    return obj && obj.__esModule ? obj : {
        default: obj
    };
}
function hashConfig(config) {
    return (0, _objecthash.default)(config, {
        ignoreUnknown: true
    });
}
