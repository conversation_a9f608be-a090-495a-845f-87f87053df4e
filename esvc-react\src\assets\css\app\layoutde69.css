/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[2].use[1]!./node_modules/next/dist/build/webpack/loaders/next-font-loader/index.js??ruleSet[1].rules[13].oneOf[2].use[2]!./node_modules/next/font/google/target.css?{"path":"app/layout.tsx","import":"Londrina_Outline","arguments":[{"weight":"400","subsets":["latin"]}],"variableName":"londrina_outline"} ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/* latin */
@font-face {
  font-family: 'Londrina Outline';
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: url(../fonts/595e4c7b8597e85f-s.p.woff2) format('woff2');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}@font-face {font-family: 'Londrina Outline Fallback';src: local("Arial");ascent-override: 110.00%;descent-override: 27.70%;line-gap-override: 0.00%;size-adjust: 85.91%
}.__className_359f50 {font-family: 'Londrina Outline', 'Londrina Outline Fallback';font-weight: 400;font-style: normal
}

/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[2].use[1]!./node_modules/next/dist/build/webpack/loaders/next-font-loader/index.js??ruleSet[1].rules[13].oneOf[2].use[2]!./node_modules/next/font/google/target.css?{"path":"app/layout.tsx","import":"Montserrat","arguments":[{"variable":"--font-montserrat","adjustFontFallback":false,"subsets":["latin"]}],"variableName":"montserrat"} ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/* cyrillic-ext */
@font-face {
  font-family: 'Montserrat';
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url(../fonts/3f69592b2fe603c7.woff2) format('woff2');
  unicode-range: U+0460-052F, U+1C80-1C8A, U+20B4, U+2DE0-2DFF, U+A640-A69F, U+FE2E-FE2F;
}
/* cyrillic */
@font-face {
  font-family: 'Montserrat';
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url(../fonts/6325a8417175c41d.woff2) format('woff2');
  unicode-range: U+0301, U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116;
}
/* vietnamese */
@font-face {
  font-family: 'Montserrat';
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url(../fonts/021bc4481ed92ece.woff2) format('woff2');
  unicode-range: U+0102-0103, U+0110-0111, U+0128-0129, U+0168-0169, U+01A0-01A1, U+01AF-01B0, U+0300-0301, U+0303-0304, U+0308-0309, U+0323, U+0329, U+1EA0-1EF9, U+20AB;
}
/* latin-ext */
@font-face {
  font-family: 'Montserrat';
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url(../fonts/99b7f73d5af7c3e2.woff2) format('woff2');
  unicode-range: U+0100-02BA, U+02BD-02C5, U+02C7-02CC, U+02CE-02D7, U+02DD-02FF, U+0304, U+0308, U+0329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}
/* latin */
@font-face {
  font-family: 'Montserrat';
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url(../fonts/4f05ba3a6752a328.p.woff2) format('woff2');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}.__className_7edfb1 {font-family: 'Montserrat';font-style: normal
}.__variable_7edfb1 {--font-montserrat: 'Montserrat'
}

/*!*************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[3]!./app/globals.css ***!
  \*************************************************************************************************************************************************************************************************************************************************************/
/*! tailwindcss v4.1.10 | MIT License | https://tailwindcss.com */
@layer properties;
@layer theme, base, components, utilities;
@layer theme {
  :root, :host {
    --color-red-300: oklch(80.8% 0.114 19.571);
    --color-red-400: oklch(70.4% 0.191 22.216);
    --color-red-500: oklch(63.7% 0.237 25.331);
    --color-red-600: oklch(57.7% 0.245 27.325);
    --color-red-700: oklch(50.5% 0.213 27.518);
    --color-orange-400: oklch(75% 0.183 55.934);
    --color-orange-500: oklch(70.5% 0.213 47.604);
    --color-orange-600: oklch(64.6% 0.222 41.116);
    --color-orange-700: oklch(55.3% 0.195 38.402);
    --color-yellow-300: oklch(90.5% 0.182 98.111);
    --color-yellow-400: oklch(85.2% 0.199 91.936);
    --color-yellow-500: oklch(79.5% 0.184 86.047);
    --color-yellow-600: oklch(68.1% 0.162 75.834);
    --color-green-400: oklch(79.2% 0.209 151.711);
    --color-green-500: oklch(72.3% 0.219 149.579);
    --color-green-900: oklch(39.3% 0.095 152.535);
    --color-blue-300: oklch(80.9% 0.105 251.813);
    --color-blue-400: oklch(70.7% 0.165 254.624);
    --color-blue-500: oklch(62.3% 0.214 259.815);
    --color-blue-600: oklch(54.6% 0.245 262.881);
    --color-blue-900: oklch(37.9% 0.146 265.522);
    --color-gray-100: oklch(96.7% 0.003 264.542);
    --color-gray-300: oklch(87.2% 0.01 258.338);
    --color-gray-400: oklch(70.7% 0.022 261.325);
    --color-gray-500: oklch(55.1% 0.027 264.364);
    --color-gray-600: oklch(44.6% 0.03 256.802);
    --color-gray-700: oklch(37.3% 0.034 259.733);
    --color-gray-800: oklch(27.8% 0.033 256.848);
    --color-gray-900: oklch(21% 0.034 264.665);
    --color-neutral-50: oklch(98.5% 0 0);
    --color-neutral-100: oklch(97% 0 0);
    --color-neutral-300: oklch(87% 0 0);
    --color-neutral-500: oklch(55.6% 0 0);
    --color-neutral-600: oklch(43.9% 0 0);
    --color-neutral-700: oklch(37.1% 0 0);
    --color-neutral-800: oklch(26.9% 0 0);
    --color-neutral-900: oklch(20.5% 0 0);
    --color-black: #000;
    --color-white: #fff;
    --spacing: 0.25rem;
    --container-3xs: 16rem;
    --container-xs: 20rem;
    --container-sm: 24rem;
    --container-md: 28rem;
    --container-xl: 36rem;
    --container-2xl: 42rem;
    --container-4xl: 56rem;
    --container-5xl: 64rem;
    --text-xs: 0.75rem;
    --text-xs--line-height: calc(1 / 0.75);
    --text-sm: 0.875rem;
    --text-sm--line-height: calc(1.25 / 0.875);
    --text-base: 1rem;
    --text-base--line-height: calc(1.5 / 1);
    --text-lg: 1.125rem;
    --text-lg--line-height: calc(1.75 / 1.125);
    --text-xl: 1.25rem;
    --text-xl--line-height: calc(1.75 / 1.25);
    --text-2xl: 1.5rem;
    --text-2xl--line-height: calc(2 / 1.5);
    --text-3xl: 1.875rem;
    --text-3xl--line-height: calc(2.25 / 1.875);
    --text-4xl: 2.25rem;
    --text-4xl--line-height: calc(2.5 / 2.25);
    --text-5xl: 3rem;
    --text-5xl--line-height: 1;
    --font-weight-normal: 400;
    --font-weight-medium: 500;
    --font-weight-semibold: 600;
    --font-weight-bold: 700;
    --tracking-tight: -0.025em;
    --leading-normal: 1.5;
    --leading-relaxed: 1.625;
    --radius-sm: 0.25rem;
    --radius-md: 0.375rem;
    --radius-lg: 0.5rem;
    --radius-xl: 0.75rem;
    --radius-2xl: 1rem;
    --radius-3xl: 1.5rem;
    --radius-4xl: 2rem;
    --ease-in-out: cubic-bezier(0.4, 0, 0.2, 1);
    --default-transition-duration: 150ms;
    --default-transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    --default-font-family: var(--font-geist-sans);
    --default-mono-font-family: var(--font-geist-mono);
  }
}
@layer base {
  *, ::after, ::before, ::backdrop, ::file-selector-button {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
    border: 0 solid;
  }
  html, :host {
    line-height: 1.5;
    -webkit-text-size-adjust: 100%;
    tab-size: 4;
    font-family: var(--default-font-family, ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji");
    font-feature-settings: var(--default-font-feature-settings, normal);
    font-variation-settings: var(--default-font-variation-settings, normal);
    -webkit-tap-highlight-color: transparent;
  }
  hr {
    height: 0;
    color: inherit;
    border-top-width: 1px;
  }
  abbr:where([title]) {
    -webkit-text-decoration: underline dotted;
    text-decoration: underline dotted;
  }
  h1, h2, h3, h4, h5, h6 {
    font-size: inherit;
    font-weight: inherit;
  }
  a {
    color: inherit;
    -webkit-text-decoration: inherit;
    text-decoration: inherit;
  }
  b, strong {
    font-weight: bolder;
  }
  code, kbd, samp, pre {
    font-family: var(--default-mono-font-family, ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace);
    font-feature-settings: var(--default-mono-font-feature-settings, normal);
    font-variation-settings: var(--default-mono-font-variation-settings, normal);
    font-size: 1em;
  }
  small {
    font-size: 80%;
  }
  sub, sup {
    font-size: 75%;
    line-height: 0;
    position: relative;
    vertical-align: baseline;
  }
  sub {
    bottom: -0.25em;
  }
  sup {
    top: -0.5em;
  }
  table {
    text-indent: 0;
    border-color: inherit;
    border-collapse: collapse;
  }
  :-moz-focusring {
    outline: auto;
  }
  progress {
    vertical-align: baseline;
  }
  summary {
    display: list-item;
  }
  ol, ul, menu {
    list-style: none;
  }
  img, svg, video, canvas, audio, iframe, embed, object {
    display: block;
    vertical-align: middle;
  }
  img, video {
    max-width: 100%;
    height: auto;
  }
  button, input, select, optgroup, textarea, ::file-selector-button {
    font: inherit;
    font-feature-settings: inherit;
    font-variation-settings: inherit;
    letter-spacing: inherit;
    color: inherit;
    border-radius: 0;
    background-color: transparent;
    opacity: 1;
  }
  :where(select:is([multiple], [size])) optgroup {
    font-weight: bolder;
  }
  :where(select:is([multiple], [size])) optgroup option {
    padding-inline-start: 20px;
  }
  ::file-selector-button {
    margin-inline-end: 4px;
  }
  ::placeholder {
    opacity: 1;
  }
  @supports (not (-webkit-appearance: -apple-pay-button))  or (contain-intrinsic-size: 1px) {
    ::placeholder {
      color: currentcolor;
      @supports (color: color-mix(in lab, red, red)) {
        color: color-mix(in oklab, currentcolor 50%, transparent);
      }
    }
  }
  textarea {
    resize: vertical;
  }
  ::-webkit-search-decoration {
    -webkit-appearance: none;
  }
  ::-webkit-date-and-time-value {
    min-height: 1lh;
    text-align: inherit;
  }
  ::-webkit-datetime-edit {
    display: inline-flex;
  }
  ::-webkit-datetime-edit-fields-wrapper {
    padding: 0;
  }
  ::-webkit-datetime-edit, ::-webkit-datetime-edit-year-field, ::-webkit-datetime-edit-month-field, ::-webkit-datetime-edit-day-field, ::-webkit-datetime-edit-hour-field, ::-webkit-datetime-edit-minute-field, ::-webkit-datetime-edit-second-field, ::-webkit-datetime-edit-millisecond-field, ::-webkit-datetime-edit-meridiem-field {
    padding-block: 0;
  }
  :-moz-ui-invalid {
    box-shadow: none;
  }
  button, input:where([type="button"], [type="reset"], [type="submit"]), ::file-selector-button {
    appearance: button;
  }
  ::-webkit-inner-spin-button, ::-webkit-outer-spin-button {
    height: auto;
  }
  [hidden]:where(:not([hidden="until-found"])) {
    display: none !important;
  }
}
@layer utilities {
  .pointer-events-none {
    pointer-events: none;
  }
  .absolute {
    position: absolute;
  }
  .fixed {
    position: fixed;
  }
  .relative {
    position: relative;
  }
  .static {
    position: static;
  }
  .inset-0 {
    inset: calc(var(--spacing) * 0);
  }
  .inset-y-0 {
    inset-block: calc(var(--spacing) * 0);
  }
  .top-0 {
    top: calc(var(--spacing) * 0);
  }
  .top-1\/2 {
    top: calc(1/2 * 100%);
  }
  .top-3 {
    top: calc(var(--spacing) * 3);
  }
  .top-4 {
    top: calc(var(--spacing) * 4);
  }
  .top-6 {
    top: calc(var(--spacing) * 6);
  }
  .top-20 {
    top: calc(var(--spacing) * 20);
  }
  .top-90 {
    top: calc(var(--spacing) * 90);
  }
  .top-\[-55px\] {
    top: -55px;
  }
  .top-\[-70px\] {
    top: -70px;
  }
  .top-\[0px\] {
    top: 0px;
  }
  .top-\[1px\] {
    top: 1px;
  }
  .top-\[42px\] {
    top: 42px;
  }
  .top-\[43px\] {
    top: 43px;
  }
  .top-\[55px\] {
    top: 55px;
  }
  .top-\[60\%\] {
    top: 60%;
  }
  .top-\[73px\] {
    top: 73px;
  }
  .top-\[120px\] {
    top: 120px;
  }
  .top-\[141px\] {
    top: 141px;
  }
  .top-\[179px\] {
    top: 179px;
  }
  .top-\[220px\] {
    top: 220px;
  }
  .top-\[232px\] {
    top: 232px;
  }
  .top-\[280px\] {
    top: 280px;
  }
  .top-\[401px\] {
    top: 401px;
  }
  .top-\[492px\] {
    top: 492px;
  }
  .top-\[599px\] {
    top: 599px;
  }
  .top-full {
    top: 100%;
  }
  .right-0 {
    right: calc(var(--spacing) * 0);
  }
  .right-1 {
    right: calc(var(--spacing) * 1);
  }
  .right-3 {
    right: calc(var(--spacing) * 3);
  }
  .right-4 {
    right: calc(var(--spacing) * 4);
  }
  .right-6 {
    right: calc(var(--spacing) * 6);
  }
  .right-\[-10px\] {
    right: -10px;
  }
  .right-\[-20px\] {
    right: -20px;
  }
  .right-\[20px\] {
    right: 20px;
  }
  .bottom-0 {
    bottom: calc(var(--spacing) * 0);
  }
  .bottom-7 {
    bottom: calc(var(--spacing) * 7);
  }
  .bottom-17 {
    bottom: calc(var(--spacing) * 17);
  }
  .bottom-\[40px\] {
    bottom: 40px;
  }
  .bottom-\[80px\] {
    bottom: 80px;
  }
  .left-0 {
    left: calc(var(--spacing) * 0);
  }
  .left-1\/2 {
    left: calc(1/2 * 100%);
  }
  .left-3 {
    left: calc(var(--spacing) * 3);
  }
  .left-\[10px\] {
    left: 10px;
  }
  .left-\[20px\] {
    left: 20px;
  }
  .left-\[22px\] {
    left: 22px;
  }
  .left-\[41px\] {
    left: 41px;
  }
  .left-\[90px\] {
    left: 90px;
  }
  .left-\[195px\] {
    left: 195px;
  }
  .left-\[407px\] {
    left: 407px;
  }
  .z-10 {
    z-index: 10;
  }
  .z-40 {
    z-index: 40;
  }
  .z-50 {
    z-index: 50;
  }
  .z-\[-10\] {
    z-index: -10;
  }
  .z-\[1\] {
    z-index: 1;
  }
  .container {
    width: 100%;
    @media (width >= 40rem) {
      max-width: 40rem;
    }
    @media (width >= 48rem) {
      max-width: 48rem;
    }
    @media (width >= 64rem) {
      max-width: 64rem;
    }
    @media (width >= 80rem) {
      max-width: 80rem;
    }
    @media (width >= 96rem) {
      max-width: 96rem;
    }
  }
  .m-4 {
    margin: calc(var(--spacing) * 4);
  }
  .m-5 {
    margin: calc(var(--spacing) * 5);
  }
  .mx-5 {
    margin-inline: calc(var(--spacing) * 5);
  }
  .mx-10 {
    margin-inline: calc(var(--spacing) * 10);
  }
  .mx-auto {
    margin-inline: auto;
  }
  .my-1 {
    margin-block: calc(var(--spacing) * 1);
  }
  .my-2 {
    margin-block: calc(var(--spacing) * 2);
  }
  .my-3 {
    margin-block: calc(var(--spacing) * 3);
  }
  .my-5 {
    margin-block: calc(var(--spacing) * 5);
  }
  .my-8 {
    margin-block: calc(var(--spacing) * 8);
  }
  .my-10 {
    margin-block: calc(var(--spacing) * 10);
  }
  .mt-1 {
    margin-top: calc(var(--spacing) * 1);
  }
  .mt-1\.5 {
    margin-top: calc(var(--spacing) * 1.5);
  }
  .mt-2 {
    margin-top: calc(var(--spacing) * 2);
  }
  .mt-3 {
    margin-top: calc(var(--spacing) * 3);
  }
  .mt-4 {
    margin-top: calc(var(--spacing) * 4);
  }
  .mt-5 {
    margin-top: calc(var(--spacing) * 5);
  }
  .mt-6 {
    margin-top: calc(var(--spacing) * 6);
  }
  .mt-8 {
    margin-top: calc(var(--spacing) * 8);
  }
  .mt-10 {
    margin-top: calc(var(--spacing) * 10);
  }
  .mt-20 {
    margin-top: calc(var(--spacing) * 20);
  }
  .mt-\[-1\.00px\] {
    margin-top: -1.00px;
  }
  .mt-\[30px\] {
    margin-top: 30px;
  }
  .mt-\[35px\] {
    margin-top: 35px;
  }
  .mt-\[47px\] {
    margin-top: 47px;
  }
  .mr-1 {
    margin-right: calc(var(--spacing) * 1);
  }
  .mr-2 {
    margin-right: calc(var(--spacing) * 2);
  }
  .mr-3 {
    margin-right: calc(var(--spacing) * 3);
  }
  .mr-4 {
    margin-right: calc(var(--spacing) * 4);
  }
  .mb-1 {
    margin-bottom: calc(var(--spacing) * 1);
  }
  .mb-2 {
    margin-bottom: calc(var(--spacing) * 2);
  }
  .mb-3 {
    margin-bottom: calc(var(--spacing) * 3);
  }
  .mb-4 {
    margin-bottom: calc(var(--spacing) * 4);
  }
  .mb-5 {
    margin-bottom: calc(var(--spacing) * 5);
  }
  .mb-6 {
    margin-bottom: calc(var(--spacing) * 6);
  }
  .mb-8 {
    margin-bottom: calc(var(--spacing) * 8);
  }
  .mb-10 {
    margin-bottom: calc(var(--spacing) * 10);
  }
  .mb-12 {
    margin-bottom: calc(var(--spacing) * 12);
  }
  .mb-20 {
    margin-bottom: calc(var(--spacing) * 20);
  }
  .ml-1 {
    margin-left: calc(var(--spacing) * 1);
  }
  .ml-2 {
    margin-left: calc(var(--spacing) * 2);
  }
  .ml-3 {
    margin-left: calc(var(--spacing) * 3);
  }
  .ml-4 {
    margin-left: calc(var(--spacing) * 4);
  }
  .ml-auto {
    margin-left: auto;
  }
  .block {
    display: block;
  }
  .contents {
    display: contents;
  }
  .flex {
    display: flex;
  }
  .grid {
    display: grid;
  }
  .hidden {
    display: none;
  }
  .inline-flex {
    display: inline-flex;
  }
  .table {
    display: table;
  }
  .h-1\.5 {
    height: calc(var(--spacing) * 1.5);
  }
  .h-2 {
    height: calc(var(--spacing) * 2);
  }
  .h-2\.5 {
    height: calc(var(--spacing) * 2.5);
  }
  .h-3 {
    height: calc(var(--spacing) * 3);
  }
  .h-4 {
    height: calc(var(--spacing) * 4);
  }
  .h-5 {
    height: calc(var(--spacing) * 5);
  }
  .h-6 {
    height: calc(var(--spacing) * 6);
  }
  .h-8 {
    height: calc(var(--spacing) * 8);
  }
  .h-9 {
    height: calc(var(--spacing) * 9);
  }
  .h-10 {
    height: calc(var(--spacing) * 10);
  }
  .h-11 {
    height: calc(var(--spacing) * 11);
  }
  .h-12 {
    height: calc(var(--spacing) * 12);
  }
  .h-14 {
    height: calc(var(--spacing) * 14);
  }
  .h-16 {
    height: calc(var(--spacing) * 16);
  }
  .h-40 {
    height: calc(var(--spacing) * 40);
  }
  .h-64 {
    height: calc(var(--spacing) * 64);
  }
  .h-\[1px\] {
    height: 1px;
  }
  .h-\[13px\] {
    height: 13px;
  }
  .h-\[18px\] {
    height: 18px;
  }
  .h-\[19px\] {
    height: 19px;
  }
  .h-\[32px\] {
    height: 32px;
  }
  .h-\[38px\] {
    height: 38px;
  }
  .h-\[50px\] {
    height: 50px;
  }
  .h-\[52px\] {
    height: 52px;
  }
  .h-\[64px\] {
    height: 64px;
  }
  .h-\[72px\] {
    height: 72px;
  }
  .h-\[78px\] {
    height: 78px;
  }
  .h-\[79px\] {
    height: 79px;
  }
  .h-\[84px\] {
    height: 84px;
  }
  .h-\[94px\] {
    height: 94px;
  }
  .h-\[106px\] {
    height: 106px;
  }
  .h-\[234px\] {
    height: 234px;
  }
  .h-\[239px\] {
    height: 239px;
  }
  .h-\[316px\] {
    height: 316px;
  }
  .h-\[350px\] {
    height: 350px;
  }
  .h-\[381px\] {
    height: 381px;
  }
  .h-\[var\(--radix-navigation-menu-viewport-height\)\] {
    height: var(--radix-navigation-menu-viewport-height);
  }
  .h-full {
    height: 100%;
  }
  .min-h-screen {
    min-height: 100vh;
  }
  .w-1\/3 {
    width: calc(1/3 * 100%);
  }
  .w-2 {
    width: calc(var(--spacing) * 2);
  }
  .w-2\.5 {
    width: calc(var(--spacing) * 2.5);
  }
  .w-3 {
    width: calc(var(--spacing) * 3);
  }
  .w-4 {
    width: calc(var(--spacing) * 4);
  }
  .w-4\/5 {
    width: calc(4/5 * 100%);
  }
  .w-5 {
    width: calc(var(--spacing) * 5);
  }
  .w-6 {
    width: calc(var(--spacing) * 6);
  }
  .w-8 {
    width: calc(var(--spacing) * 8);
  }
  .w-9 {
    width: calc(var(--spacing) * 9);
  }
  .w-10 {
    width: calc(var(--spacing) * 10);
  }
  .w-11 {
    width: calc(var(--spacing) * 11);
  }
  .w-12 {
    width: calc(var(--spacing) * 12);
  }
  .w-16 {
    width: calc(var(--spacing) * 16);
  }
  .w-40 {
    width: calc(var(--spacing) * 40);
  }
  .w-44 {
    width: calc(var(--spacing) * 44);
  }
  .w-58 {
    width: calc(var(--spacing) * 58);
  }
  .w-60 {
    width: calc(var(--spacing) * 60);
  }
  .w-\[1px\] {
    width: 1px;
  }
  .w-\[32px\] {
    width: 32px;
  }
  .w-\[39px\] {
    width: 39px;
  }
  .w-\[52px\] {
    width: 52px;
  }
  .w-\[64px\] {
    width: 64px;
  }
  .w-\[78px\] {
    width: 78px;
  }
  .w-\[79px\] {
    width: 79px;
  }
  .w-\[84px\] {
    width: 84px;
  }
  .w-\[94px\] {
    width: 94px;
  }
  .w-\[106px\] {
    width: 106px;
  }
  .w-\[154px\] {
    width: 154px;
  }
  .w-\[165px\] {
    width: 165px;
  }
  .w-\[167px\] {
    width: 167px;
  }
  .w-\[200px\] {
    width: 200px;
  }
  .w-\[234px\] {
    width: 234px;
  }
  .w-\[239px\] {
    width: 239px;
  }
  .w-\[316px\] {
    width: 316px;
  }
  .w-\[353px\] {
    width: 353px;
  }
  .w-\[381px\] {
    width: 381px;
  }
  .w-fit {
    width: fit-content;
  }
  .w-full {
    width: 100%;
  }
  .w-max {
    width: max-content;
  }
  .w-px {
    width: 1px;
  }
  .max-w-2xl {
    max-width: var(--container-2xl);
  }
  .max-w-4xl {
    max-width: var(--container-4xl);
  }
  .max-w-5xl {
    max-width: var(--container-5xl);
  }
  .max-w-\[517px\] {
    max-width: 517px;
  }
  .max-w-\[588px\] {
    max-width: 588px;
  }
  .max-w-\[627px\] {
    max-width: 627px;
  }
  .max-w-\[670px\] {
    max-width: 670px;
  }
  .max-w-\[880px\] {
    max-width: 880px;
  }
  .max-w-\[1082px\] {
    max-width: 1082px;
  }
  .max-w-\[1200px\] {
    max-width: 1200px;
  }
  .max-w-\[1440px\] {
    max-width: 1440px;
  }
  .max-w-full {
    max-width: 100%;
  }
  .max-w-max {
    max-width: max-content;
  }
  .max-w-md {
    max-width: var(--container-md);
  }
  .max-w-sm {
    max-width: var(--container-sm);
  }
  .max-w-xl {
    max-width: var(--container-xl);
  }
  .max-w-xs {
    max-width: var(--container-xs);
  }
  .min-w-full {
    min-width: 100%;
  }
  .min-w-screen {
    min-width: 100vw;
  }
  .flex-1 {
    flex: 1;
  }
  .flex-shrink-0 {
    flex-shrink: 0;
  }
  .flex-shrink-1 {
    flex-shrink: 1;
  }
  .shrink-0 {
    flex-shrink: 0;
  }
  .flex-grow {
    flex-grow: 1;
  }
  .-translate-x-1\/2 {
    --tw-translate-x: calc(calc(1/2 * 100%) * -1);
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }
  .translate-x-0 {
    --tw-translate-x: calc(var(--spacing) * 0);
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }
  .translate-x-full {
    --tw-translate-x: 100%;
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }
  .-translate-y-1\/2 {
    --tw-translate-y: calc(calc(1/2 * 100%) * -1);
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }
  .-translate-y-\[-20px\] {
    --tw-translate-y: calc(-20px * -1);
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }
  .rotate-45 {
    rotate: 45deg;
  }
  .rotate-90 {
    rotate: 90deg;
  }
  .rotate-180 {
    rotate: 180deg;
  }
  .transform {
    transform: var(--tw-rotate-x,) var(--tw-rotate-y,) var(--tw-rotate-z,) var(--tw-skew-x,) var(--tw-skew-y,);
  }
  .cursor-default {
    cursor: default;
  }
  .cursor-pointer {
    cursor: pointer;
  }
  .list-none {
    list-style-type: none;
  }
  .appearance-none {
    appearance: none;
  }
  .grid-cols-1 {
    grid-template-columns: repeat(1, minmax(0, 1fr));
  }
  .grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }
  .grid-rows-\[0fr\] {
    grid-template-rows: 0fr;
  }
  .grid-rows-\[1fr\] {
    grid-template-rows: 1fr;
  }
  .flex-col {
    flex-direction: column;
  }
  .items-baseline {
    align-items: baseline;
  }
  .items-center {
    align-items: center;
  }
  .items-end {
    align-items: flex-end;
  }
  .items-start {
    align-items: flex-start;
  }
  .justify-between {
    justify-content: space-between;
  }
  .justify-center {
    justify-content: center;
  }
  .gap-1 {
    gap: calc(var(--spacing) * 1);
  }
  .gap-2 {
    gap: calc(var(--spacing) * 2);
  }
  .gap-2\.5 {
    gap: calc(var(--spacing) * 2.5);
  }
  .gap-3 {
    gap: calc(var(--spacing) * 3);
  }
  .gap-4 {
    gap: calc(var(--spacing) * 4);
  }
  .gap-5 {
    gap: calc(var(--spacing) * 5);
  }
  .gap-6 {
    gap: calc(var(--spacing) * 6);
  }
  .gap-8 {
    gap: calc(var(--spacing) * 8);
  }
  .gap-10 {
    gap: calc(var(--spacing) * 10);
  }
  .gap-\[30px\] {
    gap: 30px;
  }
  .space-y-1 {
    :where(& > :not(:last-child)) {
      --tw-space-y-reverse: 0;
      margin-block-start: calc(calc(var(--spacing) * 1) * var(--tw-space-y-reverse));
      margin-block-end: calc(calc(var(--spacing) * 1) * calc(1 - var(--tw-space-y-reverse)));
    }
  }
  .space-y-1\.5 {
    :where(& > :not(:last-child)) {
      --tw-space-y-reverse: 0;
      margin-block-start: calc(calc(var(--spacing) * 1.5) * var(--tw-space-y-reverse));
      margin-block-end: calc(calc(var(--spacing) * 1.5) * calc(1 - var(--tw-space-y-reverse)));
    }
  }
  .space-y-2 {
    :where(& > :not(:last-child)) {
      --tw-space-y-reverse: 0;
      margin-block-start: calc(calc(var(--spacing) * 2) * var(--tw-space-y-reverse));
      margin-block-end: calc(calc(var(--spacing) * 2) * calc(1 - var(--tw-space-y-reverse)));
    }
  }
  .space-y-4 {
    :where(& > :not(:last-child)) {
      --tw-space-y-reverse: 0;
      margin-block-start: calc(calc(var(--spacing) * 4) * var(--tw-space-y-reverse));
      margin-block-end: calc(calc(var(--spacing) * 4) * calc(1 - var(--tw-space-y-reverse)));
    }
  }
  .space-y-6 {
    :where(& > :not(:last-child)) {
      --tw-space-y-reverse: 0;
      margin-block-start: calc(calc(var(--spacing) * 6) * var(--tw-space-y-reverse));
      margin-block-end: calc(calc(var(--spacing) * 6) * calc(1 - var(--tw-space-y-reverse)));
    }
  }
  .space-y-8 {
    :where(& > :not(:last-child)) {
      --tw-space-y-reverse: 0;
      margin-block-start: calc(calc(var(--spacing) * 8) * var(--tw-space-y-reverse));
      margin-block-end: calc(calc(var(--spacing) * 8) * calc(1 - var(--tw-space-y-reverse)));
    }
  }
  .gap-x-2 {
    column-gap: calc(var(--spacing) * 2);
  }
  .space-x-1 {
    :where(& > :not(:last-child)) {
      --tw-space-x-reverse: 0;
      margin-inline-start: calc(calc(var(--spacing) * 1) * var(--tw-space-x-reverse));
      margin-inline-end: calc(calc(var(--spacing) * 1) * calc(1 - var(--tw-space-x-reverse)));
    }
  }
  .space-x-2 {
    :where(& > :not(:last-child)) {
      --tw-space-x-reverse: 0;
      margin-inline-start: calc(calc(var(--spacing) * 2) * var(--tw-space-x-reverse));
      margin-inline-end: calc(calc(var(--spacing) * 2) * calc(1 - var(--tw-space-x-reverse)));
    }
  }
  .space-x-3 {
    :where(& > :not(:last-child)) {
      --tw-space-x-reverse: 0;
      margin-inline-start: calc(calc(var(--spacing) * 3) * var(--tw-space-x-reverse));
      margin-inline-end: calc(calc(var(--spacing) * 3) * calc(1 - var(--tw-space-x-reverse)));
    }
  }
  .space-x-4 {
    :where(& > :not(:last-child)) {
      --tw-space-x-reverse: 0;
      margin-inline-start: calc(calc(var(--spacing) * 4) * var(--tw-space-x-reverse));
      margin-inline-end: calc(calc(var(--spacing) * 4) * calc(1 - var(--tw-space-x-reverse)));
    }
  }
  .divide-y {
    :where(& > :not(:last-child)) {
      --tw-divide-y-reverse: 0;
      border-bottom-style: var(--tw-border-style);
      border-top-style: var(--tw-border-style);
      border-top-width: calc(1px * var(--tw-divide-y-reverse));
      border-bottom-width: calc(1px * calc(1 - var(--tw-divide-y-reverse)));
    }
  }
  .divide-gray-700 {
    :where(& > :not(:last-child)) {
      border-color: var(--color-gray-700);
    }
  }
  .divide-gray-800 {
    :where(& > :not(:last-child)) {
      border-color: var(--color-gray-800);
    }
  }
  .self-stretch {
    align-self: stretch;
  }
  .truncate {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  .overflow-hidden {
    overflow: hidden;
  }
  .overflow-x-auto {
    overflow-x: auto;
  }
  .overflow-x-scroll {
    overflow-x: scroll;
  }
  .rounded-2xl {
    border-radius: var(--radius-2xl);
  }
  .rounded-3xl {
    border-radius: var(--radius-3xl);
  }
  .rounded-4xl {
    border-radius: var(--radius-4xl);
  }
  .rounded-\[0px_16px_0px_0px\] {
    border-radius: 0px 16px 0px 0px;
  }
  .rounded-\[16px_0px_0px_0px\] {
    border-radius: 16px 0px 0px 0px;
  }
  .rounded-\[39\.5px\] {
    border-radius: 39.5px;
  }
  .rounded-\[117px\] {
    border-radius: 117px;
  }
  .rounded-\[119\.5px\] {
    border-radius: 119.5px;
  }
  .rounded-\[158px\] {
    border-radius: 158px;
  }
  .rounded-\[190\.5px\] {
    border-radius: 190.5px;
  }
  .rounded-\[981\.72px\] {
    border-radius: 981.72px;
  }
  .rounded-\[999px\] {
    border-radius: 999px;
  }
  .rounded-full {
    border-radius: calc(infinity * 1px);
  }
  .rounded-lg {
    border-radius: var(--radius-lg);
  }
  .rounded-md {
    border-radius: var(--radius-md);
  }
  .rounded-xl {
    border-radius: var(--radius-xl);
  }
  .rounded-tl-2xl {
    border-top-left-radius: var(--radius-2xl);
  }
  .rounded-tl-sm {
    border-top-left-radius: var(--radius-sm);
  }
  .rounded-tr-2xl {
    border-top-right-radius: var(--radius-2xl);
  }
  .rounded-br-2xl {
    border-bottom-right-radius: var(--radius-2xl);
  }
  .rounded-bl-2xl {
    border-bottom-left-radius: var(--radius-2xl);
  }
  .border {
    border-style: var(--tw-border-style);
    border-width: 1px;
  }
  .border-2 {
    border-style: var(--tw-border-style);
    border-width: 2px;
  }
  .border-4 {
    border-style: var(--tw-border-style);
    border-width: 4px;
  }
  .border-\[0\.2px\] {
    border-style: var(--tw-border-style);
    border-width: 0.2px;
  }
  .border-r {
    border-right-style: var(--tw-border-style);
    border-right-width: 1px;
  }
  .border-l {
    border-left-style: var(--tw-border-style);
    border-left-width: 1px;
  }
  .border-none {
    --tw-border-style: none;
    border-style: none;
  }
  .border-solid {
    --tw-border-style: solid;
    border-style: solid;
  }
  .border-\[\#2D2D2D\] {
    border-color: #2D2D2D;
  }
  .border-\[\#3A3A3A\] {
    border-color: #3A3A3A;
  }
  .border-\[\#D19049\] {
    border-color: #D19049;
  }
  .border-blue-500\/50 {
    border-color: color-mix(in srgb, oklch(62.3% 0.214 259.815) 50%, transparent);
    @supports (color: color-mix(in lab, red, red)) {
      border-color: color-mix(in oklab, var(--color-blue-500) 50%, transparent);
    }
  }
  .border-gray-500 {
    border-color: var(--color-gray-500);
  }
  .border-gray-600 {
    border-color: var(--color-gray-600);
  }
  .border-gray-700 {
    border-color: var(--color-gray-700);
  }
  .border-gray-700\/50 {
    border-color: color-mix(in srgb, oklch(37.3% 0.034 259.733) 50%, transparent);
    @supports (color: color-mix(in lab, red, red)) {
      border-color: color-mix(in oklab, var(--color-gray-700) 50%, transparent);
    }
  }
  .border-gray-800 {
    border-color: var(--color-gray-800);
  }
  .border-neutral-600 {
    border-color: var(--color-neutral-600);
  }
  .border-neutral-700 {
    border-color: var(--color-neutral-700);
  }
  .border-red-500 {
    border-color: var(--color-red-500);
  }
  .border-transparent {
    border-color: transparent;
  }
  .bg-\[\#1C1C1C\] {
    background-color: #1C1C1C;
  }
  .bg-\[\#1C1C1E\] {
    background-color: #1C1C1E;
  }
  .bg-\[\#1D1104\] {
    background-color: #1D1104;
  }
  .bg-\[\#1E1E1E\] {
    background-color: #1E1E1E;
  }
  .bg-\[\#1a1a1a\] {
    background-color: #1a1a1a;
  }
  .bg-\[\#1d1104\] {
    background-color: #1d1104;
  }
  .bg-\[\#2A2A2A\] {
    background-color: #2A2A2A;
  }
  .bg-\[\#2C2C2E\] {
    background-color: #2C2C2E;
  }
  .bg-\[\#2D2D2D\] {
    background-color: #2D2D2D;
  }
  .bg-\[\#3a3a3a\] {
    background-color: #3a3a3a;
  }
  .bg-\[\#4A3933\] {
    background-color: #4A3933;
  }
  .bg-\[\#4F2E0B\] {
    background-color: #4F2E0B;
  }
  .bg-\[\#7CCA8D\] {
    background-color: #7CCA8D;
  }
  .bg-\[\#260D08\] {
    background-color: #260D08;
  }
  .bg-\[\#08210E\] {
    background-color: #08210E;
  }
  .bg-\[\#10431C\] {
    background-color: #10431C;
  }
  .bg-\[\#121212\] {
    background-color: #121212;
  }
  .bg-\[\#171717\] {
    background-color: #171717;
  }
  .bg-\[\#183668\] {
    background-color: #183668;
  }
  .bg-\[\#262626\] {
    background-color: #262626;
  }
  .bg-\[\#281705\] {
    background-color: #281705;
  }
  .bg-\[\#BF4129\] {
    background-color: #BF4129;
  }
  .bg-\[\#C6741B\] {
    background-color: #C6741B;
  }
  .bg-\[\#D4A373\] {
    background-color: #D4A373;
  }
  .bg-\[\#D8E6FD\] {
    background-color: #D8E6FD;
  }
  .bg-\[\#D95E33\] {
    background-color: #D95E33;
  }
  .bg-\[\#E5B3A9\] {
    background-color: #E5B3A9;
  }
  .bg-\[\#bf4129\] {
    background-color: #bf4129;
  }
  .bg-\[\#cc6754\] {
    background-color: #cc6754;
  }
  .bg-\[\#d19049\] {
    background-color: #d19049;
  }
  .bg-background {
    background-color: var(--background);
  }
  .bg-black {
    background-color: var(--color-black);
  }
  .bg-blue-600 {
    background-color: var(--color-blue-600);
  }
  .bg-blue-900\/30 {
    background-color: color-mix(in srgb, oklch(37.9% 0.146 265.522) 30%, transparent);
    @supports (color: color-mix(in lab, red, red)) {
      background-color: color-mix(in oklab, var(--color-blue-900) 30%, transparent);
    }
  }
  .bg-gray-700 {
    background-color: var(--color-gray-700);
  }
  .bg-gray-800 {
    background-color: var(--color-gray-800);
  }
  .bg-green-500 {
    background-color: var(--color-green-500);
  }
  .bg-green-900\/30 {
    background-color: color-mix(in srgb, oklch(39.3% 0.095 152.535) 30%, transparent);
    @supports (color: color-mix(in lab, red, red)) {
      background-color: color-mix(in oklab, var(--color-green-900) 30%, transparent);
    }
  }
  .bg-neutral-600 {
    background-color: var(--color-neutral-600);
  }
  .bg-neutral-700 {
    background-color: var(--color-neutral-700);
  }
  .bg-neutral-800 {
    background-color: var(--color-neutral-800);
  }
  .bg-neutral-900 {
    background-color: var(--color-neutral-900);
  }
  .bg-orange-500 {
    background-color: var(--color-orange-500);
  }
  .bg-red-600 {
    background-color: var(--color-red-600);
  }
  .bg-transparent {
    background-color: transparent;
  }
  .bg-white {
    background-color: var(--color-white);
  }
  .bg-yellow-500 {
    background-color: var(--color-yellow-500);
  }
  .bg-gradient-to-r {
    --tw-gradient-position: to right in oklab;
    background-image: linear-gradient(var(--tw-gradient-stops));
  }
  .bg-\[linear-gradient\(38deg\,rgba\(209\,144\,73\,1\)_36\%\,rgba\(191\,65\,41\,1\)_100\%\)\,linear-gradient\(0deg\,rgba\(38\,38\,38\,1\)_0\%\,rgba\(38\,38\,38\,1\)_100\%\)\] {
    background-image: linear-gradient(38deg,rgba(209,144,73,1) 36%,rgba(191,65,41,1) 100%),linear-gradient(0deg,rgba(38,38,38,1) 0%,rgba(38,38,38,1) 100%);
  }
  .bg-\[linear-gradient\(55deg\,rgba\(209\,144\,73\,1\)_30\%\,rgba\(191\,65\,41\,1\)_100\%\)\] {
    background-image: linear-gradient(55deg,rgba(209,144,73,1) 30%,rgba(191,65,41,1) 100%);
  }
  .from-\[\#BF4129\] {
    --tw-gradient-from: #BF4129;
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .from-\[\#D19049\] {
    --tw-gradient-from: #D19049;
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .to-\[\#BF4129\] {
    --tw-gradient-to: #BF4129;
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .to-\[\#D19049\] {
    --tw-gradient-to: #D19049;
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .bg-clip-text {
    background-clip: text;
  }
  .object-cover {
    object-fit: cover;
  }
  .p-0\.5 {
    padding: calc(var(--spacing) * 0.5);
  }
  .p-1\.5 {
    padding: calc(var(--spacing) * 1.5);
  }
  .p-2 {
    padding: calc(var(--spacing) * 2);
  }
  .p-3 {
    padding: calc(var(--spacing) * 3);
  }
  .p-4 {
    padding: calc(var(--spacing) * 4);
  }
  .p-5 {
    padding: calc(var(--spacing) * 5);
  }
  .p-6 {
    padding: calc(var(--spacing) * 6);
  }
  .p-8 {
    padding: calc(var(--spacing) * 8);
  }
  .p-10 {
    padding: calc(var(--spacing) * 10);
  }
  .px-2 {
    padding-inline: calc(var(--spacing) * 2);
  }
  .px-2\.5 {
    padding-inline: calc(var(--spacing) * 2.5);
  }
  .px-3 {
    padding-inline: calc(var(--spacing) * 3);
  }
  .px-4 {
    padding-inline: calc(var(--spacing) * 4);
  }
  .px-5 {
    padding-inline: calc(var(--spacing) * 5);
  }
  .px-6 {
    padding-inline: calc(var(--spacing) * 6);
  }
  .px-8 {
    padding-inline: calc(var(--spacing) * 8);
  }
  .px-10 {
    padding-inline: calc(var(--spacing) * 10);
  }
  .py-0 {
    padding-block: calc(var(--spacing) * 0);
  }
  .py-1 {
    padding-block: calc(var(--spacing) * 1);
  }
  .py-2 {
    padding-block: calc(var(--spacing) * 2);
  }
  .py-2\.5 {
    padding-block: calc(var(--spacing) * 2.5);
  }
  .py-3 {
    padding-block: calc(var(--spacing) * 3);
  }
  .py-3\.5 {
    padding-block: calc(var(--spacing) * 3.5);
  }
  .py-5 {
    padding-block: calc(var(--spacing) * 5);
  }
  .py-6 {
    padding-block: calc(var(--spacing) * 6);
  }
  .py-8 {
    padding-block: calc(var(--spacing) * 8);
  }
  .py-10 {
    padding-block: calc(var(--spacing) * 10);
  }
  .py-20 {
    padding-block: calc(var(--spacing) * 20);
  }
  .pt-0 {
    padding-top: calc(var(--spacing) * 0);
  }
  .pt-6 {
    padding-top: calc(var(--spacing) * 6);
  }
  .pt-10 {
    padding-top: calc(var(--spacing) * 10);
  }
  .pr-2 {
    padding-right: calc(var(--spacing) * 2);
  }
  .pr-3 {
    padding-right: calc(var(--spacing) * 3);
  }
  .pr-4 {
    padding-right: calc(var(--spacing) * 4);
  }
  .pr-5 {
    padding-right: calc(var(--spacing) * 5);
  }
  .pr-10 {
    padding-right: calc(var(--spacing) * 10);
  }
  .pr-16 {
    padding-right: calc(var(--spacing) * 16);
  }
  .pl-3 {
    padding-left: calc(var(--spacing) * 3);
  }
  .pl-4 {
    padding-left: calc(var(--spacing) * 4);
  }
  .pl-8 {
    padding-left: calc(var(--spacing) * 8);
  }
  .pl-10 {
    padding-left: calc(var(--spacing) * 10);
  }
  .text-center {
    text-align: center;
  }
  .text-left {
    text-align: left;
  }
  .text-right {
    text-align: right;
  }
  .\[font-family\:\'Montserrat\'\,Helvetica\] {
    font-family: 'Montserrat',Helvetica;
  }
  .\[font-family\:\'Montserrat\'\] {
    font-family: 'Montserrat';
  }
  .\[font-family\:\'londrina_outline\'\] {
    font-family: 'londrina outline';
  }
  .font-\[\'Montserrat\'\,Helvetica\] {
    font-family: 'Montserrat',Helvetica;
  }
  .font-\[\'Montserrat\'\] {
    font-family: 'Montserrat';
  }
  .font-mono {
    font-family: var(--font-geist-mono);
  }
  .font-sans {
    font-family: var(--font-geist-sans);
  }
  .text-2xl {
    font-size: var(--text-2xl);
    line-height: var(--tw-leading, var(--text-2xl--line-height));
  }
  .text-3xl {
    font-size: var(--text-3xl);
    line-height: var(--tw-leading, var(--text-3xl--line-height));
  }
  .text-4xl {
    font-size: var(--text-4xl);
    line-height: var(--tw-leading, var(--text-4xl--line-height));
  }
  .text-base {
    font-size: var(--text-base);
    line-height: var(--tw-leading, var(--text-base--line-height));
  }
  .text-lg {
    font-size: var(--text-lg);
    line-height: var(--tw-leading, var(--text-lg--line-height));
  }
  .text-sm {
    font-size: var(--text-sm);
    line-height: var(--tw-leading, var(--text-sm--line-height));
  }
  .text-xl {
    font-size: var(--text-xl);
    line-height: var(--tw-leading, var(--text-xl--line-height));
  }
  .text-xs {
    font-size: var(--text-xs);
    line-height: var(--tw-leading, var(--text-xs--line-height));
  }
  .text-\[9px\] {
    font-size: 9px;
  }
  .text-\[10px\] {
    font-size: 10px;
  }
  .text-\[12px\] {
    font-size: 12px;
  }
  .text-\[16px\] {
    font-size: 16px;
  }
  .text-\[20px\] {
    font-size: 20px;
  }
  .text-\[24px\] {
    font-size: 24px;
  }
  .text-\[28px\] {
    font-size: 28px;
  }
  .text-\[33px\] {
    font-size: 33px;
  }
  .text-\[40px\] {
    font-size: 40px;
  }
  .leading-6 {
    --tw-leading: calc(var(--spacing) * 6);
    line-height: calc(var(--spacing) * 6);
  }
  .leading-\[normal\] {
    --tw-leading: normal;
    line-height: normal;
  }
  .leading-none {
    --tw-leading: 1;
    line-height: 1;
  }
  .leading-normal {
    --tw-leading: var(--leading-normal);
    line-height: var(--leading-normal);
  }
  .leading-relaxed {
    --tw-leading: var(--leading-relaxed);
    line-height: var(--leading-relaxed);
  }
  .font-\[600\] {
    --tw-font-weight: 600;
    font-weight: 600;
  }
  .font-bold {
    --tw-font-weight: var(--font-weight-bold);
    font-weight: var(--font-weight-bold);
  }
  .font-medium {
    --tw-font-weight: var(--font-weight-medium);
    font-weight: var(--font-weight-medium);
  }
  .font-normal {
    --tw-font-weight: var(--font-weight-normal);
    font-weight: var(--font-weight-normal);
  }
  .font-semibold {
    --tw-font-weight: var(--font-weight-semibold);
    font-weight: var(--font-weight-semibold);
  }
  .tracking-\[0\] {
    --tw-tracking: 0;
    letter-spacing: 0;
  }
  .tracking-tight {
    --tw-tracking: var(--tracking-tight);
    letter-spacing: var(--tracking-tight);
  }
  .whitespace-nowrap {
    white-space: nowrap;
  }
  .text-\[\#7CCA8D\] {
    color: #7CCA8D;
  }
  .text-\[\#7cca8d\] {
    color: #7cca8d;
  }
  .text-\[\#171717\] {
    color: #171717;
  }
  .text-\[\#BF4129\] {
    color: #BF4129;
  }
  .text-\[\#C6741B\] {
    color: #C6741B;
  }
  .text-\[\#D4AF37\] {
    color: #D4AF37;
  }
  .text-\[\#F5F5F5\] {
    color: #F5F5F5;
  }
  .text-\[\#bf4129\] {
    color: #bf4129;
  }
  .text-\[\#cc6754\] {
    color: #cc6754;
  }
  .text-\[\#d4d4d4\] {
    color: #d4d4d4;
  }
  .text-\[\#d19049\] {
    color: #d19049;
  }
  .text-\[\#f58f8f\] {
    color: #f58f8f;
  }
  .text-black {
    color: var(--color-black);
  }
  .text-blue-300 {
    color: var(--color-blue-300);
  }
  .text-blue-400 {
    color: var(--color-blue-400);
  }
  .text-gray-100 {
    color: var(--color-gray-100);
  }
  .text-gray-300 {
    color: var(--color-gray-300);
  }
  .text-gray-400 {
    color: var(--color-gray-400);
  }
  .text-gray-500 {
    color: var(--color-gray-500);
  }
  .text-gray-900 {
    color: var(--color-gray-900);
  }
  .text-green-400 {
    color: var(--color-green-400);
  }
  .text-green-500 {
    color: var(--color-green-500);
  }
  .text-neutral-50 {
    color: var(--color-neutral-50);
  }
  .text-neutral-100 {
    color: var(--color-neutral-100);
  }
  .text-neutral-300 {
    color: var(--color-neutral-300);
  }
  .text-neutral-700 {
    color: var(--color-neutral-700);
  }
  .text-orange-400 {
    color: var(--color-orange-400);
  }
  .text-red-300 {
    color: var(--color-red-300);
  }
  .text-red-400 {
    color: var(--color-red-400);
  }
  .text-red-500 {
    color: var(--color-red-500);
  }
  .text-transparent {
    color: transparent;
  }
  .text-white {
    color: var(--color-white);
  }
  .text-white\/80 {
    color: color-mix(in srgb, #fff 80%, transparent);
    @supports (color: color-mix(in lab, red, red)) {
      color: color-mix(in oklab, var(--color-white) 80%, transparent);
    }
  }
  .text-white\/90 {
    color: color-mix(in srgb, #fff 90%, transparent);
    @supports (color: color-mix(in lab, red, red)) {
      color: color-mix(in oklab, var(--color-white) 90%, transparent);
    }
  }
  .text-yellow-300 {
    color: var(--color-yellow-300);
  }
  .text-yellow-400 {
    color: var(--color-yellow-400);
  }
  .text-yellow-500 {
    color: var(--color-yellow-500);
  }
  .lowercase {
    text-transform: lowercase;
  }
  .uppercase {
    text-transform: uppercase;
  }
  .underline-offset-4 {
    text-underline-offset: 4px;
  }
  .antialiased {
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }
  .opacity-0 {
    opacity: 0%;
  }
  .opacity-20 {
    opacity: 20%;
  }
  .opacity-30 {
    opacity: 30%;
  }
  .opacity-100 {
    opacity: 100%;
  }
  .shadow {
    --tw-shadow: 0 1px 3px 0 var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 1px 2px -1px var(--tw-shadow-color, rgb(0 0 0 / 0.1));
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }
  .shadow-lg {
    --tw-shadow: 0 10px 15px -3px var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 4px 6px -4px var(--tw-shadow-color, rgb(0 0 0 / 0.1));
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }
  .shadow-md {
    --tw-shadow: 0 4px 6px -1px var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 2px 4px -2px var(--tw-shadow-color, rgb(0 0 0 / 0.1));
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }
  .shadow-sm {
    --tw-shadow: 0 1px 3px 0 var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 1px 2px -1px var(--tw-shadow-color, rgb(0 0 0 / 0.1));
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }
  .shadow-xl {
    --tw-shadow: 0 20px 25px -5px var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 8px 10px -6px var(--tw-shadow-color, rgb(0 0 0 / 0.1));
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }
  .ring-offset-background {
    --tw-ring-offset-color: var(--background);
  }
  .outline {
    outline-style: var(--tw-outline-style);
    outline-width: 1px;
  }
  .blur {
    --tw-blur: blur(8px);
    filter: var(--tw-blur,) var(--tw-brightness,) var(--tw-contrast,) var(--tw-grayscale,) var(--tw-hue-rotate,) var(--tw-invert,) var(--tw-saturate,) var(--tw-sepia,) var(--tw-drop-shadow,);
  }
  .blur-\[33\.05px\] {
    --tw-blur: blur(33.05px);
    filter: var(--tw-blur,) var(--tw-brightness,) var(--tw-contrast,) var(--tw-grayscale,) var(--tw-hue-rotate,) var(--tw-invert,) var(--tw-saturate,) var(--tw-sepia,) var(--tw-drop-shadow,);
  }
  .blur-\[97\.91px\] {
    --tw-blur: blur(97.91px);
    filter: var(--tw-blur,) var(--tw-brightness,) var(--tw-contrast,) var(--tw-grayscale,) var(--tw-hue-rotate,) var(--tw-invert,) var(--tw-saturate,) var(--tw-sepia,) var(--tw-drop-shadow,);
  }
  .blur-\[100px\] {
    --tw-blur: blur(100px);
    filter: var(--tw-blur,) var(--tw-brightness,) var(--tw-contrast,) var(--tw-grayscale,) var(--tw-hue-rotate,) var(--tw-invert,) var(--tw-saturate,) var(--tw-sepia,) var(--tw-drop-shadow,);
  }
  .blur-\[132\.22px\] {
    --tw-blur: blur(132.22px);
    filter: var(--tw-blur,) var(--tw-brightness,) var(--tw-contrast,) var(--tw-grayscale,) var(--tw-hue-rotate,) var(--tw-invert,) var(--tw-saturate,) var(--tw-sepia,) var(--tw-drop-shadow,);
  }
  .blur-\[159\.41px\] {
    --tw-blur: blur(159.41px);
    filter: var(--tw-blur,) var(--tw-brightness,) var(--tw-contrast,) var(--tw-grayscale,) var(--tw-hue-rotate,) var(--tw-invert,) var(--tw-saturate,) var(--tw-sepia,) var(--tw-drop-shadow,);
  }
  .filter {
    filter: var(--tw-blur,) var(--tw-brightness,) var(--tw-contrast,) var(--tw-grayscale,) var(--tw-hue-rotate,) var(--tw-invert,) var(--tw-saturate,) var(--tw-sepia,) var(--tw-drop-shadow,);
  }
  .transition {
    transition-property: color, background-color, border-color, outline-color, text-decoration-color, fill, stroke, --tw-gradient-from, --tw-gradient-via, --tw-gradient-to, opacity, box-shadow, transform, translate, scale, rotate, filter, -webkit-backdrop-filter, backdrop-filter, display, visibility, content-visibility, overlay, pointer-events;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }
  .transition-all {
    transition-property: all;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }
  .transition-colors {
    transition-property: color, background-color, border-color, outline-color, text-decoration-color, fill, stroke, --tw-gradient-from, --tw-gradient-via, --tw-gradient-to;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }
  .transition-transform {
    transition-property: transform, translate, scale, rotate;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }
  .duration-200 {
    --tw-duration: 200ms;
    transition-duration: 200ms;
  }
  .duration-300 {
    --tw-duration: 300ms;
    transition-duration: 300ms;
  }
  .ease-in-out {
    --tw-ease: var(--ease-in-out);
    transition-timing-function: var(--ease-in-out);
  }
  .\[border-image\:linear-gradient\(38deg\,rgba\(209\,144\,73\,1\)_36\%\,rgba\(191\,65\,41\,1\)_100\%\)_1\] {
    border-image: linear-gradient(38deg,rgba(209,144,73,1) 36%,rgba(191,65,41,1) 100%) 1;
  }
  .\[border-image\:linear-gradient\(249deg\,rgba\(255\,255\,255\,1\)_0\%\,rgba\(255\,255\,255\,0\)_54\%\,rgba\(255\,255\,255\,1\)_100\%\)_1\] {
    border-image: linear-gradient(249deg,rgba(255,255,255,1) 0%,rgba(255,255,255,0) 54%,rgba(255,255,255,1) 100%) 1;
  }
  .group-data-\[state\=open\]\:rotate-180 {
    &:is(:where(.group)[data-state="open"] *) {
      rotate: 180deg;
    }
  }
  .peer-checked\:border-orange-500 {
    &:is(:where(.peer):checked ~ *) {
      border-color: var(--color-orange-500);
    }
  }
  .peer-checked\:opacity-100 {
    &:is(:where(.peer):checked ~ *) {
      opacity: 100%;
    }
  }
  .file\:border-0 {
    &::file-selector-button {
      border-style: var(--tw-border-style);
      border-width: 0px;
    }
  }
  .file\:bg-transparent {
    &::file-selector-button {
      background-color: transparent;
    }
  }
  .file\:text-sm {
    &::file-selector-button {
      font-size: var(--text-sm);
      line-height: var(--tw-leading, var(--text-sm--line-height));
    }
  }
  .file\:font-medium {
    &::file-selector-button {
      --tw-font-weight: var(--font-weight-medium);
      font-weight: var(--font-weight-medium);
    }
  }
  .file\:text-foreground {
    &::file-selector-button {
      color: var(--foreground);
    }
  }
  .hover\:rounded-\[999px\] {
    &:hover {
      @media (hover: hover) {
        border-radius: 999px;
      }
    }
  }
  .hover\:border {
    &:hover {
      @media (hover: hover) {
        border-style: var(--tw-border-style);
        border-width: 1px;
      }
    }
  }
  .hover\:border-neutral-500 {
    &:hover {
      @media (hover: hover) {
        border-color: var(--color-neutral-500);
      }
    }
  }
  .hover\:border-neutral-600 {
    &:hover {
      @media (hover: hover) {
        border-color: var(--color-neutral-600);
      }
    }
  }
  .hover\:bg-\[\#C6741B\] {
    &:hover {
      @media (hover: hover) {
        background-color: #C6741B;
      }
    }
  }
  .hover\:bg-\[\#a83a25\] {
    &:hover {
      @media (hover: hover) {
        background-color: #a83a25;
      }
    }
  }
  .hover\:bg-gray-600 {
    &:hover {
      @media (hover: hover) {
        background-color: var(--color-gray-600);
      }
    }
  }
  .hover\:bg-gray-700 {
    &:hover {
      @media (hover: hover) {
        background-color: var(--color-gray-700);
      }
    }
  }
  .hover\:bg-orange-600 {
    &:hover {
      @media (hover: hover) {
        background-color: var(--color-orange-600);
      }
    }
  }
  .hover\:bg-orange-700 {
    &:hover {
      @media (hover: hover) {
        background-color: var(--color-orange-700);
      }
    }
  }
  .hover\:bg-red-700 {
    &:hover {
      @media (hover: hover) {
        background-color: var(--color-red-700);
      }
    }
  }
  .hover\:bg-yellow-600 {
    &:hover {
      @media (hover: hover) {
        background-color: var(--color-yellow-600);
      }
    }
  }
  .hover\:text-white {
    &:hover {
      @media (hover: hover) {
        color: var(--color-white);
      }
    }
  }
  .hover\:text-yellow-400 {
    &:hover {
      @media (hover: hover) {
        color: var(--color-yellow-400);
      }
    }
  }
  .hover\:underline {
    &:hover {
      @media (hover: hover) {
        text-decoration-line: underline;
      }
    }
  }
  .focus\:border-orange-500 {
    &:focus {
      border-color: var(--color-orange-500);
    }
  }
  .focus\:bg-\[\#404040\] {
    &:focus {
      background-color: #404040;
    }
  }
  .focus\:ring-1 {
    &:focus {
      --tw-ring-shadow: var(--tw-ring-inset,) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
    }
  }
  .focus\:ring-2 {
    &:focus {
      --tw-ring-shadow: var(--tw-ring-inset,) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
    }
  }
  .focus\:ring-gray-500 {
    &:focus {
      --tw-ring-color: var(--color-gray-500);
    }
  }
  .focus\:ring-orange-500 {
    &:focus {
      --tw-ring-color: var(--color-orange-500);
    }
  }
  .focus\:outline-none {
    &:focus {
      --tw-outline-style: none;
      outline-style: none;
    }
  }
  .focus-visible\:ring-1 {
    &:focus-visible {
      --tw-ring-shadow: var(--tw-ring-inset,) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
    }
  }
  .focus-visible\:ring-2 {
    &:focus-visible {
      --tw-ring-shadow: var(--tw-ring-inset,) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
    }
  }
  .focus-visible\:ring-offset-2 {
    &:focus-visible {
      --tw-ring-offset-width: 2px;
      --tw-ring-offset-shadow: var(--tw-ring-inset,) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
    }
  }
  .focus-visible\:outline-none {
    &:focus-visible {
      --tw-outline-style: none;
      outline-style: none;
    }
  }
  .disabled\:pointer-events-none {
    &:disabled {
      pointer-events: none;
    }
  }
  .disabled\:cursor-not-allowed {
    &:disabled {
      cursor: not-allowed;
    }
  }
  .disabled\:opacity-50 {
    &:disabled {
      opacity: 50%;
    }
  }
  .data-\[state\=active\]\:bg-neutral-700 {
    &[data-state="active"] {
      background-color: var(--color-neutral-700);
    }
  }
  .data-\[state\=active\]\:text-foreground {
    &[data-state="active"] {
      color: var(--foreground);
    }
  }
  .data-\[state\=active\]\:shadow {
    &[data-state="active"] {
      --tw-shadow: 0 1px 3px 0 var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 1px 2px -1px var(--tw-shadow-color, rgb(0 0 0 / 0.1));
      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
    }
  }
  .sm\:grid-cols-2 {
    @media (width >= 40rem) {
      grid-template-columns: repeat(2, minmax(0, 1fr));
    }
  }
  .sm\:flex-row {
    @media (width >= 40rem) {
      flex-direction: row;
    }
  }
  .sm\:p-6 {
    @media (width >= 40rem) {
      padding: calc(var(--spacing) * 6);
    }
  }
  .md\:absolute {
    @media (width >= 48rem) {
      position: absolute;
    }
  }
  .md\:top-\[150px\] {
    @media (width >= 48rem) {
      top: 150px;
    }
  }
  .md\:top-\[179px\] {
    @media (width >= 48rem) {
      top: 179px;
    }
  }
  .md\:top-\[187px\] {
    @media (width >= 48rem) {
      top: 187px;
    }
  }
  .md\:top-\[210px\] {
    @media (width >= 48rem) {
      top: 210px;
    }
  }
  .md\:top-\[352px\] {
    @media (width >= 48rem) {
      top: 352px;
    }
  }
  .md\:right-1 {
    @media (width >= 48rem) {
      right: calc(var(--spacing) * 1);
    }
  }
  .md\:right-\[5px\] {
    @media (width >= 48rem) {
      right: 5px;
    }
  }
  .md\:right-\[250px\] {
    @media (width >= 48rem) {
      right: 250px;
    }
  }
  .md\:right-\[256px\] {
    @media (width >= 48rem) {
      right: 256px;
    }
  }
  .md\:bottom-\[0px\] {
    @media (width >= 48rem) {
      bottom: 0px;
    }
  }
  .md\:left-0 {
    @media (width >= 48rem) {
      left: calc(var(--spacing) * 0);
    }
  }
  .md\:left-\[220px\] {
    @media (width >= 48rem) {
      left: 220px;
    }
  }
  .md\:left-\[276px\] {
    @media (width >= 48rem) {
      left: 276px;
    }
  }
  .md\:left-\[481px\] {
    @media (width >= 48rem) {
      left: 481px;
    }
  }
  .md\:mt-0 {
    @media (width >= 48rem) {
      margin-top: calc(var(--spacing) * 0);
    }
  }
  .md\:mb-0 {
    @media (width >= 48rem) {
      margin-bottom: calc(var(--spacing) * 0);
    }
  }
  .md\:block {
    @media (width >= 48rem) {
      display: block;
    }
  }
  .md\:flex {
    @media (width >= 48rem) {
      display: flex;
    }
  }
  .md\:hidden {
    @media (width >= 48rem) {
      display: none;
    }
  }
  .md\:h-\[52px\] {
    @media (width >= 48rem) {
      height: 52px;
    }
  }
  .md\:h-\[75px\] {
    @media (width >= 48rem) {
      height: 75px;
    }
  }
  .md\:h-\[156px\] {
    @media (width >= 48rem) {
      height: 156px;
    }
  }
  .md\:h-\[188px\] {
    @media (width >= 48rem) {
      height: 188px;
    }
  }
  .md\:h-\[296px\] {
    @media (width >= 48rem) {
      height: 296px;
    }
  }
  .md\:h-\[370px\] {
    @media (width >= 48rem) {
      height: 370px;
    }
  }
  .md\:w-1\/2 {
    @media (width >= 48rem) {
      width: calc(1/2 * 100%);
    }
  }
  .md\:w-3xs {
    @media (width >= 48rem) {
      width: var(--container-3xs);
    }
  }
  .md\:w-44 {
    @media (width >= 48rem) {
      width: calc(var(--spacing) * 44);
    }
  }
  .md\:w-64 {
    @media (width >= 48rem) {
      width: calc(var(--spacing) * 64);
    }
  }
  .md\:w-\[78px\] {
    @media (width >= 48rem) {
      width: 78px;
    }
  }
  .md\:w-\[156px\] {
    @media (width >= 48rem) {
      width: 156px;
    }
  }
  .md\:w-\[188px\] {
    @media (width >= 48rem) {
      width: 188px;
    }
  }
  .md\:w-\[296px\] {
    @media (width >= 48rem) {
      width: 296px;
    }
  }
  .md\:w-\[300px\] {
    @media (width >= 48rem) {
      width: 300px;
    }
  }
  .md\:w-\[370px\] {
    @media (width >= 48rem) {
      width: 370px;
    }
  }
  .md\:w-\[var\(--radix-navigation-menu-viewport-width\)\] {
    @media (width >= 48rem) {
      width: var(--radix-navigation-menu-viewport-width);
    }
  }
  .md\:w-auto {
    @media (width >= 48rem) {
      width: auto;
    }
  }
  .md\:flex-grow-0 {
    @media (width >= 48rem) {
      flex-grow: 0;
    }
  }
  .md\:-translate-x-1\/2 {
    @media (width >= 48rem) {
      --tw-translate-x: calc(calc(1/2 * 100%) * -1);
      translate: var(--tw-translate-x) var(--tw-translate-y);
    }
  }
  .md\:grid-cols-2 {
    @media (width >= 48rem) {
      grid-template-columns: repeat(2, minmax(0, 1fr));
    }
  }
  .md\:grid-cols-3 {
    @media (width >= 48rem) {
      grid-template-columns: repeat(3, minmax(0, 1fr));
    }
  }
  .md\:grid-cols-4 {
    @media (width >= 48rem) {
      grid-template-columns: repeat(4, minmax(0, 1fr));
    }
  }
  .md\:grid-cols-none {
    @media (width >= 48rem) {
      grid-template-columns: none;
    }
  }
  .md\:flex-col {
    @media (width >= 48rem) {
      flex-direction: column;
    }
  }
  .md\:flex-row {
    @media (width >= 48rem) {
      flex-direction: row;
    }
  }
  .md\:items-center {
    @media (width >= 48rem) {
      align-items: center;
    }
  }
  .md\:items-start {
    @media (width >= 48rem) {
      align-items: flex-start;
    }
  }
  .md\:justify-center {
    @media (width >= 48rem) {
      justify-content: center;
    }
  }
  .md\:gap-4 {
    @media (width >= 48rem) {
      gap: calc(var(--spacing) * 4);
    }
  }
  .md\:gap-6 {
    @media (width >= 48rem) {
      gap: calc(var(--spacing) * 6);
    }
  }
  .md\:space-x-5 {
    @media (width >= 48rem) {
      :where(& > :not(:last-child)) {
        --tw-space-x-reverse: 0;
        margin-inline-start: calc(calc(var(--spacing) * 5) * var(--tw-space-x-reverse));
        margin-inline-end: calc(calc(var(--spacing) * 5) * calc(1 - var(--tw-space-x-reverse)));
      }
    }
  }
  .md\:space-x-6 {
    @media (width >= 48rem) {
      :where(& > :not(:last-child)) {
        --tw-space-x-reverse: 0;
        margin-inline-start: calc(calc(var(--spacing) * 6) * var(--tw-space-x-reverse));
        margin-inline-end: calc(calc(var(--spacing) * 6) * calc(1 - var(--tw-space-x-reverse)));
      }
    }
  }
  .md\:p-8 {
    @media (width >= 48rem) {
      padding: calc(var(--spacing) * 8);
    }
  }
  .md\:px-0 {
    @media (width >= 48rem) {
      padding-inline: calc(var(--spacing) * 0);
    }
  }
  .md\:px-\[76px\] {
    @media (width >= 48rem) {
      padding-inline: 76px;
    }
  }
  .md\:pl-8 {
    @media (width >= 48rem) {
      padding-left: calc(var(--spacing) * 8);
    }
  }
  .md\:text-left {
    @media (width >= 48rem) {
      text-align: left;
    }
  }
  .md\:text-2xl {
    @media (width >= 48rem) {
      font-size: var(--text-2xl);
      line-height: var(--tw-leading, var(--text-2xl--line-height));
    }
  }
  .md\:text-4xl {
    @media (width >= 48rem) {
      font-size: var(--text-4xl);
      line-height: var(--tw-leading, var(--text-4xl--line-height));
    }
  }
  .md\:text-lg {
    @media (width >= 48rem) {
      font-size: var(--text-lg);
      line-height: var(--tw-leading, var(--text-lg--line-height));
    }
  }
  .md\:text-sm {
    @media (width >= 48rem) {
      font-size: var(--text-sm);
      line-height: var(--tw-leading, var(--text-sm--line-height));
    }
  }
  .md\:text-xl {
    @media (width >= 48rem) {
      font-size: var(--text-xl);
      line-height: var(--tw-leading, var(--text-xl--line-height));
    }
  }
  .md\:text-\[47px\] {
    @media (width >= 48rem) {
      font-size: 47px;
    }
  }
  .lg\:top-\[-55px\] {
    @media (width >= 64rem) {
      top: -55px;
    }
  }
  .lg\:top-\[80px\] {
    @media (width >= 64rem) {
      top: 80px;
    }
  }
  .lg\:top-\[280px\] {
    @media (width >= 64rem) {
      top: 280px;
    }
  }
  .lg\:right-0 {
    @media (width >= 64rem) {
      right: calc(var(--spacing) * 0);
    }
  }
  .lg\:right-4 {
    @media (width >= 64rem) {
      right: calc(var(--spacing) * 4);
    }
  }
  .lg\:right-100 {
    @media (width >= 64rem) {
      right: calc(var(--spacing) * 100);
    }
  }
  .lg\:right-\[-40px\] {
    @media (width >= 64rem) {
      right: -40px;
    }
  }
  .lg\:right-\[-120px\] {
    @media (width >= 64rem) {
      right: -120px;
    }
  }
  .lg\:left-0 {
    @media (width >= 64rem) {
      left: calc(var(--spacing) * 0);
    }
  }
  .lg\:left-1\/2 {
    @media (width >= 64rem) {
      left: calc(1/2 * 100%);
    }
  }
  .lg\:left-\[-120px\] {
    @media (width >= 64rem) {
      left: -120px;
    }
  }
  .lg\:left-\[22px\] {
    @media (width >= 64rem) {
      left: 22px;
    }
  }
  .lg\:left-\[41px\] {
    @media (width >= 64rem) {
      left: 41px;
    }
  }
  .lg\:left-\[571px\] {
    @media (width >= 64rem) {
      left: 571px;
    }
  }
  .lg\:left-\[990px\] {
    @media (width >= 64rem) {
      left: 990px;
    }
  }
  .lg\:m-0 {
    @media (width >= 64rem) {
      margin: calc(var(--spacing) * 0);
    }
  }
  .lg\:mb-0 {
    @media (width >= 64rem) {
      margin-bottom: calc(var(--spacing) * 0);
    }
  }
  .lg\:flex {
    @media (width >= 64rem) {
      display: flex;
    }
  }
  .lg\:grid {
    @media (width >= 64rem) {
      display: grid;
    }
  }
  .lg\:hidden {
    @media (width >= 64rem) {
      display: none;
    }
  }
  .lg\:inline-flex {
    @media (width >= 64rem) {
      display: inline-flex;
    }
  }
  .lg\:h-\[239px\] {
    @media (width >= 64rem) {
      height: 239px;
    }
  }
  .lg\:h-\[316px\] {
    @media (width >= 64rem) {
      height: 316px;
    }
  }
  .lg\:w-50 {
    @media (width >= 64rem) {
      width: calc(var(--spacing) * 50);
    }
  }
  .lg\:w-60 {
    @media (width >= 64rem) {
      width: calc(var(--spacing) * 60);
    }
  }
  .lg\:w-243 {
    @media (width >= 64rem) {
      width: calc(var(--spacing) * 243);
    }
  }
  .lg\:w-250 {
    @media (width >= 64rem) {
      width: calc(var(--spacing) * 250);
    }
  }
  .lg\:w-\[239px\] {
    @media (width >= 64rem) {
      width: 239px;
    }
  }
  .lg\:w-\[300px\] {
    @media (width >= 64rem) {
      width: 300px;
    }
  }
  .lg\:w-\[316px\] {
    @media (width >= 64rem) {
      width: 316px;
    }
  }
  .lg\:w-\[350px\] {
    @media (width >= 64rem) {
      width: 350px;
    }
  }
  .lg\:w-\[480px\] {
    @media (width >= 64rem) {
      width: 480px;
    }
  }
  .lg\:w-\[560px\] {
    @media (width >= 64rem) {
      width: 560px;
    }
  }
  .lg\:w-fit {
    @media (width >= 64rem) {
      width: fit-content;
    }
  }
  .lg\:-translate-x-1\/2 {
    @media (width >= 64rem) {
      --tw-translate-x: calc(calc(1/2 * 100%) * -1);
      translate: var(--tw-translate-x) var(--tw-translate-y);
    }
  }
  .lg\:rotate-0 {
    @media (width >= 64rem) {
      rotate: 0deg;
    }
  }
  .lg\:grid-cols-3 {
    @media (width >= 64rem) {
      grid-template-columns: repeat(3, minmax(0, 1fr));
    }
  }
  .lg\:flex-col {
    @media (width >= 64rem) {
      flex-direction: column;
    }
  }
  .lg\:flex-row {
    @media (width >= 64rem) {
      flex-direction: row;
    }
  }
  .lg\:space-y-0 {
    @media (width >= 64rem) {
      :where(& > :not(:last-child)) {
        --tw-space-y-reverse: 0;
        margin-block-start: calc(calc(var(--spacing) * 0) * var(--tw-space-y-reverse));
        margin-block-end: calc(calc(var(--spacing) * 0) * calc(1 - var(--tw-space-y-reverse)));
      }
    }
  }
  .lg\:overflow-hidden {
    @media (width >= 64rem) {
      overflow: hidden;
    }
  }
  .lg\:rounded-2xl {
    @media (width >= 64rem) {
      border-radius: var(--radius-2xl);
    }
  }
  .lg\:bg-neutral-900 {
    @media (width >= 64rem) {
      background-color: var(--color-neutral-900);
    }
  }
  .lg\:p-8 {
    @media (width >= 64rem) {
      padding: calc(var(--spacing) * 8);
    }
  }
  .lg\:px-0 {
    @media (width >= 64rem) {
      padding-inline: calc(var(--spacing) * 0);
    }
  }
  .lg\:px-5 {
    @media (width >= 64rem) {
      padding-inline: calc(var(--spacing) * 5);
    }
  }
  .lg\:text-start {
    @media (width >= 64rem) {
      text-align: start;
    }
  }
  .lg\:text-2xl {
    @media (width >= 64rem) {
      font-size: var(--text-2xl);
      line-height: var(--tw-leading, var(--text-2xl--line-height));
    }
  }
  .lg\:text-3xl {
    @media (width >= 64rem) {
      font-size: var(--text-3xl);
      line-height: var(--tw-leading, var(--text-3xl--line-height));
    }
  }
  .lg\:text-5xl {
    @media (width >= 64rem) {
      font-size: var(--text-5xl);
      line-height: var(--tw-leading, var(--text-5xl--line-height));
    }
  }
  .lg\:text-lg {
    @media (width >= 64rem) {
      font-size: var(--text-lg);
      line-height: var(--tw-leading, var(--text-lg--line-height));
    }
  }
  .lg\:text-xs {
    @media (width >= 64rem) {
      font-size: var(--text-xs);
      line-height: var(--tw-leading, var(--text-xs--line-height));
    }
  }
  .lg\:text-\[32px\] {
    @media (width >= 64rem) {
      font-size: 32px;
    }
  }
  .lg\:text-\[40px\] {
    @media (width >= 64rem) {
      font-size: 40px;
    }
  }
  .\[\&_svg\]\:pointer-events-none {
    & svg {
      pointer-events: none;
    }
  }
  .\[\&_svg\]\:size-4 {
    & svg {
      width: calc(var(--spacing) * 4);
      height: calc(var(--spacing) * 4);
    }
  }
  .\[\&_svg\]\:shrink-0 {
    & svg {
      flex-shrink: 0;
    }
  }
}
:root {
  --background: #ffffff;
  --foreground: #171717;
}
@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}
body {
  background: var(--background);
  color: var(--foreground);
  font-family: Montserrat, Helvetica, sans-serif;
}
@property --tw-translate-x {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}
@property --tw-translate-y {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}
@property --tw-translate-z {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}
@property --tw-rotate-x {
  syntax: "*";
  inherits: false;
}
@property --tw-rotate-y {
  syntax: "*";
  inherits: false;
}
@property --tw-rotate-z {
  syntax: "*";
  inherits: false;
}
@property --tw-skew-x {
  syntax: "*";
  inherits: false;
}
@property --tw-skew-y {
  syntax: "*";
  inherits: false;
}
@property --tw-space-y-reverse {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}
@property --tw-space-x-reverse {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}
@property --tw-divide-y-reverse {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}
@property --tw-border-style {
  syntax: "*";
  inherits: false;
  initial-value: solid;
}
@property --tw-gradient-position {
  syntax: "*";
  inherits: false;
}
@property --tw-gradient-from {
  syntax: "<color>";
  inherits: false;
  initial-value: #0000;
}
@property --tw-gradient-via {
  syntax: "<color>";
  inherits: false;
  initial-value: #0000;
}
@property --tw-gradient-to {
  syntax: "<color>";
  inherits: false;
  initial-value: #0000;
}
@property --tw-gradient-stops {
  syntax: "*";
  inherits: false;
}
@property --tw-gradient-via-stops {
  syntax: "*";
  inherits: false;
}
@property --tw-gradient-from-position {
  syntax: "<length-percentage>";
  inherits: false;
  initial-value: 0%;
}
@property --tw-gradient-via-position {
  syntax: "<length-percentage>";
  inherits: false;
  initial-value: 50%;
}
@property --tw-gradient-to-position {
  syntax: "<length-percentage>";
  inherits: false;
  initial-value: 100%;
}
@property --tw-leading {
  syntax: "*";
  inherits: false;
}
@property --tw-font-weight {
  syntax: "*";
  inherits: false;
}
@property --tw-tracking {
  syntax: "*";
  inherits: false;
}
@property --tw-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}
@property --tw-shadow-color {
  syntax: "*";
  inherits: false;
}
@property --tw-shadow-alpha {
  syntax: "<percentage>";
  inherits: false;
  initial-value: 100%;
}
@property --tw-inset-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}
@property --tw-inset-shadow-color {
  syntax: "*";
  inherits: false;
}
@property --tw-inset-shadow-alpha {
  syntax: "<percentage>";
  inherits: false;
  initial-value: 100%;
}
@property --tw-ring-color {
  syntax: "*";
  inherits: false;
}
@property --tw-ring-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}
@property --tw-inset-ring-color {
  syntax: "*";
  inherits: false;
}
@property --tw-inset-ring-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}
@property --tw-ring-inset {
  syntax: "*";
  inherits: false;
}
@property --tw-ring-offset-width {
  syntax: "<length>";
  inherits: false;
  initial-value: 0px;
}
@property --tw-ring-offset-color {
  syntax: "*";
  inherits: false;
  initial-value: #fff;
}
@property --tw-ring-offset-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}
@property --tw-outline-style {
  syntax: "*";
  inherits: false;
  initial-value: solid;
}
@property --tw-blur {
  syntax: "*";
  inherits: false;
}
@property --tw-brightness {
  syntax: "*";
  inherits: false;
}
@property --tw-contrast {
  syntax: "*";
  inherits: false;
}
@property --tw-grayscale {
  syntax: "*";
  inherits: false;
}
@property --tw-hue-rotate {
  syntax: "*";
  inherits: false;
}
@property --tw-invert {
  syntax: "*";
  inherits: false;
}
@property --tw-opacity {
  syntax: "*";
  inherits: false;
}
@property --tw-saturate {
  syntax: "*";
  inherits: false;
}
@property --tw-sepia {
  syntax: "*";
  inherits: false;
}
@property --tw-drop-shadow {
  syntax: "*";
  inherits: false;
}
@property --tw-drop-shadow-color {
  syntax: "*";
  inherits: false;
}
@property --tw-drop-shadow-alpha {
  syntax: "<percentage>";
  inherits: false;
  initial-value: 100%;
}
@property --tw-drop-shadow-size {
  syntax: "*";
  inherits: false;
}
@property --tw-duration {
  syntax: "*";
  inherits: false;
}
@property --tw-ease {
  syntax: "*";
  inherits: false;
}
@layer properties {
  @supports ((-webkit-hyphens: none) and (not (margin-trim: inline))) or ((-moz-orient: inline) and (not (color:rgb(from red r g b)))) {
    *, ::before, ::after, ::backdrop {
      --tw-translate-x: 0;
      --tw-translate-y: 0;
      --tw-translate-z: 0;
      --tw-rotate-x: initial;
      --tw-rotate-y: initial;
      --tw-rotate-z: initial;
      --tw-skew-x: initial;
      --tw-skew-y: initial;
      --tw-space-y-reverse: 0;
      --tw-space-x-reverse: 0;
      --tw-divide-y-reverse: 0;
      --tw-border-style: solid;
      --tw-gradient-position: initial;
      --tw-gradient-from: #0000;
      --tw-gradient-via: #0000;
      --tw-gradient-to: #0000;
      --tw-gradient-stops: initial;
      --tw-gradient-via-stops: initial;
      --tw-gradient-from-position: 0%;
      --tw-gradient-via-position: 50%;
      --tw-gradient-to-position: 100%;
      --tw-leading: initial;
      --tw-font-weight: initial;
      --tw-tracking: initial;
      --tw-shadow: 0 0 #0000;
      --tw-shadow-color: initial;
      --tw-shadow-alpha: 100%;
      --tw-inset-shadow: 0 0 #0000;
      --tw-inset-shadow-color: initial;
      --tw-inset-shadow-alpha: 100%;
      --tw-ring-color: initial;
      --tw-ring-shadow: 0 0 #0000;
      --tw-inset-ring-color: initial;
      --tw-inset-ring-shadow: 0 0 #0000;
      --tw-ring-inset: initial;
      --tw-ring-offset-width: 0px;
      --tw-ring-offset-color: #fff;
      --tw-ring-offset-shadow: 0 0 #0000;
      --tw-outline-style: solid;
      --tw-blur: initial;
      --tw-brightness: initial;
      --tw-contrast: initial;
      --tw-grayscale: initial;
      --tw-hue-rotate: initial;
      --tw-invert: initial;
      --tw-opacity: initial;
      --tw-saturate: initial;
      --tw-sepia: initial;
      --tw-drop-shadow: initial;
      --tw-drop-shadow-color: initial;
      --tw-drop-shadow-alpha: 100%;
      --tw-drop-shadow-size: initial;
      --tw-duration: initial;
      --tw-ease: initial;
    }
  }
}

