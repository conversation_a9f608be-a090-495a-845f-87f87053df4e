{"ast": null, "code": "var _jsxFileName = \"C:\\\\My Web Sites\\\\esvc\\\\esvc-react\\\\src\\\\pages\\\\Login.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Login = () => {\n  _s();\n  const [activeTab, setActiveTab] = useState('login');\n  return /*#__PURE__*/_jsxDEV(\"main\", {\n    className: \"w-full bg-neutral-900 min-h-screen\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"container mx-auto px-4 py-20\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-md mx-auto bg-neutral-800 rounded-lg p-8\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex mb-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            className: `flex-1 py-2 px-4 text-center rounded-l-lg ${activeTab === 'login' ? 'bg-[#bf4129] text-white' : 'bg-neutral-700 text-neutral-300'}`,\n            onClick: () => setActiveTab('login'),\n            children: \"Login\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 11,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: `flex-1 py-2 px-4 text-center rounded-r-lg ${activeTab === 'signup' ? 'bg-[#bf4129] text-white' : 'bg-neutral-700 text-neutral-300'}`,\n            onClick: () => setActiveTab('signup'),\n            children: \"Sign Up\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 21,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 10,\n          columnNumber: 11\n        }, this), activeTab === 'login' ? /*#__PURE__*/_jsxDEV(\"form\", {\n          className: \"space-y-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"block text-neutral-300 text-sm font-medium mb-2\",\n              children: \"Email\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 36,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"email\",\n              className: \"w-full px-3 py-2 bg-neutral-700 border border-neutral-600 rounded-md text-white focus:outline-none focus:border-[#bf4129]\",\n              placeholder: \"Enter your email\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 39,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 35,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"block text-neutral-300 text-sm font-medium mb-2\",\n              children: \"Password\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 46,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"password\",\n              className: \"w-full px-3 py-2 bg-neutral-700 border border-neutral-600 rounded-md text-white focus:outline-none focus:border-[#bf4129]\",\n              placeholder: \"Enter your password\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 49,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 45,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"submit\",\n            className: \"w-full bg-[#bf4129] hover:bg-[#a83a25] text-white py-2 px-4 rounded-md font-semibold transition-colors\",\n            children: \"Login\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 55,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 34,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(\"form\", {\n          className: \"space-y-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"block text-neutral-300 text-sm font-medium mb-2\",\n              children: \"Full Name\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 65,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              className: \"w-full px-3 py-2 bg-neutral-700 border border-neutral-600 rounded-md text-white focus:outline-none focus:border-[#bf4129]\",\n              placeholder: \"Enter your full name\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 68,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 64,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"block text-neutral-300 text-sm font-medium mb-2\",\n              children: \"Email\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 75,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"email\",\n              className: \"w-full px-3 py-2 bg-neutral-700 border border-neutral-600 rounded-md text-white focus:outline-none focus:border-[#bf4129]\",\n              placeholder: \"Enter your email\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 78,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 74,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"block text-neutral-300 text-sm font-medium mb-2\",\n              children: \"Password\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 85,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"password\",\n              className: \"w-full px-3 py-2 bg-neutral-700 border border-neutral-600 rounded-md text-white focus:outline-none focus:border-[#bf4129]\",\n              placeholder: \"Create a password\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 88,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 84,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"block text-neutral-300 text-sm font-medium mb-2\",\n              children: \"Confirm Password\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 95,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"password\",\n              className: \"w-full px-3 py-2 bg-neutral-700 border border-neutral-600 rounded-md text-white focus:outline-none focus:border-[#bf4129]\",\n              placeholder: \"Confirm your password\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 98,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 94,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"submit\",\n            className: \"w-full bg-[#bf4129] hover:bg-[#a83a25] text-white py-2 px-4 rounded-md font-semibold transition-colors\",\n            children: \"Sign Up\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 104,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 63,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 9,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 8,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 7,\n    columnNumber: 5\n  }, this);\n};\n_s(Login, \"Z4iOefLcE+XbawdTqi7V2gbRJHw=\");\n_c = Login;\nexport default Login;\nvar _c;\n$RefreshReg$(_c, \"Login\");", "map": {"version": 3, "names": ["React", "useState", "jsxDEV", "_jsxDEV", "<PERSON><PERSON>", "_s", "activeTab", "setActiveTab", "className", "children", "onClick", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "type", "placeholder", "_c", "$RefreshReg$"], "sources": ["C:/My Web Sites/esvc/esvc-react/src/pages/Login.tsx"], "sourcesContent": ["import React, { useState } from 'react';\n\nconst Login: React.FC = () => {\n  const [activeTab, setActiveTab] = useState<'login' | 'signup'>('login');\n\n  return (\n    <main className=\"w-full bg-neutral-900 min-h-screen\">\n      <div className=\"container mx-auto px-4 py-20\">\n        <div className=\"max-w-md mx-auto bg-neutral-800 rounded-lg p-8\">\n          <div className=\"flex mb-6\">\n            <button\n              className={`flex-1 py-2 px-4 text-center rounded-l-lg ${\n                activeTab === 'login'\n                  ? 'bg-[#bf4129] text-white'\n                  : 'bg-neutral-700 text-neutral-300'\n              }`}\n              onClick={() => setActiveTab('login')}\n            >\n              Login\n            </button>\n            <button\n              className={`flex-1 py-2 px-4 text-center rounded-r-lg ${\n                activeTab === 'signup'\n                  ? 'bg-[#bf4129] text-white'\n                  : 'bg-neutral-700 text-neutral-300'\n              }`}\n              onClick={() => setActiveTab('signup')}\n            >\n              Sign Up\n            </button>\n          </div>\n\n          {activeTab === 'login' ? (\n            <form className=\"space-y-4\">\n              <div>\n                <label className=\"block text-neutral-300 text-sm font-medium mb-2\">\n                  Email\n                </label>\n                <input\n                  type=\"email\"\n                  className=\"w-full px-3 py-2 bg-neutral-700 border border-neutral-600 rounded-md text-white focus:outline-none focus:border-[#bf4129]\"\n                  placeholder=\"Enter your email\"\n                />\n              </div>\n              <div>\n                <label className=\"block text-neutral-300 text-sm font-medium mb-2\">\n                  Password\n                </label>\n                <input\n                  type=\"password\"\n                  className=\"w-full px-3 py-2 bg-neutral-700 border border-neutral-600 rounded-md text-white focus:outline-none focus:border-[#bf4129]\"\n                  placeholder=\"Enter your password\"\n                />\n              </div>\n              <button\n                type=\"submit\"\n                className=\"w-full bg-[#bf4129] hover:bg-[#a83a25] text-white py-2 px-4 rounded-md font-semibold transition-colors\"\n              >\n                Login\n              </button>\n            </form>\n          ) : (\n            <form className=\"space-y-4\">\n              <div>\n                <label className=\"block text-neutral-300 text-sm font-medium mb-2\">\n                  Full Name\n                </label>\n                <input\n                  type=\"text\"\n                  className=\"w-full px-3 py-2 bg-neutral-700 border border-neutral-600 rounded-md text-white focus:outline-none focus:border-[#bf4129]\"\n                  placeholder=\"Enter your full name\"\n                />\n              </div>\n              <div>\n                <label className=\"block text-neutral-300 text-sm font-medium mb-2\">\n                  Email\n                </label>\n                <input\n                  type=\"email\"\n                  className=\"w-full px-3 py-2 bg-neutral-700 border border-neutral-600 rounded-md text-white focus:outline-none focus:border-[#bf4129]\"\n                  placeholder=\"Enter your email\"\n                />\n              </div>\n              <div>\n                <label className=\"block text-neutral-300 text-sm font-medium mb-2\">\n                  Password\n                </label>\n                <input\n                  type=\"password\"\n                  className=\"w-full px-3 py-2 bg-neutral-700 border border-neutral-600 rounded-md text-white focus:outline-none focus:border-[#bf4129]\"\n                  placeholder=\"Create a password\"\n                />\n              </div>\n              <div>\n                <label className=\"block text-neutral-300 text-sm font-medium mb-2\">\n                  Confirm Password\n                </label>\n                <input\n                  type=\"password\"\n                  className=\"w-full px-3 py-2 bg-neutral-700 border border-neutral-600 rounded-md text-white focus:outline-none focus:border-[#bf4129]\"\n                  placeholder=\"Confirm your password\"\n                />\n              </div>\n              <button\n                type=\"submit\"\n                className=\"w-full bg-[#bf4129] hover:bg-[#a83a25] text-white py-2 px-4 rounded-md font-semibold transition-colors\"\n              >\n                Sign Up\n              </button>\n            </form>\n          )}\n        </div>\n      </div>\n    </main>\n  );\n};\n\nexport default Login;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExC,MAAMC,KAAe,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC5B,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGN,QAAQ,CAAqB,OAAO,CAAC;EAEvE,oBACEE,OAAA;IAAMK,SAAS,EAAC,oCAAoC;IAAAC,QAAA,eAClDN,OAAA;MAAKK,SAAS,EAAC,8BAA8B;MAAAC,QAAA,eAC3CN,OAAA;QAAKK,SAAS,EAAC,gDAAgD;QAAAC,QAAA,gBAC7DN,OAAA;UAAKK,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACxBN,OAAA;YACEK,SAAS,EAAE,6CACTF,SAAS,KAAK,OAAO,GACjB,yBAAyB,GACzB,iCAAiC,EACpC;YACHI,OAAO,EAAEA,CAAA,KAAMH,YAAY,CAAC,OAAO,CAAE;YAAAE,QAAA,EACtC;UAED;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACTX,OAAA;YACEK,SAAS,EAAE,6CACTF,SAAS,KAAK,QAAQ,GAClB,yBAAyB,GACzB,iCAAiC,EACpC;YACHI,OAAO,EAAEA,CAAA,KAAMH,YAAY,CAAC,QAAQ,CAAE;YAAAE,QAAA,EACvC;UAED;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,EAELR,SAAS,KAAK,OAAO,gBACpBH,OAAA;UAAMK,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACzBN,OAAA;YAAAM,QAAA,gBACEN,OAAA;cAAOK,SAAS,EAAC,iDAAiD;cAAAC,QAAA,EAAC;YAEnE;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRX,OAAA;cACEY,IAAI,EAAC,OAAO;cACZP,SAAS,EAAC,2HAA2H;cACrIQ,WAAW,EAAC;YAAkB;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/B,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eACNX,OAAA;YAAAM,QAAA,gBACEN,OAAA;cAAOK,SAAS,EAAC,iDAAiD;cAAAC,QAAA,EAAC;YAEnE;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRX,OAAA;cACEY,IAAI,EAAC,UAAU;cACfP,SAAS,EAAC,2HAA2H;cACrIQ,WAAW,EAAC;YAAqB;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eACNX,OAAA;YACEY,IAAI,EAAC,QAAQ;YACbP,SAAS,EAAC,wGAAwG;YAAAC,QAAA,EACnH;UAED;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,gBAEPX,OAAA;UAAMK,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACzBN,OAAA;YAAAM,QAAA,gBACEN,OAAA;cAAOK,SAAS,EAAC,iDAAiD;cAAAC,QAAA,EAAC;YAEnE;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRX,OAAA;cACEY,IAAI,EAAC,MAAM;cACXP,SAAS,EAAC,2HAA2H;cACrIQ,WAAW,EAAC;YAAsB;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eACNX,OAAA;YAAAM,QAAA,gBACEN,OAAA;cAAOK,SAAS,EAAC,iDAAiD;cAAAC,QAAA,EAAC;YAEnE;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRX,OAAA;cACEY,IAAI,EAAC,OAAO;cACZP,SAAS,EAAC,2HAA2H;cACrIQ,WAAW,EAAC;YAAkB;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/B,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eACNX,OAAA;YAAAM,QAAA,gBACEN,OAAA;cAAOK,SAAS,EAAC,iDAAiD;cAAAC,QAAA,EAAC;YAEnE;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRX,OAAA;cACEY,IAAI,EAAC,UAAU;cACfP,SAAS,EAAC,2HAA2H;cACrIQ,WAAW,EAAC;YAAmB;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eACNX,OAAA;YAAAM,QAAA,gBACEN,OAAA;cAAOK,SAAS,EAAC,iDAAiD;cAAAC,QAAA,EAAC;YAEnE;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRX,OAAA;cACEY,IAAI,EAAC,UAAU;cACfP,SAAS,EAAC,2HAA2H;cACrIQ,WAAW,EAAC;YAAuB;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eACNX,OAAA;YACEY,IAAI,EAAC,QAAQ;YACbP,SAAS,EAAC,wGAAwG;YAAAC,QAAA,EACnH;UAED;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CACP;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACF,CAAC;AAEX,CAAC;AAACT,EAAA,CAjHID,KAAe;AAAAa,EAAA,GAAfb,KAAe;AAmHrB,eAAeA,KAAK;AAAC,IAAAa,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}