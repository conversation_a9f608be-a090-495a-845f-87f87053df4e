{"ast": null, "code": "var _jsxFileName = \"C:\\\\My Web Sites\\\\esvc\\\\esvc-react\\\\src\\\\pages\\\\TradeChallenge.tsx\";\nimport React from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst TradeChallenge = () => {\n  return /*#__PURE__*/_jsxDEV(\"main\", {\n    className: \"w-full bg-neutral-900 min-h-screen\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"container mx-auto px-4 py-20\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"text-4xl font-bold text-white mb-8\",\n          children: \"Trade Challenge\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 8,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-neutral-300 text-lg mb-8\",\n          children: \"Join our exclusive trading challenge and compete with other traders for substantial rewards.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 9,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid md:grid-cols-2 gap-8 max-w-4xl mx-auto\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-neutral-800 rounded-lg p-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-xl font-semibold text-white mb-4\",\n              children: \"Challenge Overview\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 15,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-neutral-300 mb-4\",\n              children: \"Participate in our monthly trading challenge where skilled traders compete for prizes and recognition in the ESVC community.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 16,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n              className: \"text-neutral-300 text-left space-y-2\",\n              children: [/*#__PURE__*/_jsxDEV(\"li\", {\n                children: \"\\u2022 Monthly competitions\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 21,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: \"\\u2022 Substantial prize pools\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 22,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: \"\\u2022 Professional trading tools\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 23,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: \"\\u2022 Community leaderboards\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 24,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 20,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 14,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-neutral-800 rounded-lg p-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-xl font-semibold text-white mb-4\",\n              children: \"How to Participate\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 29,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-neutral-300 mb-4\",\n              children: \"Getting started with the trade challenge is simple and straightforward.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 30,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"ol\", {\n              className: \"text-neutral-300 text-left space-y-2\",\n              children: [/*#__PURE__*/_jsxDEV(\"li\", {\n                children: \"1. Register for the challenge\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 34,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: \"2. Complete your profile\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 35,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: \"3. Start trading with virtual funds\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 36,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: \"4. Climb the leaderboard\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 37,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 33,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 28,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 13,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mt-12\",\n          children: /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"bg-[#bf4129] hover:bg-[#a83a25] text-white py-3 px-8 rounded-full font-semibold text-lg transition-colors\",\n            children: \"Join Challenge\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 43,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 42,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 7,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 6,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 5,\n    columnNumber: 5\n  }, this);\n};\n_c = TradeChallenge;\nexport default TradeChallenge;\nvar _c;\n$RefreshReg$(_c, \"TradeChallenge\");", "map": {"version": 3, "names": ["React", "jsxDEV", "_jsxDEV", "TradeChallenge", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/My Web Sites/esvc/esvc-react/src/pages/TradeChallenge.tsx"], "sourcesContent": ["import React from 'react';\n\nconst TradeChallenge: React.FC = () => {\n  return (\n    <main className=\"w-full bg-neutral-900 min-h-screen\">\n      <div className=\"container mx-auto px-4 py-20\">\n        <div className=\"text-center\">\n          <h1 className=\"text-4xl font-bold text-white mb-8\">Trade Challenge</h1>\n          <p className=\"text-neutral-300 text-lg mb-8\">\n            Join our exclusive trading challenge and compete with other traders for substantial rewards.\n          </p>\n          \n          <div className=\"grid md:grid-cols-2 gap-8 max-w-4xl mx-auto\">\n            <div className=\"bg-neutral-800 rounded-lg p-6\">\n              <h3 className=\"text-xl font-semibold text-white mb-4\">Challenge Overview</h3>\n              <p className=\"text-neutral-300 mb-4\">\n                Participate in our monthly trading challenge where skilled traders compete for prizes \n                and recognition in the ESVC community.\n              </p>\n              <ul className=\"text-neutral-300 text-left space-y-2\">\n                <li>• Monthly competitions</li>\n                <li>• Substantial prize pools</li>\n                <li>• Professional trading tools</li>\n                <li>• Community leaderboards</li>\n              </ul>\n            </div>\n            \n            <div className=\"bg-neutral-800 rounded-lg p-6\">\n              <h3 className=\"text-xl font-semibold text-white mb-4\">How to Participate</h3>\n              <p className=\"text-neutral-300 mb-4\">\n                Getting started with the trade challenge is simple and straightforward.\n              </p>\n              <ol className=\"text-neutral-300 text-left space-y-2\">\n                <li>1. Register for the challenge</li>\n                <li>2. Complete your profile</li>\n                <li>3. Start trading with virtual funds</li>\n                <li>4. Climb the leaderboard</li>\n              </ol>\n            </div>\n          </div>\n          \n          <div className=\"mt-12\">\n            <button className=\"bg-[#bf4129] hover:bg-[#a83a25] text-white py-3 px-8 rounded-full font-semibold text-lg transition-colors\">\n              Join Challenge\n            </button>\n          </div>\n        </div>\n      </div>\n    </main>\n  );\n};\n\nexport default TradeChallenge;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1B,MAAMC,cAAwB,GAAGA,CAAA,KAAM;EACrC,oBACED,OAAA;IAAME,SAAS,EAAC,oCAAoC;IAAAC,QAAA,eAClDH,OAAA;MAAKE,SAAS,EAAC,8BAA8B;MAAAC,QAAA,eAC3CH,OAAA;QAAKE,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1BH,OAAA;UAAIE,SAAS,EAAC,oCAAoC;UAAAC,QAAA,EAAC;QAAe;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACvEP,OAAA;UAAGE,SAAS,EAAC,+BAA+B;UAAAC,QAAA,EAAC;QAE7C;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eAEJP,OAAA;UAAKE,SAAS,EAAC,6CAA6C;UAAAC,QAAA,gBAC1DH,OAAA;YAAKE,SAAS,EAAC,+BAA+B;YAAAC,QAAA,gBAC5CH,OAAA;cAAIE,SAAS,EAAC,uCAAuC;cAAAC,QAAA,EAAC;YAAkB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC7EP,OAAA;cAAGE,SAAS,EAAC,uBAAuB;cAAAC,QAAA,EAAC;YAGrC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACJP,OAAA;cAAIE,SAAS,EAAC,sCAAsC;cAAAC,QAAA,gBAClDH,OAAA;gBAAAG,QAAA,EAAI;cAAsB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC/BP,OAAA;gBAAAG,QAAA,EAAI;cAAyB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAClCP,OAAA;gBAAAG,QAAA,EAAI;cAA4B;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACrCP,OAAA;gBAAAG,QAAA,EAAI;cAAwB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/B,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eAENP,OAAA;YAAKE,SAAS,EAAC,+BAA+B;YAAAC,QAAA,gBAC5CH,OAAA;cAAIE,SAAS,EAAC,uCAAuC;cAAAC,QAAA,EAAC;YAAkB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC7EP,OAAA;cAAGE,SAAS,EAAC,uBAAuB;cAAAC,QAAA,EAAC;YAErC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACJP,OAAA;cAAIE,SAAS,EAAC,sCAAsC;cAAAC,QAAA,gBAClDH,OAAA;gBAAAG,QAAA,EAAI;cAA6B;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACtCP,OAAA;gBAAAG,QAAA,EAAI;cAAwB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACjCP,OAAA;gBAAAG,QAAA,EAAI;cAAmC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC5CP,OAAA;gBAAAG,QAAA,EAAI;cAAwB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/B,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENP,OAAA;UAAKE,SAAS,EAAC,OAAO;UAAAC,QAAA,eACpBH,OAAA;YAAQE,SAAS,EAAC,2GAA2G;YAAAC,QAAA,EAAC;UAE9H;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACF,CAAC;AAEX,CAAC;AAACC,EAAA,GAhDIP,cAAwB;AAkD9B,eAAeA,cAAc;AAAC,IAAAO,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}