{"ast": null, "code": "'use strict';\n\nif (process.env.NODE_ENV === 'production') {\n  module.exports = require('./cjs/react.production.js');\n} else {\n  module.exports = require('./cjs/react.development.js');\n}", "map": {"version": 3, "names": ["process", "env", "NODE_ENV", "module", "exports", "require"], "sources": ["C:/My Web Sites/esvc/esvc-react/node_modules/react/index.js"], "sourcesContent": ["'use strict';\n\nif (process.env.NODE_ENV === 'production') {\n  module.exports = require('./cjs/react.production.js');\n} else {\n  module.exports = require('./cjs/react.development.js');\n}\n"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzCC,MAAM,CAACC,OAAO,GAAGC,OAAO,CAAC,2BAA2B,CAAC;AACvD,CAAC,MAAM;EACLF,MAAM,CAACC,OAAO,GAAGC,OAAO,CAAC,4BAA4B,CAAC;AACxD", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}