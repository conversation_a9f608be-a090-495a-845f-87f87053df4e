{"ast": null, "code": "\"use strict\";\n\nmodule.exports = function (item) {\n  var content = item[1];\n  var cssMapping = item[3];\n  if (!cssMapping) {\n    return content;\n  }\n  if (typeof btoa === \"function\") {\n    var base64 = btoa(unescape(encodeURIComponent(JSON.stringify(cssMapping))));\n    var data = \"sourceMappingURL=data:application/json;charset=utf-8;base64,\".concat(base64);\n    var sourceMapping = \"/*# \".concat(data, \" */\");\n    return [content].concat([sourceMapping]).join(\"\\n\");\n  }\n  return [content].join(\"\\n\");\n};", "map": {"version": 3, "names": ["module", "exports", "item", "content", "cssMapping", "btoa", "base64", "unescape", "encodeURIComponent", "JSON", "stringify", "data", "concat", "sourceMapping", "join"], "sources": ["C:/My Web Sites/esvc/esvc-react/node_modules/css-loader/dist/runtime/sourceMaps.js"], "sourcesContent": ["\"use strict\";\n\nmodule.exports = function (item) {\n  var content = item[1];\n  var cssMapping = item[3];\n  if (!cssMapping) {\n    return content;\n  }\n  if (typeof btoa === \"function\") {\n    var base64 = btoa(unescape(encodeURIComponent(JSON.stringify(cssMapping))));\n    var data = \"sourceMappingURL=data:application/json;charset=utf-8;base64,\".concat(base64);\n    var sourceMapping = \"/*# \".concat(data, \" */\");\n    return [content].concat([sourceMapping]).join(\"\\n\");\n  }\n  return [content].join(\"\\n\");\n};"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,OAAO,GAAG,UAAUC,IAAI,EAAE;EAC/B,IAAIC,OAAO,GAAGD,IAAI,CAAC,CAAC,CAAC;EACrB,IAAIE,UAAU,GAAGF,IAAI,CAAC,CAAC,CAAC;EACxB,IAAI,CAACE,UAAU,EAAE;IACf,OAAOD,OAAO;EAChB;EACA,IAAI,OAAOE,IAAI,KAAK,UAAU,EAAE;IAC9B,IAAIC,MAAM,GAAGD,IAAI,CAACE,QAAQ,CAACC,kBAAkB,CAACC,IAAI,CAACC,SAAS,CAACN,UAAU,CAAC,CAAC,CAAC,CAAC;IAC3E,IAAIO,IAAI,GAAG,8DAA8D,CAACC,MAAM,CAACN,MAAM,CAAC;IACxF,IAAIO,aAAa,GAAG,MAAM,CAACD,MAAM,CAACD,IAAI,EAAE,KAAK,CAAC;IAC9C,OAAO,CAACR,OAAO,CAAC,CAACS,MAAM,CAAC,CAACC,aAAa,CAAC,CAAC,CAACC,IAAI,CAAC,IAAI,CAAC;EACrD;EACA,OAAO,CAACX,OAAO,CAAC,CAACW,IAAI,CAAC,IAAI,CAAC;AAC7B,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}