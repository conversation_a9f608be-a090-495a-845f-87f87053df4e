{"ast": null, "code": "var _jsxFileName = \"C:\\\\My Web Sites\\\\esvc\\\\esvc-react\\\\src\\\\pages\\\\Index.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Index = () => {\n  _s();\n  const [openAccordion, setOpenAccordion] = useState(null);\n  const toggleAccordion = index => {\n    setOpenAccordion(openAccordion === index ? null : index);\n  };\n  const faqData = [{\n    question: \"What is ESVC?\",\n    answer: \"ESVC is a decentralized staking protocol that allows users to earn rewards by staking their ESVC tokens. It leverages the Solana blockchain for high-speed and low-cost transactions.\"\n  }, {\n    question: \"How do I stake ESVC?\",\n    answer: \"Simply click \\\"Start Staking Now\\\", create an account, choose how much you want to stake (in USD), and deposit using either Solana (SOL) or USDC. Your ESVC tokens will be automatically purchased and staked for a 6 or 12-month period.\"\n  }, {\n    question: \"What's the minimum amount I can stake?\",\n    answer: \"The minimum staking amount is 50 USD to ensure efficient transaction processing and reward distribution within the protocol.\"\n  }, {\n    question: \"How much ROI can I earn?\",\n    answer: \"Expected ROI varies based on staking period and current network conditions. Typically, annual returns range from 8% to 15%. Detailed projections are available after you log in.\"\n  }, {\n    question: \"When can I withdraw my ROI?\",\n    answer: \"Your earned ROI can be withdrawn at the end of your chosen staking period (6 or 12 months). Partial withdrawals during the staking period are not allowed to maintain network stability.\"\n  }];\n  return /*#__PURE__*/_jsxDEV(\"main\", {\n    className: \"w-full bg-neutral-900\",\n    \"data-model-id\": \"1:21\",\n    children: [/*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"relative w-full\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute w-[239px] h-[239px] top-0 lg:right-0 bg-[#d19049] rounded-[119.5px] blur-[100px] opacity-30\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 37,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute w-[239px] h-[239px] top-[232px] lg:left-[22px] bg-[#cc6754] rounded-[119.5px] blur-[100px] opacity-30\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 38,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n        className: \"flex flex-col items-center gap-8 w-full max-w-4xl mx-auto mb-20 py-5 px-5 lg:px-0\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex flex-col items-center gap-4 w-full text-center relative\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"inline-flex items-center justify-center gap-2.5 px-4 rounded-full\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 42,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"border border-neutral-700 rounded-full p-3 bg-gradient-to-r from-[#BF4129] to-[#D19049] bg-clip-text text-transparent\",\n            children: /*#__PURE__*/_jsxDEV(\"h3\", {\n              children: \"Earn 20% annual returns\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 44,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 43,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n            className: \"font-['Montserrat'] font-bold md:text-[47px] text-[33px]\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-[#cc6754]\",\n              children: \"Grow\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 47,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-neutral-100\",\n              children: \" Your Wealth. \"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 48,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-[#d19049]\",\n              children: \"Get\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 49,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-neutral-100\",\n              children: [\" Funded. \", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 50,\n                columnNumber: 59\n              }, this), \" Stake and Earn .\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 50,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 46,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"max-w-[627px] font-['Montserrat',Helvetica] font-normal text-neutral-300 text-base leading-6\",\n            children: [\"Stake your ESVC tokens and earn daily ROI. \", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 53,\n              columnNumber: 58\n            }, this), \"Unlock exclusive opportunities to pitch your startup ideas for funding/get funded trade capital. \", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 54,\n              columnNumber: 112\n            }, this), \"Earn from our Bitcoin/Crypto Treasury\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 52,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"img\", {\n            className: \"top-[220px] w-[200px] h-[13px] absolute md:top-[210px] md:w-[300px] right-1 md:right-1 md:-translate-x-1/2\",\n            alt: \"Decorative line\",\n            src: \"https://c.animaapp.com/mc62dpc6QTKBF1/img/vector-1.svg\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 57,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"img\", {\n            className: \"absolute w-44 h-[50px] top-[179px] left-[20px] md:w-44 md:h-[75px] md:top-[150px] md:left-[220px]\",\n            alt: \"Vector graphic circle\",\n            src: \"https://c.animaapp.com/mc62dpc6QTKBF1/img/vector.svg\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 62,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 41,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"whitespace-nowrap text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0 text-primary-foreground shadow h-14 bg-[#bf4129] hover:bg-[#a83a25] rounded-full px-4 py-2.5 flex items-center justify-center gap-3\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"font-['Montserrat',Helvetica] font-semibold text-neutral-50 text-lg\",\n            children: \"Start Staking Now\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 69,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"img\", {\n            className: \"w-6 h-6\",\n            alt: \"Rocket launch\",\n            src: \"https://c.animaapp.com/mc62dpc6QTKBF1/img/rocket-launch-24dp-00000-fill0-wght300-grad0-opsz24-1.svg\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 70,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 68,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 40,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"img\", {\n        className: \"absolute w-[106px] top-[401px] right-[-10px] md:w-[296px] md:h-[296px] md:top-[187px] md:right-[5px] object-cover\",\n        alt: \"Decorative coin image gold\",\n        src: \"https://c.animaapp.com/mc62dpc6QTKBF1/img/whatsapp-image-2025-06-13-at-6-11-51-pm--2--1.png\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 74,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"img\", {\n        className: \"w-[106px] h-[106px] top-[401px] left-0 md:w-[370px] md:h-[370px] md:top-[179px] md:left-0 absolute object-cover\",\n        alt: \"Decorative coin image red big\",\n        src: \"https://c.animaapp.com/mc62dpc6QTKBF1/img/whatsapp-image-2025-06-13-at-6-11-51-pm--1--2.png\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 75,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"img\", {\n        className: \"w-[78px] h-[78px] top-[492px] right-[20px] md:w-[156px] md:h-[156px] md:top-[352px] md:right-[256px] absolute object-cover\",\n        alt: \"Decorative coin image red small right\",\n        src: \"https://c.animaapp.com/mc62dpc6QTKBF1/img/whatsapp-image-2025-06-13-at-6-11-51-pm--1--2.png\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 76,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"img\", {\n        className: \"absolute w-[94px] h-[94px] top-[492px] left-[10px] md:w-[188px] md:h-[188px] md:top-[352px] md:left-[276px] object-cover\",\n        alt: \"Decorative coin image\",\n        src: \"https://c.animaapp.com/mc62dpc6QTKBF1/img/whatsapp-image-2025-06-13-at-6-11-51-pm-1.png\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 77,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 36,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"w-full p-10 bg-neutral-800\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container max-w-[1200px] mx-auto relative\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"absolute w-[239px] h-[239px] top-[73px] lg:left-[41px] md:left-[481px] rounded-[119.5px] blur-[100px] bg-[linear-gradient(55deg,rgba(209,144,73,1)_30%,rgba(191,65,41,1)_100%)] opacity-20\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 83,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex flex-col items-center gap-4 mb-10 relative\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-[40px] font-semibold text-neutral-100 text-center [font-family:'Montserrat',Helvetica]\",\n            children: \"Treasury Dashboard\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 86,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"max-w-[670px] font-normal text-neutral-300 text-base text-center leading-6 [font-family:'Montserrat',Helvetica]\",\n            children: \"Live insights into how funds are held, ROI is distributed, and startups are supported. Empowering you to stake with trust.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 87,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"img\", {\n            className: \"hidden absolute w-[154px] h-[18px] top-[43px] left-[407px]\",\n            alt: \"Vector\",\n            src: \"https://c.animaapp.com/mc62dpc6QTKBF1/img/vector-2.svg\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 90,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 85,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"md:flex md:flex-col w-full gap-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"md:flex items-center gap-6 w-full\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"rounded-xl border text-card-foreground shadow flex-1 bg-transparent border-neutral-700 mb-4\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex flex-col items-start gap-4 p-4\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center gap-2 w-full mt-[-1.00px]\",\n                  children: /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"font-medium text-neutral-300 text-sm [font-family:'Montserrat',Helvetica]\",\n                    children: \"Total Capital Invested\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 98,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 97,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"self-stretch font-semibold text-neutral-100 text-[28px] [font-family:'Montserrat',Helvetica]\",\n                  children: \"$266,500\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 100,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center gap-2\",\n                  children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                    xmlns: \"http://www.w3.org/2000/svg\",\n                    width: \"24\",\n                    height: \"24\",\n                    viewBox: \"0 0 24 24\",\n                    fill: \"none\",\n                    stroke: \"currentColor\",\n                    strokeWidth: \"2\",\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    className: \"lucide lucide-trending-up w-4 h-4 text-[#7cca8d]\",\n                    \"aria-hidden\": \"true\",\n                    children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                      d: \"M16 7h6v6\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 103,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                      d: \"m22 7-8.5 8.5-5-5L2 17\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 104,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 102,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"font-normal text-xs [font-family:'Montserrat',Helvetica] text-[#7cca8d]\",\n                    children: \"+ 4.8% Today\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 106,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 101,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 96,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 95,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"rounded-xl border text-card-foreground shadow flex-1 bg-transparent border-neutral-700 mb-4\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex flex-col items-start gap-4 p-4\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center gap-2 w-full mt-[-1.00px]\",\n                  children: /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"font-medium text-neutral-300 text-sm [font-family:'Montserrat',Helvetica]\",\n                    children: \"Total Profit Generated\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 114,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 113,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"self-stretch font-semibold text-neutral-100 text-[28px] [font-family:'Montserrat',Helvetica]\",\n                  children: \"$43,700\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 116,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center gap-2\",\n                  children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                    xmlns: \"http://www.w3.org/2000/svg\",\n                    width: \"24\",\n                    height: \"24\",\n                    viewBox: \"0 0 24 24\",\n                    fill: \"none\",\n                    stroke: \"currentColor\",\n                    strokeWidth: \"2\",\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    className: \"lucide lucide-trending-up w-4 h-4 text-[#7cca8d]\",\n                    \"aria-hidden\": \"true\",\n                    children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                      d: \"M16 7h6v6\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 119,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                      d: \"m22 7-8.5 8.5-5-5L2 17\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 120,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 118,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"font-normal text-xs [font-family:'Montserrat',Helvetica] text-[#7cca8d]\",\n                    children: \"+ 4.8% Today\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 122,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 117,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 112,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 111,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"rounded-xl border text-card-foreground shadow flex-1 bg-transparent border-neutral-700 mb-4\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex flex-col items-start gap-4 p-4\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center gap-2 w-full mt-[-1.00px]\",\n                  children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"font-medium text-neutral-300 text-sm [font-family:'Montserrat',Helvetica]\",\n                    children: \"Funds Available to Fund Startups\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 130,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"svg\", {\n                    xmlns: \"http://www.w3.org/2000/svg\",\n                    width: \"24\",\n                    height: \"24\",\n                    viewBox: \"0 0 24 24\",\n                    fill: \"none\",\n                    stroke: \"currentColor\",\n                    strokeWidth: \"2\",\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    className: \"lucide lucide-info w-5 h-5 text-neutral-300\",\n                    \"aria-hidden\": \"true\",\n                    children: [/*#__PURE__*/_jsxDEV(\"circle\", {\n                      cx: \"12\",\n                      cy: \"12\",\n                      r: \"10\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 132,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                      d: \"M12 16v-4\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 133,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                      d: \"M12 8h.01\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 134,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 131,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 129,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"self-stretch font-semibold text-neutral-100 text-[28px] [font-family:'Montserrat',Helvetica]\",\n                  children: \"$2,185\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 137,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center gap-2\",\n                  children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                    xmlns: \"http://www.w3.org/2000/svg\",\n                    width: \"24\",\n                    height: \"24\",\n                    viewBox: \"0 0 24 24\",\n                    fill: \"none\",\n                    stroke: \"currentColor\",\n                    strokeWidth: \"2\",\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    className: \"lucide lucide-trending-up w-4 h-4 text-[#7cca8d]\",\n                    \"aria-hidden\": \"true\",\n                    children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                      d: \"M16 7h6v6\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 140,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                      d: \"m22 7-8.5 8.5-5-5L2 17\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 141,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 139,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"font-normal text-xs [font-family:'Montserrat',Helvetica] text-[#7cca8d]\",\n                    children: \"+ 4.8% Today\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 143,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 138,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 128,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 127,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 94,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"rounded-xl border text-card-foreground shadow w-full bg-transparent border-neutral-700\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"p-6 pt-0 md:flex md:items-start md:gap-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex-1 flex flex-col items-start gap-3 p-4 rounded-2xl overflow-hidden\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center gap-3 w-full\",\n                  children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                    className: \"w-11 h-11 rounded-4xl\",\n                    alt: \"BTC HOLDINGS\",\n                    src: \"https://c.animaapp.com/mc62dpc6QTKBF1/img/bitcoin-symbol-png.png\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 154,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex flex-col items-start gap-3 flex-1\",\n                    children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"self-stretch font-medium text-neutral-300 text-sm [font-family:'Montserrat',Helvetica]\",\n                      children: \"BTC HOLDINGS\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 156,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"self-stretch font-semibold text-neutral-100 text-xl [font-family:'Montserrat',Helvetica]\",\n                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                        children: \"$91,000 \"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 158,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"text-sm\",\n                        children: \"BTC\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 159,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 157,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 155,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 153,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center gap-2\",\n                  children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                    xmlns: \"http://www.w3.org/2000/svg\",\n                    width: \"24\",\n                    height: \"24\",\n                    viewBox: \"0 0 24 24\",\n                    fill: \"none\",\n                    stroke: \"currentColor\",\n                    strokeWidth: \"2\",\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    className: \"lucide lucide-trending-up w-4 h-4 text-[#7cca8d]\",\n                    \"aria-hidden\": \"true\",\n                    children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                      d: \"M16 7h6v6\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 165,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                      d: \"m22 7-8.5 8.5-5-5L2 17\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 166,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 164,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"font-normal text-xs [font-family:'Montserrat',Helvetica] text-[#7cca8d]\",\n                    children: \"+ 4.8% Today\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 168,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 163,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 152,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                  className: \"self-stretch object-cover py-2 hidden md:block\",\n                  alt: \"Line\",\n                  src: \"https://c.animaapp.com/mc62dpc6QTKBF1/img/line-1.svg\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 173,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  \"data-orientation\": \"horizontal\",\n                  role: \"none\",\n                  className: \"shrink-0 h-[1px] w-full bg-neutral-700\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 174,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 172,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex-1 flex flex-col items-start gap-3 p-4 rounded-2xl overflow-hidden\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center gap-3 w-full\",\n                  children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                    className: \"w-11 h-11 rounded-4xl\",\n                    alt: \"SOLANA HOLDINGS\",\n                    src: \"https://c.animaapp.com/mc62dpc6QTKBF1/img/solana-icon-jpeg.png\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 179,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex flex-col items-start gap-3 flex-1\",\n                    children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"self-stretch font-medium text-neutral-300 text-sm [font-family:'Montserrat',Helvetica]\",\n                      children: \"SOLANA HOLDINGS\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 181,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"self-stretch font-semibold text-neutral-100 text-xl [font-family:'Montserrat',Helvetica]\",\n                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                        children: \"$124,000 \"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 183,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"text-sm\",\n                        children: \"SOL\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 184,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 182,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 180,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 178,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center gap-2\",\n                  children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                    xmlns: \"http://www.w3.org/2000/svg\",\n                    width: \"24\",\n                    height: \"24\",\n                    viewBox: \"0 0 24 24\",\n                    fill: \"none\",\n                    stroke: \"currentColor\",\n                    strokeWidth: \"2\",\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    className: \"lucide lucide-trending-up w-4 h-4 text-[#7cca8d]\",\n                    \"aria-hidden\": \"true\",\n                    children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                      d: \"M16 7h6v6\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 190,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                      d: \"m22 7-8.5 8.5-5-5L2 17\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 191,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 189,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"font-normal text-xs [font-family:'Montserrat',Helvetica] text-[#7cca8d]\",\n                    children: \"+ 4.8% Today\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 193,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 188,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 177,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                  className: \"self-stretch object-cover py-2 hidden md:block\",\n                  alt: \"Line\",\n                  src: \"https://c.animaapp.com/mc62dpc6QTKBF1/img/line-1.svg\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 198,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  \"data-orientation\": \"horizontal\",\n                  role: \"none\",\n                  className: \"shrink-0 h-[1px] w-full bg-neutral-700\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 199,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 197,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex-1 flex flex-col items-start gap-3 p-4 rounded-2xl overflow-hidden\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center gap-3 w-full\",\n                  children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                    className: \"w-11 h-11 rounded-4xl\",\n                    alt: \"USDC HOLDINGS\",\n                    src: \"https://c.animaapp.com/mc62dpc6QTKBF1/img/image.png\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 204,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex flex-col items-start gap-3 flex-1\",\n                    children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"self-stretch font-medium text-neutral-300 text-sm [font-family:'Montserrat',Helvetica]\",\n                      children: \"USDC HOLDINGS\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 206,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"self-stretch font-semibold text-neutral-100 text-xl [font-family:'Montserrat',Helvetica]\",\n                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                        children: \"$51,500 \"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 208,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"text-sm\",\n                        children: \"USDC\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 209,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 207,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 205,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 203,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center gap-2\",\n                  children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                    className: \"w-4 h-4\",\n                    alt: \"Trend icon\",\n                    src: \"https://c.animaapp.com/mc62dpc6QTKBF1/img/trend-icon-1.svg\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 214,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"font-normal text-xs [font-family:'Montserrat',Helvetica] text-[#f58f8f]\",\n                    children: \"- 0.9% Today\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 215,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 213,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 202,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                  className: \"self-stretch object-cover py-2 hidden md:block\",\n                  alt: \"Line\",\n                  src: \"https://c.animaapp.com/mc62dpc6QTKBF1/img/line-1.svg\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 220,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  \"data-orientation\": \"horizontal\",\n                  role: \"none\",\n                  className: \"shrink-0 h-[1px] w-full bg-neutral-700\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 221,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 219,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex-1 flex flex-col items-start gap-3 p-4 rounded-2xl overflow-hidden\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center gap-3 w-full\",\n                  children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                    className: \"w-11 h-11 rounded-4xl\",\n                    alt: \"ESVC RESERVES\",\n                    src: \"https://c.animaapp.com/mc62dpc6QTKBF1/img/image-1.png\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 226,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex flex-col items-start gap-3 flex-1\",\n                    children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"self-stretch font-medium text-neutral-300 text-sm [font-family:'Montserrat',Helvetica]\",\n                      children: \"ESVC RESERVES\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 228,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"self-stretch font-semibold text-neutral-100 text-xl [font-family:'Montserrat',Helvetica]\",\n                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                        children: \"$51,500 \"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 230,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"text-sm\",\n                        children: \"ESVC\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 231,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 229,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 227,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 225,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center gap-2\",\n                  children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                    className: \"w-4 h-4\",\n                    alt: \"Trend icon\",\n                    src: \"https://c.animaapp.com/mc62dpc6QTKBF1/img/trend-icon-1.svg\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 236,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"font-normal text-xs [font-family:'Montserrat',Helvetica] text-[#f58f8f]\",\n                    children: \"- 0.9% Today\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 237,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 235,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 224,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 151,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 150,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 93,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"img\", {\n          className: \"absolute w-[39px] md:w-[78px] md:h-[52px] bottom-17 right-[-20px] md:bottom-[0px] md:right-[250px]\",\n          alt: \"Element\",\n          src: \"https://c.animaapp.com/mc62dpc6QTKBF1/img/element-09.svg\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 244,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex justify-center items-center w-full\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"md:flex md:items-center md:justify-center md:gap-6 mt-10\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"inline-flex items-center justify-center gap-2 whitespace-nowrap transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0 text-primary-foreground shadow mb-5 md:mb-0 h-14 w-full px-4 py-2.5 bg-[#bf4129] hover:bg-[#a83a25] rounded-[999px] text-lg font-semibold [font-family:'Montserrat',Helvetica]\",\n              children: [\"Start Staking Now\", /*#__PURE__*/_jsxDEV(\"svg\", {\n                xmlns: \"http://www.w3.org/2000/svg\",\n                width: \"24\",\n                height: \"24\",\n                viewBox: \"0 0 24 24\",\n                fill: \"none\",\n                stroke: \"currentColor\",\n                strokeWidth: \"2\",\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                className: \"lucide lucide-rocket w-6 h-6 ml-3\",\n                \"aria-hidden\": \"true\",\n                children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                  d: \"M4.5 16.5c-1.5 1.26-2 5-2 5s3.74-.5 5-2c.71-.84.7-2.13-.09-2.91a2.18 2.18 0 0 0-2.91-.09z\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 251,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                  d: \"m12 15-3-3a22 22 0 0 1 2-3.95A12.88 12.88 0 0 1 22 2c0 2.72-.78 7.5-6 11a22.35 22.35 0 0 1-4 2z\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 252,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                  d: \"M9 12H4s.55-3.03 2-4c1.62-1.08 5 0 5 0\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 253,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                  d: \"M12 15v5s3.03-.55 4-2c1.08-1.62 0-5 0-5\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 254,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 250,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 248,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"inline-flex items-center justify-center gap-2 whitespace-nowrap transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0 border bg-background shadow-sm hover:bg-accent hover:text-accent-foreground h-14 w-full px-4 py-2.5 rounded-[999px] border-neutral-700 text-neutral-100 text-lg font-semibold [font-family:'Montserrat',Helvetica] hover:border-neutral-500\",\n              children: [\"View Full Breakdown\", /*#__PURE__*/_jsxDEV(\"svg\", {\n                xmlns: \"http://www.w3.org/2000/svg\",\n                width: \"24\",\n                height: \"24\",\n                viewBox: \"0 0 24 24\",\n                fill: \"none\",\n                stroke: \"currentColor\",\n                strokeWidth: \"2\",\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                className: \"lucide lucide-arrow-right w-6 h-6 ml-3\",\n                \"aria-hidden\": \"true\",\n                children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                  d: \"M5 12h14\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 260,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                  d: \"m12 5 7 7-7 7\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 261,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 259,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 257,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 247,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 246,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 82,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 81,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"relative w-full\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute w-[239px] h-[239px] top-0 lg:right-0 rounded-[119.5px] blur-[100px] bg-[linear-gradient(55deg,rgba(209,144,73,1)_30%,rgba(191,65,41,1)_100%)] opacity-20\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 271,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mx-auto max-w-[1082px] relative\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"absolute w-[381px] h-[381px] top-20 left-0 bg-[#d19049] rounded-[190.5px] blur-[159.41px] opacity-20\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 273,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n          className: \"flex flex-col w-full max-w-[880px] items-center gap-10 mx-auto my-10\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex flex-col items-center gap-10 w-full\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex flex-col w-full max-w-[517px] items-center gap-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                className: \"w-full [font-family:'Montserrat',Helvetica] font-semibold text-neutral-100 text-[40px] text-center\",\n                children: \"What Makes Us Different\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 278,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"w-full [font-family:'Montserrat',Helvetica] font-normal text-neutral-300 text-base text-center\",\n                children: \"ESVC Staking Versus Traditional Crypto\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 279,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 277,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex w-full px-5 md:px-0\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex flex-col items-start gap-1 flex-1\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center justify-center gap-2.5 md:px-[76px] py-2.5 w-full bg-neutral-800 rounded-[16px_0px_0px_0px]\",\n                  children: /*#__PURE__*/_jsxDEV(\"h3\", {\n                    className: \"[font-family:'Montserrat',Helvetica] font-semibold text-neutral-100 md:text-2xl text-center\",\n                    children: \"Traditional Crypto\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 285,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 284,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"md:flex items-center gap-3 px-5 py-2.5 w-full border border-solid border-transparent [border-image:linear-gradient(249deg,rgba(255,255,255,1)_0%,rgba(255,255,255,0)_54%,rgba(255,255,255,1)_100%)_1]\",\n                  children: /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"[font-family:'Montserrat',Helvetica] font-normal text-neutral-300 text-[10px] md:text-xl text-center whitespace-nowrap\",\n                    children: \"Speculative Trading\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 288,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 287,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"md:flex items-center gap-3 px-5 py-2.5 w-full border border-solid border-transparent [border-image:linear-gradient(249deg,rgba(255,255,255,1)_0%,rgba(255,255,255,0)_54%,rgba(255,255,255,1)_100%)_1]\",\n                  children: /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"[font-family:'Montserrat',Helvetica] font-normal text-neutral-300 text-[10px] md:text-xl text-center whitespace-nowrap\",\n                    children: \"No revenue backing\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 291,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 290,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"md:flex items-center gap-3 px-5 py-2.5 w-full border border-solid border-transparent [border-image:linear-gradient(249deg,rgba(255,255,255,1)_0%,rgba(255,255,255,0)_54%,rgba(255,255,255,1)_100%)_1]\",\n                  children: /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"[font-family:'Montserrat',Helvetica] font-normal text-neutral-300 text-[10px] md:text-xl text-center whitespace-nowrap\",\n                    children: \"Opaque Tokenomics\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 294,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 293,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"md:flex items-center gap-3 px-5 py-2.5 w-full rounded-bl-2xl border\",\n                  children: /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"[font-family:'Montserrat',Helvetica] font-normal text-neutral-300 text-[10px] md:text-xl text-center whitespace-nowrap\",\n                    children: \"Low staking rewards\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 297,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 296,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 283,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex flex-col items-start gap-1 flex-1\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center justify-center gap-2.5 md:px-[76px] py-2.5 w-full rounded-[0px_16px_0px_0px] bg-[linear-gradient(38deg,rgba(209,144,73,1)_36%,rgba(191,65,41,1)_100%),linear-gradient(0deg,rgba(38,38,38,1)_0%,rgba(38,38,38,1)_100%)]\",\n                  children: /*#__PURE__*/_jsxDEV(\"h3\", {\n                    className: \"[font-family:'Montserrat',Helvetica] font-semibold text-neutral-100 md:text-2xl text-center\",\n                    children: \"ESVC\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 303,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 302,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"md:flex items-center gap-3 px-5 py-2.5 w-full border border-solid border-transparent [border-image:linear-gradient(38deg,rgba(209,144,73,1)_36%,rgba(191,65,41,1)_100%)_1]\",\n                  children: /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"[font-family:'Montserrat',Helvetica] font-normal text-neutral-300 text-[10px] md:text-xl text-center whitespace-nowrap\",\n                    children: \"Token-gated startup funding\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 306,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 305,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"md:flex items-center gap-3 px-5 py-2.5 w-full border border-solid border-transparent [border-image:linear-gradient(38deg,rgba(209,144,73,1)_36%,rgba(191,65,41,1)_100%)_1]\",\n                  children: /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"[font-family:'Montserrat',Helvetica] font-normal text-neutral-300 text-[10px] md:text-xl text-center whitespace-nowrap\",\n                    children: \"Backed by real assets\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 309,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 308,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"md:flex items-center gap-3 px-5 py-2.5 w-full border border-solid border-transparent [border-image:linear-gradient(38deg,rgba(209,144,73,1)_36%,rgba(191,65,41,1)_100%)_1]\",\n                  children: /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"[font-family:'Montserrat',Helvetica] font-normal text-neutral-300 text-[10px] md:text-xl text-center whitespace-nowrap\",\n                    children: \"Transparent treasury\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 312,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 311,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"md:flex items-center gap-3 px-5 py-2.5 w-full rounded-br-2xl border border-[#D19049]\",\n                  children: /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"[font-family:'Montserrat',Helvetica] font-normal text-neutral-300 text-[10px] md:text-xl text-center whitespace-nowrap\",\n                    children: \"High-yield ESVC staking\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 315,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 314,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 301,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 282,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 276,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"md:flex items-center gap-6\",\n              children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                className: \"justify-center whitespace-nowrap transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0 shadow h-14 w-full px-4 py-2.5 bg-[#bf4129] hover:bg-[#a83a25] rounded-[999px] [font-family:'Montserrat',Helvetica] font-semibold text-neutral-50 text-lg flex items-center gap-3 mb-5 md:mb-0\",\n                children: [\"Start Staking Now\", /*#__PURE__*/_jsxDEV(\"img\", {\n                  className: \"w-6 h-6\",\n                  alt: \"RocketIcon launch\",\n                  src: \"https://c.animaapp.com/mc62dpc6QTKBF1/img/rocket-launch-24dp-00000-fill0-wght300-grad0-opsz24-1.svg\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 325,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 323,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                className: \"inline-flex items-center justify-center gap-2 whitespace-nowrap transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0 bg-background shadow-sm hover:bg-accent hover:text-accent-foreground w-full h-14 px-4 py-2.5 rounded-[999px] border border-solid border-neutral-700 [font-family:'Montserrat',Helvetica] font-semibold text-neutral-100 text-lg\",\n                children: \"See How it Works\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 327,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 322,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 321,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 275,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 272,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 270,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"relative w-full\",\n      children: /*#__PURE__*/_jsxDEV(\"section\", {\n        className: \"min-h-screen flex flex-col items-center justify-center bg-dark p-4 sm:p-6 lg:p-8 relative w-full bg-[#1d1104] py-20 overflow-hidden\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"w-full max-w-2xl\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"absolute w-[234px] h-[234px] top-[141px] lg:left-1/2 -translate-x-1/2 bg-[#d19049] rounded-[117px] blur-[97.91px] opacity-20\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 340,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"absolute w-[239px] h-[239px] top-0 lg:right-[-120px] rounded-[119.5px] blur-[100px] bg-[linear-gradient(55deg,rgba(209,144,73,1)_30%,rgba(191,65,41,1)_100%)] opacity-20\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 341,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"absolute w-[79px] h-[79px] bottom-[80px] lg:right-[-40px] rounded-[39.5px] blur-[33.05px] bg-[linear-gradient(55deg,rgba(209,144,73,1)_30%,rgba(191,65,41,1)_100%)] opacity-20\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 342,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"absolute w-[239px] h-[239px] bottom-[40px] lg:left-[-120px] rounded-[119.5px] blur-[100px] bg-[linear-gradient(55deg,rgba(209,144,73,1)_30%,rgba(191,65,41,1)_100%)] opacity-20\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 343,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-center font-['Montserrat',Helvetica] font-semibold text-neutral-100 text-[40px] mb-12\",\n            children: \"Frequently Asked Questions\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 345,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-4\",\n            children: faqData.map((faq, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"border border-neutral-700 rounded-lg overflow-hidden mb-4 bg-[#281705]\",\n              children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                className: \"w-full text-left p-2 flex justify-between items-center text-neutral-100\",\n                \"aria-expanded\": openAccordion === index,\n                \"aria-controls\": `accordion-content-${index}`,\n                onClick: () => toggleAccordion(index),\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-lg font-semibold pr-4 text-white\",\n                  children: [index + 1, \". \", faq.question]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 356,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"flex-shrink-0 border rounded-lg p-1.5 border-neutral-700\",\n                  children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                    xmlns: \"http://www.w3.org/2000/svg\",\n                    width: \"24\",\n                    height: \"24\",\n                    viewBox: \"0 0 24 24\",\n                    fill: \"none\",\n                    stroke: \"currentColor\",\n                    strokeWidth: \"2\",\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    className: `lucide lucide-plus w-6 h-6 text-neutral-700 transition-transform duration-200 ${openAccordion === index ? 'rotate-45' : ''}`,\n                    \"aria-hidden\": \"true\",\n                    children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                      d: \"M5 12h14\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 373,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                      d: \"M12 5v14\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 374,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 360,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 359,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 350,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                id: `accordion-content-${index}`,\n                role: \"region\",\n                \"aria-labelledby\": `accordion-button-${index}`,\n                className: `grid transition-all duration-300 ease-in-out ${openAccordion === index ? 'grid-rows-[1fr] opacity-100' : 'grid-rows-[0fr] opacity-0'}`,\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"overflow-hidden\",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"p-5 pt-0 text-textMuted leading-relaxed text-[#d4d4d4]\",\n                    children: faq.answer\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 385,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 384,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 378,\n                columnNumber: 19\n              }, this)]\n            }, index, true, {\n              fileName: _jsxFileName,\n              lineNumber: 349,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 347,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex flex-col sm:flex-row justify-center items-center gap-4 mt-10\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"flex items-center bg-[#bf4129] hover:bg-[#a83a25] text-white py-3 px-6 rounded-full font-semibold text-lg transition-colors duration-200 shadow-lg\",\n              children: [\"Start Staking Now\", /*#__PURE__*/_jsxDEV(\"svg\", {\n                xmlns: \"http://www.w3.org/2000/svg\",\n                width: \"24\",\n                height: \"24\",\n                viewBox: \"0 0 24 24\",\n                fill: \"none\",\n                stroke: \"currentColor\",\n                strokeWidth: \"2\",\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                className: \"lucide lucide-rocket ml-2 w-5 h-5\",\n                \"aria-hidden\": \"true\",\n                children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                  d: \"M4.5 16.5c-1.5 1.26-2 5-2 5s3.74-.5 5-2c.71-.84.7-2.13-.09-2.91a2.18 2.18 0 0 0-2.91-.09z\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 398,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                  d: \"m12 15-3-3a22 22 0 0 1 2-3.95A12.88 12.88 0 0 1 22 2c0 2.72-.78 7.5-6 11a22.35 22.35 0 0 1-4 2z\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 399,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                  d: \"M9 12H4s.55-3.03 2-4c1.62-1.08 5 0 5 0\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 400,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                  d: \"M12 15v5s3.03-.55 4-2c1.08-1.62 0-5 0-5\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 401,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 397,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 395,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"bg-transparent text-textLight border border-textLight py-3 px-6 rounded-full font-semibold text-lg hover:border-primary hover:text-primary transition-colors duration-200\",\n              children: \"See How It Works\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 404,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 394,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 339,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 338,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 337,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 34,\n    columnNumber: 5\n  }, this);\n};\n_s(Index, \"RUALCq4MH9WUBPfjujTmJuoSe9E=\");\n_c = Index;\nexport default Index;\nvar _c;\n$RefreshReg$(_c, \"Index\");", "map": {"version": 3, "names": ["React", "useState", "jsxDEV", "_jsxDEV", "Index", "_s", "openAccordion", "setOpenAccordion", "to<PERSON><PERSON><PERSON><PERSON><PERSON>", "index", "faqData", "question", "answer", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "alt", "src", "xmlns", "width", "height", "viewBox", "fill", "stroke", "strokeWidth", "strokeLinecap", "strokeLinejoin", "d", "cx", "cy", "r", "role", "map", "faq", "onClick", "id", "_c", "$RefreshReg$"], "sources": ["C:/My Web Sites/esvc/esvc-react/src/pages/Index.tsx"], "sourcesContent": ["import React, { useState } from 'react';\n\nconst Index: React.FC = () => {\n  const [openAccordion, setOpenAccordion] = useState<number | null>(null);\n\n  const toggleAccordion = (index: number) => {\n    setOpenAccordion(openAccordion === index ? null : index);\n  };\n\n  const faqData = [\n    {\n      question: \"What is ESVC?\",\n      answer: \"ESVC is a decentralized staking protocol that allows users to earn rewards by staking their ESVC tokens. It leverages the Solana blockchain for high-speed and low-cost transactions.\"\n    },\n    {\n      question: \"How do I stake ESVC?\",\n      answer: \"Simply click \\\"Start Staking Now\\\", create an account, choose how much you want to stake (in USD), and deposit using either Solana (SOL) or USDC. Your ESVC tokens will be automatically purchased and staked for a 6 or 12-month period.\"\n    },\n    {\n      question: \"What's the minimum amount I can stake?\",\n      answer: \"The minimum staking amount is 50 USD to ensure efficient transaction processing and reward distribution within the protocol.\"\n    },\n    {\n      question: \"How much ROI can I earn?\",\n      answer: \"Expected ROI varies based on staking period and current network conditions. Typically, annual returns range from 8% to 15%. Detailed projections are available after you log in.\"\n    },\n    {\n      question: \"When can I withdraw my ROI?\",\n      answer: \"Your earned ROI can be withdrawn at the end of your chosen staking period (6 or 12 months). Partial withdrawals during the staking period are not allowed to maintain network stability.\"\n    }\n  ];\n\n  return (\n    <main className=\"w-full bg-neutral-900\" data-model-id=\"1:21\">\n      {/* Hero Section */}\n      <section className=\"relative w-full\">\n        <div className=\"absolute w-[239px] h-[239px] top-0 lg:right-0 bg-[#d19049] rounded-[119.5px] blur-[100px] opacity-30\"></div>\n        <div className=\"absolute w-[239px] h-[239px] top-[232px] lg:left-[22px] bg-[#cc6754] rounded-[119.5px] blur-[100px] opacity-30\"></div>\n        \n        <section className=\"flex flex-col items-center gap-8 w-full max-w-4xl mx-auto mb-20 py-5 px-5 lg:px-0\">\n          <div className=\"flex flex-col items-center gap-4 w-full text-center relative\">\n            <div className=\"inline-flex items-center justify-center gap-2.5 px-4 rounded-full\"></div>\n            <div className=\"border border-neutral-700 rounded-full p-3 bg-gradient-to-r from-[#BF4129] to-[#D19049] bg-clip-text text-transparent\">\n              <h3>Earn 20% annual returns</h3>\n            </div>\n            <h1 className=\"font-['Montserrat'] font-bold md:text-[47px] text-[33px]\">\n              <span className=\"text-[#cc6754]\">Grow</span>\n              <span className=\"text-neutral-100\"> Your Wealth. </span>\n              <span className=\"text-[#d19049]\">Get</span>\n              <span className=\"text-neutral-100\"> Funded. <br/> Stake and Earn .</span>\n            </h1>\n            <p className=\"max-w-[627px] font-['Montserrat',Helvetica] font-normal text-neutral-300 text-base leading-6\">\n              Stake your ESVC tokens and earn daily ROI. <br/>\n              Unlock exclusive opportunities to pitch your startup ideas for funding/get funded trade capital. <br/>\n              Earn from our Bitcoin/Crypto Treasury\n            </p>\n            <img \n              className=\"top-[220px] w-[200px] h-[13px] absolute md:top-[210px] md:w-[300px] right-1 md:right-1 md:-translate-x-1/2\" \n              alt=\"Decorative line\" \n              src=\"https://c.animaapp.com/mc62dpc6QTKBF1/img/vector-1.svg\"\n            />\n            <img \n              className=\"absolute w-44 h-[50px] top-[179px] left-[20px] md:w-44 md:h-[75px] md:top-[150px] md:left-[220px]\" \n              alt=\"Vector graphic circle\" \n              src=\"https://c.animaapp.com/mc62dpc6QTKBF1/img/vector.svg\"\n            />\n          </div>\n          <button className=\"whitespace-nowrap text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0 text-primary-foreground shadow h-14 bg-[#bf4129] hover:bg-[#a83a25] rounded-full px-4 py-2.5 flex items-center justify-center gap-3\">\n            <span className=\"font-['Montserrat',Helvetica] font-semibold text-neutral-50 text-lg\">Start Staking Now</span>\n            <img className=\"w-6 h-6\" alt=\"Rocket launch\" src=\"https://c.animaapp.com/mc62dpc6QTKBF1/img/rocket-launch-24dp-00000-fill0-wght300-grad0-opsz24-1.svg\"/>\n          </button>\n        </section>\n\n        <img className=\"absolute w-[106px] top-[401px] right-[-10px] md:w-[296px] md:h-[296px] md:top-[187px] md:right-[5px] object-cover\" alt=\"Decorative coin image gold\" src=\"https://c.animaapp.com/mc62dpc6QTKBF1/img/whatsapp-image-2025-06-13-at-6-11-51-pm--2--1.png\"/>\n        <img className=\"w-[106px] h-[106px] top-[401px] left-0 md:w-[370px] md:h-[370px] md:top-[179px] md:left-0 absolute object-cover\" alt=\"Decorative coin image red big\" src=\"https://c.animaapp.com/mc62dpc6QTKBF1/img/whatsapp-image-2025-06-13-at-6-11-51-pm--1--2.png\"/>\n        <img className=\"w-[78px] h-[78px] top-[492px] right-[20px] md:w-[156px] md:h-[156px] md:top-[352px] md:right-[256px] absolute object-cover\" alt=\"Decorative coin image red small right\" src=\"https://c.animaapp.com/mc62dpc6QTKBF1/img/whatsapp-image-2025-06-13-at-6-11-51-pm--1--2.png\"/>\n        <img className=\"absolute w-[94px] h-[94px] top-[492px] left-[10px] md:w-[188px] md:h-[188px] md:top-[352px] md:left-[276px] object-cover\" alt=\"Decorative coin image\" src=\"https://c.animaapp.com/mc62dpc6QTKBF1/img/whatsapp-image-2025-06-13-at-6-11-51-pm-1.png\"/>\n      </section>\n\n      {/* Treasury Dashboard Section */}\n      <section className=\"w-full p-10 bg-neutral-800\">\n        <div className=\"container max-w-[1200px] mx-auto relative\">\n          <div className=\"absolute w-[239px] h-[239px] top-[73px] lg:left-[41px] md:left-[481px] rounded-[119.5px] blur-[100px] bg-[linear-gradient(55deg,rgba(209,144,73,1)_30%,rgba(191,65,41,1)_100%)] opacity-20\"></div>\n\n          <div className=\"flex flex-col items-center gap-4 mb-10 relative\">\n            <h2 className=\"text-[40px] font-semibold text-neutral-100 text-center [font-family:'Montserrat',Helvetica]\">Treasury Dashboard</h2>\n            <p className=\"max-w-[670px] font-normal text-neutral-300 text-base text-center leading-6 [font-family:'Montserrat',Helvetica]\">\n              Live insights into how funds are held, ROI is distributed, and startups are supported. Empowering you to stake with trust.\n            </p>\n            <img className=\"hidden absolute w-[154px] h-[18px] top-[43px] left-[407px]\" alt=\"Vector\" src=\"https://c.animaapp.com/mc62dpc6QTKBF1/img/vector-2.svg\"/>\n          </div>\n\n          <div className=\"md:flex md:flex-col w-full gap-6\">\n            <div className=\"md:flex items-center gap-6 w-full\">\n              <div className=\"rounded-xl border text-card-foreground shadow flex-1 bg-transparent border-neutral-700 mb-4\">\n                <div className=\"flex flex-col items-start gap-4 p-4\">\n                  <div className=\"flex items-center gap-2 w-full mt-[-1.00px]\">\n                    <p className=\"font-medium text-neutral-300 text-sm [font-family:'Montserrat',Helvetica]\">Total Capital Invested</p>\n                  </div>\n                  <p className=\"self-stretch font-semibold text-neutral-100 text-[28px] [font-family:'Montserrat',Helvetica]\">$266,500</p>\n                  <div className=\"flex items-center gap-2\">\n                    <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\" className=\"lucide lucide-trending-up w-4 h-4 text-[#7cca8d]\" aria-hidden=\"true\">\n                      <path d=\"M16 7h6v6\"></path>\n                      <path d=\"m22 7-8.5 8.5-5-5L2 17\"></path>\n                    </svg>\n                    <span className=\"font-normal text-xs [font-family:'Montserrat',Helvetica] text-[#7cca8d]\">+ 4.8% Today</span>\n                  </div>\n                </div>\n              </div>\n\n              <div className=\"rounded-xl border text-card-foreground shadow flex-1 bg-transparent border-neutral-700 mb-4\">\n                <div className=\"flex flex-col items-start gap-4 p-4\">\n                  <div className=\"flex items-center gap-2 w-full mt-[-1.00px]\">\n                    <p className=\"font-medium text-neutral-300 text-sm [font-family:'Montserrat',Helvetica]\">Total Profit Generated</p>\n                  </div>\n                  <p className=\"self-stretch font-semibold text-neutral-100 text-[28px] [font-family:'Montserrat',Helvetica]\">$43,700</p>\n                  <div className=\"flex items-center gap-2\">\n                    <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\" className=\"lucide lucide-trending-up w-4 h-4 text-[#7cca8d]\" aria-hidden=\"true\">\n                      <path d=\"M16 7h6v6\"></path>\n                      <path d=\"m22 7-8.5 8.5-5-5L2 17\"></path>\n                    </svg>\n                    <span className=\"font-normal text-xs [font-family:'Montserrat',Helvetica] text-[#7cca8d]\">+ 4.8% Today</span>\n                  </div>\n                </div>\n              </div>\n\n              <div className=\"rounded-xl border text-card-foreground shadow flex-1 bg-transparent border-neutral-700 mb-4\">\n                <div className=\"flex flex-col items-start gap-4 p-4\">\n                  <div className=\"flex items-center gap-2 w-full mt-[-1.00px]\">\n                    <p className=\"font-medium text-neutral-300 text-sm [font-family:'Montserrat',Helvetica]\">Funds Available to Fund Startups</p>\n                    <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\" className=\"lucide lucide-info w-5 h-5 text-neutral-300\" aria-hidden=\"true\">\n                      <circle cx=\"12\" cy=\"12\" r=\"10\"></circle>\n                      <path d=\"M12 16v-4\"></path>\n                      <path d=\"M12 8h.01\"></path>\n                    </svg>\n                  </div>\n                  <p className=\"self-stretch font-semibold text-neutral-100 text-[28px] [font-family:'Montserrat',Helvetica]\">$2,185</p>\n                  <div className=\"flex items-center gap-2\">\n                    <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\" className=\"lucide lucide-trending-up w-4 h-4 text-[#7cca8d]\" aria-hidden=\"true\">\n                      <path d=\"M16 7h6v6\"></path>\n                      <path d=\"m22 7-8.5 8.5-5-5L2 17\"></path>\n                    </svg>\n                    <span className=\"font-normal text-xs [font-family:'Montserrat',Helvetica] text-[#7cca8d]\">+ 4.8% Today</span>\n                  </div>\n                </div>\n              </div>\n            </div>\n\n            {/* Crypto Holdings Section */}\n            <div className=\"rounded-xl border text-card-foreground shadow w-full bg-transparent border-neutral-700\">\n              <div className=\"p-6 pt-0 md:flex md:items-start md:gap-4\">\n                <div className=\"flex-1 flex flex-col items-start gap-3 p-4 rounded-2xl overflow-hidden\">\n                  <div className=\"flex items-center gap-3 w-full\">\n                    <img className=\"w-11 h-11 rounded-4xl\" alt=\"BTC HOLDINGS\" src=\"https://c.animaapp.com/mc62dpc6QTKBF1/img/bitcoin-symbol-png.png\"/>\n                    <div className=\"flex flex-col items-start gap-3 flex-1\">\n                      <p className=\"self-stretch font-medium text-neutral-300 text-sm [font-family:'Montserrat',Helvetica]\">BTC HOLDINGS</p>\n                      <p className=\"self-stretch font-semibold text-neutral-100 text-xl [font-family:'Montserrat',Helvetica]\">\n                        <span>$91,000 </span>\n                        <span className=\"text-sm\">BTC</span>\n                      </p>\n                    </div>\n                  </div>\n                  <div className=\"flex items-center gap-2\">\n                    <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\" className=\"lucide lucide-trending-up w-4 h-4 text-[#7cca8d]\" aria-hidden=\"true\">\n                      <path d=\"M16 7h6v6\"></path>\n                      <path d=\"m22 7-8.5 8.5-5-5L2 17\"></path>\n                    </svg>\n                    <span className=\"font-normal text-xs [font-family:'Montserrat',Helvetica] text-[#7cca8d]\">+ 4.8% Today</span>\n                  </div>\n                </div>\n\n                <div>\n                  <img className=\"self-stretch object-cover py-2 hidden md:block\" alt=\"Line\" src=\"https://c.animaapp.com/mc62dpc6QTKBF1/img/line-1.svg\"/>\n                  <div data-orientation=\"horizontal\" role=\"none\" className=\"shrink-0 h-[1px] w-full bg-neutral-700\"></div>\n                </div>\n\n                <div className=\"flex-1 flex flex-col items-start gap-3 p-4 rounded-2xl overflow-hidden\">\n                  <div className=\"flex items-center gap-3 w-full\">\n                    <img className=\"w-11 h-11 rounded-4xl\" alt=\"SOLANA HOLDINGS\" src=\"https://c.animaapp.com/mc62dpc6QTKBF1/img/solana-icon-jpeg.png\"/>\n                    <div className=\"flex flex-col items-start gap-3 flex-1\">\n                      <p className=\"self-stretch font-medium text-neutral-300 text-sm [font-family:'Montserrat',Helvetica]\">SOLANA HOLDINGS</p>\n                      <p className=\"self-stretch font-semibold text-neutral-100 text-xl [font-family:'Montserrat',Helvetica]\">\n                        <span>$124,000 </span>\n                        <span className=\"text-sm\">SOL</span>\n                      </p>\n                    </div>\n                  </div>\n                  <div className=\"flex items-center gap-2\">\n                    <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\" className=\"lucide lucide-trending-up w-4 h-4 text-[#7cca8d]\" aria-hidden=\"true\">\n                      <path d=\"M16 7h6v6\"></path>\n                      <path d=\"m22 7-8.5 8.5-5-5L2 17\"></path>\n                    </svg>\n                    <span className=\"font-normal text-xs [font-family:'Montserrat',Helvetica] text-[#7cca8d]\">+ 4.8% Today</span>\n                  </div>\n                </div>\n\n                <div>\n                  <img className=\"self-stretch object-cover py-2 hidden md:block\" alt=\"Line\" src=\"https://c.animaapp.com/mc62dpc6QTKBF1/img/line-1.svg\"/>\n                  <div data-orientation=\"horizontal\" role=\"none\" className=\"shrink-0 h-[1px] w-full bg-neutral-700\"></div>\n                </div>\n\n                <div className=\"flex-1 flex flex-col items-start gap-3 p-4 rounded-2xl overflow-hidden\">\n                  <div className=\"flex items-center gap-3 w-full\">\n                    <img className=\"w-11 h-11 rounded-4xl\" alt=\"USDC HOLDINGS\" src=\"https://c.animaapp.com/mc62dpc6QTKBF1/img/image.png\"/>\n                    <div className=\"flex flex-col items-start gap-3 flex-1\">\n                      <p className=\"self-stretch font-medium text-neutral-300 text-sm [font-family:'Montserrat',Helvetica]\">USDC HOLDINGS</p>\n                      <p className=\"self-stretch font-semibold text-neutral-100 text-xl [font-family:'Montserrat',Helvetica]\">\n                        <span>$51,500 </span>\n                        <span className=\"text-sm\">USDC</span>\n                      </p>\n                    </div>\n                  </div>\n                  <div className=\"flex items-center gap-2\">\n                    <img className=\"w-4 h-4\" alt=\"Trend icon\" src=\"https://c.animaapp.com/mc62dpc6QTKBF1/img/trend-icon-1.svg\"/>\n                    <span className=\"font-normal text-xs [font-family:'Montserrat',Helvetica] text-[#f58f8f]\">- 0.9% Today</span>\n                  </div>\n                </div>\n\n                <div>\n                  <img className=\"self-stretch object-cover py-2 hidden md:block\" alt=\"Line\" src=\"https://c.animaapp.com/mc62dpc6QTKBF1/img/line-1.svg\"/>\n                  <div data-orientation=\"horizontal\" role=\"none\" className=\"shrink-0 h-[1px] w-full bg-neutral-700\"></div>\n                </div>\n\n                <div className=\"flex-1 flex flex-col items-start gap-3 p-4 rounded-2xl overflow-hidden\">\n                  <div className=\"flex items-center gap-3 w-full\">\n                    <img className=\"w-11 h-11 rounded-4xl\" alt=\"ESVC RESERVES\" src=\"https://c.animaapp.com/mc62dpc6QTKBF1/img/image-1.png\"/>\n                    <div className=\"flex flex-col items-start gap-3 flex-1\">\n                      <p className=\"self-stretch font-medium text-neutral-300 text-sm [font-family:'Montserrat',Helvetica]\">ESVC RESERVES</p>\n                      <p className=\"self-stretch font-semibold text-neutral-100 text-xl [font-family:'Montserrat',Helvetica]\">\n                        <span>$51,500 </span>\n                        <span className=\"text-sm\">ESVC</span>\n                      </p>\n                    </div>\n                  </div>\n                  <div className=\"flex items-center gap-2\">\n                    <img className=\"w-4 h-4\" alt=\"Trend icon\" src=\"https://c.animaapp.com/mc62dpc6QTKBF1/img/trend-icon-1.svg\"/>\n                    <span className=\"font-normal text-xs [font-family:'Montserrat',Helvetica] text-[#f58f8f]\">- 0.9% Today</span>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          <img className=\"absolute w-[39px] md:w-[78px] md:h-[52px] bottom-17 right-[-20px] md:bottom-[0px] md:right-[250px]\" alt=\"Element\" src=\"https://c.animaapp.com/mc62dpc6QTKBF1/img/element-09.svg\"/>\n\n          <div className=\"flex justify-center items-center w-full\">\n            <div className=\"md:flex md:items-center md:justify-center md:gap-6 mt-10\">\n              <button className=\"inline-flex items-center justify-center gap-2 whitespace-nowrap transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0 text-primary-foreground shadow mb-5 md:mb-0 h-14 w-full px-4 py-2.5 bg-[#bf4129] hover:bg-[#a83a25] rounded-[999px] text-lg font-semibold [font-family:'Montserrat',Helvetica]\">\n                Start Staking Now\n                <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\" className=\"lucide lucide-rocket w-6 h-6 ml-3\" aria-hidden=\"true\">\n                  <path d=\"M4.5 16.5c-1.5 1.26-2 5-2 5s3.74-.5 5-2c.71-.84.7-2.13-.09-2.91a2.18 2.18 0 0 0-2.91-.09z\"></path>\n                  <path d=\"m12 15-3-3a22 22 0 0 1 2-3.95A12.88 12.88 0 0 1 22 2c0 2.72-.78 7.5-6 11a22.35 22.35 0 0 1-4 2z\"></path>\n                  <path d=\"M9 12H4s.55-3.03 2-4c1.62-1.08 5 0 5 0\"></path>\n                  <path d=\"M12 15v5s3.03-.55 4-2c1.08-1.62 0-5 0-5\"></path>\n                </svg>\n              </button>\n              <button className=\"inline-flex items-center justify-center gap-2 whitespace-nowrap transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0 border bg-background shadow-sm hover:bg-accent hover:text-accent-foreground h-14 w-full px-4 py-2.5 rounded-[999px] border-neutral-700 text-neutral-100 text-lg font-semibold [font-family:'Montserrat',Helvetica] hover:border-neutral-500\">\n                View Full Breakdown\n                <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\" className=\"lucide lucide-arrow-right w-6 h-6 ml-3\" aria-hidden=\"true\">\n                  <path d=\"M5 12h14\"></path>\n                  <path d=\"m12 5 7 7-7 7\"></path>\n                </svg>\n              </button>\n            </div>\n          </div>\n        </div>\n      </section>\n\n      {/* Comparison Section */}\n      <section className=\"relative w-full\">\n        <div className=\"absolute w-[239px] h-[239px] top-0 lg:right-0 rounded-[119.5px] blur-[100px] bg-[linear-gradient(55deg,rgba(209,144,73,1)_30%,rgba(191,65,41,1)_100%)] opacity-20\"></div>\n        <div className=\"mx-auto max-w-[1082px] relative\">\n          <div className=\"absolute w-[381px] h-[381px] top-20 left-0 bg-[#d19049] rounded-[190.5px] blur-[159.41px] opacity-20\"></div>\n\n          <section className=\"flex flex-col w-full max-w-[880px] items-center gap-10 mx-auto my-10\">\n            <div className=\"flex flex-col items-center gap-10 w-full\">\n              <div className=\"flex flex-col w-full max-w-[517px] items-center gap-4\">\n                <h2 className=\"w-full [font-family:'Montserrat',Helvetica] font-semibold text-neutral-100 text-[40px] text-center\">What Makes Us Different</h2>\n                <p className=\"w-full [font-family:'Montserrat',Helvetica] font-normal text-neutral-300 text-base text-center\">ESVC Staking Versus Traditional Crypto</p>\n              </div>\n\n              <div className=\"flex w-full px-5 md:px-0\">\n                <div className=\"flex flex-col items-start gap-1 flex-1\">\n                  <div className=\"flex items-center justify-center gap-2.5 md:px-[76px] py-2.5 w-full bg-neutral-800 rounded-[16px_0px_0px_0px]\">\n                    <h3 className=\"[font-family:'Montserrat',Helvetica] font-semibold text-neutral-100 md:text-2xl text-center\">Traditional Crypto</h3>\n                  </div>\n                  <div className=\"md:flex items-center gap-3 px-5 py-2.5 w-full border border-solid border-transparent [border-image:linear-gradient(249deg,rgba(255,255,255,1)_0%,rgba(255,255,255,0)_54%,rgba(255,255,255,1)_100%)_1]\">\n                    <span className=\"[font-family:'Montserrat',Helvetica] font-normal text-neutral-300 text-[10px] md:text-xl text-center whitespace-nowrap\">Speculative Trading</span>\n                  </div>\n                  <div className=\"md:flex items-center gap-3 px-5 py-2.5 w-full border border-solid border-transparent [border-image:linear-gradient(249deg,rgba(255,255,255,1)_0%,rgba(255,255,255,0)_54%,rgba(255,255,255,1)_100%)_1]\">\n                    <span className=\"[font-family:'Montserrat',Helvetica] font-normal text-neutral-300 text-[10px] md:text-xl text-center whitespace-nowrap\">No revenue backing</span>\n                  </div>\n                  <div className=\"md:flex items-center gap-3 px-5 py-2.5 w-full border border-solid border-transparent [border-image:linear-gradient(249deg,rgba(255,255,255,1)_0%,rgba(255,255,255,0)_54%,rgba(255,255,255,1)_100%)_1]\">\n                    <span className=\"[font-family:'Montserrat',Helvetica] font-normal text-neutral-300 text-[10px] md:text-xl text-center whitespace-nowrap\">Opaque Tokenomics</span>\n                  </div>\n                  <div className=\"md:flex items-center gap-3 px-5 py-2.5 w-full rounded-bl-2xl border\">\n                    <span className=\"[font-family:'Montserrat',Helvetica] font-normal text-neutral-300 text-[10px] md:text-xl text-center whitespace-nowrap\">Low staking rewards</span>\n                  </div>\n                </div>\n\n                <div className=\"flex flex-col items-start gap-1 flex-1\">\n                  <div className=\"flex items-center justify-center gap-2.5 md:px-[76px] py-2.5 w-full rounded-[0px_16px_0px_0px] bg-[linear-gradient(38deg,rgba(209,144,73,1)_36%,rgba(191,65,41,1)_100%),linear-gradient(0deg,rgba(38,38,38,1)_0%,rgba(38,38,38,1)_100%)]\">\n                    <h3 className=\"[font-family:'Montserrat',Helvetica] font-semibold text-neutral-100 md:text-2xl text-center\">ESVC</h3>\n                  </div>\n                  <div className=\"md:flex items-center gap-3 px-5 py-2.5 w-full border border-solid border-transparent [border-image:linear-gradient(38deg,rgba(209,144,73,1)_36%,rgba(191,65,41,1)_100%)_1]\">\n                    <span className=\"[font-family:'Montserrat',Helvetica] font-normal text-neutral-300 text-[10px] md:text-xl text-center whitespace-nowrap\">Token-gated startup funding</span>\n                  </div>\n                  <div className=\"md:flex items-center gap-3 px-5 py-2.5 w-full border border-solid border-transparent [border-image:linear-gradient(38deg,rgba(209,144,73,1)_36%,rgba(191,65,41,1)_100%)_1]\">\n                    <span className=\"[font-family:'Montserrat',Helvetica] font-normal text-neutral-300 text-[10px] md:text-xl text-center whitespace-nowrap\">Backed by real assets</span>\n                  </div>\n                  <div className=\"md:flex items-center gap-3 px-5 py-2.5 w-full border border-solid border-transparent [border-image:linear-gradient(38deg,rgba(209,144,73,1)_36%,rgba(191,65,41,1)_100%)_1]\">\n                    <span className=\"[font-family:'Montserrat',Helvetica] font-normal text-neutral-300 text-[10px] md:text-xl text-center whitespace-nowrap\">Transparent treasury</span>\n                  </div>\n                  <div className=\"md:flex items-center gap-3 px-5 py-2.5 w-full rounded-br-2xl border border-[#D19049]\">\n                    <span className=\"[font-family:'Montserrat',Helvetica] font-normal text-neutral-300 text-[10px] md:text-xl text-center whitespace-nowrap\">High-yield ESVC staking</span>\n                  </div>\n                </div>\n              </div>\n            </div>\n\n            <div className=\"\">\n              <div className=\"md:flex items-center gap-6\">\n                <button className=\"justify-center whitespace-nowrap transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0 shadow h-14 w-full px-4 py-2.5 bg-[#bf4129] hover:bg-[#a83a25] rounded-[999px] [font-family:'Montserrat',Helvetica] font-semibold text-neutral-50 text-lg flex items-center gap-3 mb-5 md:mb-0\">\n                  Start Staking Now\n                  <img className=\"w-6 h-6\" alt=\"RocketIcon launch\" src=\"https://c.animaapp.com/mc62dpc6QTKBF1/img/rocket-launch-24dp-00000-fill0-wght300-grad0-opsz24-1.svg\"/>\n                </button>\n                <button className=\"inline-flex items-center justify-center gap-2 whitespace-nowrap transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0 bg-background shadow-sm hover:bg-accent hover:text-accent-foreground w-full h-14 px-4 py-2.5 rounded-[999px] border border-solid border-neutral-700 [font-family:'Montserrat',Helvetica] font-semibold text-neutral-100 text-lg\">\n                  See How it Works\n                </button>\n              </div>\n            </div>\n          </section>\n        </div>\n      </section>\n\n      {/* FAQ Section */}\n      <section className=\"relative w-full\">\n        <section className=\"min-h-screen flex flex-col items-center justify-center bg-dark p-4 sm:p-6 lg:p-8 relative w-full bg-[#1d1104] py-20 overflow-hidden\">\n          <div className=\"w-full max-w-2xl\">\n            <div className=\"absolute w-[234px] h-[234px] top-[141px] lg:left-1/2 -translate-x-1/2 bg-[#d19049] rounded-[117px] blur-[97.91px] opacity-20\"></div>\n            <div className=\"absolute w-[239px] h-[239px] top-0 lg:right-[-120px] rounded-[119.5px] blur-[100px] bg-[linear-gradient(55deg,rgba(209,144,73,1)_30%,rgba(191,65,41,1)_100%)] opacity-20\"></div>\n            <div className=\"absolute w-[79px] h-[79px] bottom-[80px] lg:right-[-40px] rounded-[39.5px] blur-[33.05px] bg-[linear-gradient(55deg,rgba(209,144,73,1)_30%,rgba(191,65,41,1)_100%)] opacity-20\"></div>\n            <div className=\"absolute w-[239px] h-[239px] bottom-[40px] lg:left-[-120px] rounded-[119.5px] blur-[100px] bg-[linear-gradient(55deg,rgba(209,144,73,1)_30%,rgba(191,65,41,1)_100%)] opacity-20\"></div>\n\n            <h2 className=\"text-center font-['Montserrat',Helvetica] font-semibold text-neutral-100 text-[40px] mb-12\">Frequently Asked Questions</h2>\n\n            <div className=\"space-y-4\">\n              {faqData.map((faq, index) => (\n                <div key={index} className=\"border border-neutral-700 rounded-lg overflow-hidden mb-4 bg-[#281705]\">\n                  <button\n                    className=\"w-full text-left p-2 flex justify-between items-center text-neutral-100\"\n                    aria-expanded={openAccordion === index}\n                    aria-controls={`accordion-content-${index}`}\n                    onClick={() => toggleAccordion(index)}\n                  >\n                    <span className=\"text-lg font-semibold pr-4 text-white\">\n                      {index + 1}. {faq.question}\n                    </span>\n                    <span className=\"flex-shrink-0 border rounded-lg p-1.5 border-neutral-700\">\n                      <svg\n                        xmlns=\"http://www.w3.org/2000/svg\"\n                        width=\"24\"\n                        height=\"24\"\n                        viewBox=\"0 0 24 24\"\n                        fill=\"none\"\n                        stroke=\"currentColor\"\n                        strokeWidth=\"2\"\n                        strokeLinecap=\"round\"\n                        strokeLinejoin=\"round\"\n                        className={`lucide lucide-plus w-6 h-6 text-neutral-700 transition-transform duration-200 ${openAccordion === index ? 'rotate-45' : ''}`}\n                        aria-hidden=\"true\"\n                      >\n                        <path d=\"M5 12h14\"></path>\n                        <path d=\"M12 5v14\"></path>\n                      </svg>\n                    </span>\n                  </button>\n                  <div\n                    id={`accordion-content-${index}`}\n                    role=\"region\"\n                    aria-labelledby={`accordion-button-${index}`}\n                    className={`grid transition-all duration-300 ease-in-out ${openAccordion === index ? 'grid-rows-[1fr] opacity-100' : 'grid-rows-[0fr] opacity-0'}`}\n                  >\n                    <div className=\"overflow-hidden\">\n                      <div className=\"p-5 pt-0 text-textMuted leading-relaxed text-[#d4d4d4]\">\n                        {faq.answer}\n                      </div>\n                    </div>\n                  </div>\n                </div>\n              ))}\n            </div>\n\n            <div className=\"flex flex-col sm:flex-row justify-center items-center gap-4 mt-10\">\n              <button className=\"flex items-center bg-[#bf4129] hover:bg-[#a83a25] text-white py-3 px-6 rounded-full font-semibold text-lg transition-colors duration-200 shadow-lg\">\n                Start Staking Now\n                <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\" className=\"lucide lucide-rocket ml-2 w-5 h-5\" aria-hidden=\"true\">\n                  <path d=\"M4.5 16.5c-1.5 1.26-2 5-2 5s3.74-.5 5-2c.71-.84.7-2.13-.09-2.91a2.18 2.18 0 0 0-2.91-.09z\"></path>\n                  <path d=\"m12 15-3-3a22 22 0 0 1 2-3.95A12.88 12.88 0 0 1 22 2c0 2.72-.78 7.5-6 11a22.35 22.35 0 0 1-4 2z\"></path>\n                  <path d=\"M9 12H4s.55-3.03 2-4c1.62-1.08 5 0 5 0\"></path>\n                  <path d=\"M12 15v5s3.03-.55 4-2c1.08-1.62 0-5 0-5\"></path>\n                </svg>\n              </button>\n              <button className=\"bg-transparent text-textLight border border-textLight py-3 px-6 rounded-full font-semibold text-lg hover:border-primary hover:text-primary transition-colors duration-200\">\n                See How It Works\n              </button>\n            </div>\n          </div>\n        </section>\n      </section>\n    </main>\n  );\n};\n\nexport default Index;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExC,MAAMC,KAAe,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC5B,MAAM,CAACC,aAAa,EAAEC,gBAAgB,CAAC,GAAGN,QAAQ,CAAgB,IAAI,CAAC;EAEvE,MAAMO,eAAe,GAAIC,KAAa,IAAK;IACzCF,gBAAgB,CAACD,aAAa,KAAKG,KAAK,GAAG,IAAI,GAAGA,KAAK,CAAC;EAC1D,CAAC;EAED,MAAMC,OAAO,GAAG,CACd;IACEC,QAAQ,EAAE,eAAe;IACzBC,MAAM,EAAE;EACV,CAAC,EACD;IACED,QAAQ,EAAE,sBAAsB;IAChCC,MAAM,EAAE;EACV,CAAC,EACD;IACED,QAAQ,EAAE,wCAAwC;IAClDC,MAAM,EAAE;EACV,CAAC,EACD;IACED,QAAQ,EAAE,0BAA0B;IACpCC,MAAM,EAAE;EACV,CAAC,EACD;IACED,QAAQ,EAAE,6BAA6B;IACvCC,MAAM,EAAE;EACV,CAAC,CACF;EAED,oBACET,OAAA;IAAMU,SAAS,EAAC,uBAAuB;IAAC,iBAAc,MAAM;IAAAC,QAAA,gBAE1DX,OAAA;MAASU,SAAS,EAAC,iBAAiB;MAAAC,QAAA,gBAClCX,OAAA;QAAKU,SAAS,EAAC;MAAsG;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eAC5Hf,OAAA;QAAKU,SAAS,EAAC;MAAgH;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eAEtIf,OAAA;QAASU,SAAS,EAAC,mFAAmF;QAAAC,QAAA,gBACpGX,OAAA;UAAKU,SAAS,EAAC,8DAA8D;UAAAC,QAAA,gBAC3EX,OAAA;YAAKU,SAAS,EAAC;UAAmE;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACzFf,OAAA;YAAKU,SAAS,EAAC,uHAAuH;YAAAC,QAAA,eACpIX,OAAA;cAAAW,QAAA,EAAI;YAAuB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7B,CAAC,eACNf,OAAA;YAAIU,SAAS,EAAC,0DAA0D;YAAAC,QAAA,gBACtEX,OAAA;cAAMU,SAAS,EAAC,gBAAgB;cAAAC,QAAA,EAAC;YAAI;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC5Cf,OAAA;cAAMU,SAAS,EAAC,kBAAkB;cAAAC,QAAA,EAAC;YAAc;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACxDf,OAAA;cAAMU,SAAS,EAAC,gBAAgB;cAAAC,QAAA,EAAC;YAAG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC3Cf,OAAA;cAAMU,SAAS,EAAC,kBAAkB;cAAAC,QAAA,GAAC,WAAS,eAAAX,OAAA;gBAAAY,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,qBAAiB;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvE,CAAC,eACLf,OAAA;YAAGU,SAAS,EAAC,8FAA8F;YAAAC,QAAA,GAAC,6CAC/D,eAAAX,OAAA;cAAAY,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,qGACiD,eAAAf,OAAA;cAAAY,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,yCAExG;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACJf,OAAA;YACEU,SAAS,EAAC,4GAA4G;YACtHM,GAAG,EAAC,iBAAiB;YACrBC,GAAG,EAAC;UAAwD;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7D,CAAC,eACFf,OAAA;YACEU,SAAS,EAAC,mGAAmG;YAC7GM,GAAG,EAAC,uBAAuB;YAC3BC,GAAG,EAAC;UAAsD;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3D,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eACNf,OAAA;UAAQU,SAAS,EAAC,kXAAkX;UAAAC,QAAA,gBAClYX,OAAA;YAAMU,SAAS,EAAC,qEAAqE;YAAAC,QAAA,EAAC;UAAiB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC9Gf,OAAA;YAAKU,SAAS,EAAC,SAAS;YAACM,GAAG,EAAC,eAAe;YAACC,GAAG,EAAC;UAAqG;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClJ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eAEVf,OAAA;QAAKU,SAAS,EAAC,mHAAmH;QAACM,GAAG,EAAC,4BAA4B;QAACC,GAAG,EAAC;MAA6F;QAAAL,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAC,CAAC,eACvQf,OAAA;QAAKU,SAAS,EAAC,iHAAiH;QAACM,GAAG,EAAC,+BAA+B;QAACC,GAAG,EAAC;MAA6F;QAAAL,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAC,CAAC,eACxQf,OAAA;QAAKU,SAAS,EAAC,4HAA4H;QAACM,GAAG,EAAC,uCAAuC;QAACC,GAAG,EAAC;MAA6F;QAAAL,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAC,CAAC,eAC3Rf,OAAA;QAAKU,SAAS,EAAC,0HAA0H;QAACM,GAAG,EAAC,uBAAuB;QAACC,GAAG,EAAC;MAAyF;QAAAL,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC9P,CAAC,eAGVf,OAAA;MAASU,SAAS,EAAC,4BAA4B;MAAAC,QAAA,eAC7CX,OAAA;QAAKU,SAAS,EAAC,2CAA2C;QAAAC,QAAA,gBACxDX,OAAA;UAAKU,SAAS,EAAC;QAA4L;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAElNf,OAAA;UAAKU,SAAS,EAAC,iDAAiD;UAAAC,QAAA,gBAC9DX,OAAA;YAAIU,SAAS,EAAC,6FAA6F;YAAAC,QAAA,EAAC;UAAkB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACnIf,OAAA;YAAGU,SAAS,EAAC,iHAAiH;YAAAC,QAAA,EAAC;UAE/H;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACJf,OAAA;YAAKU,SAAS,EAAC,4DAA4D;YAACM,GAAG,EAAC,QAAQ;YAACC,GAAG,EAAC;UAAwD;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpJ,CAAC,eAENf,OAAA;UAAKU,SAAS,EAAC,kCAAkC;UAAAC,QAAA,gBAC/CX,OAAA;YAAKU,SAAS,EAAC,mCAAmC;YAAAC,QAAA,gBAChDX,OAAA;cAAKU,SAAS,EAAC,6FAA6F;cAAAC,QAAA,eAC1GX,OAAA;gBAAKU,SAAS,EAAC,qCAAqC;gBAAAC,QAAA,gBAClDX,OAAA;kBAAKU,SAAS,EAAC,6CAA6C;kBAAAC,QAAA,eAC1DX,OAAA;oBAAGU,SAAS,EAAC,2EAA2E;oBAAAC,QAAA,EAAC;kBAAsB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChH,CAAC,eACNf,OAAA;kBAAGU,SAAS,EAAC,8FAA8F;kBAAAC,QAAA,EAAC;gBAAQ;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC,eACxHf,OAAA;kBAAKU,SAAS,EAAC,yBAAyB;kBAAAC,QAAA,gBACtCX,OAAA;oBAAKkB,KAAK,EAAC,4BAA4B;oBAACC,KAAK,EAAC,IAAI;oBAACC,MAAM,EAAC,IAAI;oBAACC,OAAO,EAAC,WAAW;oBAACC,IAAI,EAAC,MAAM;oBAACC,MAAM,EAAC,cAAc;oBAACC,WAAW,EAAC,GAAG;oBAACC,aAAa,EAAC,OAAO;oBAACC,cAAc,EAAC,OAAO;oBAAChB,SAAS,EAAC,kDAAkD;oBAAC,eAAY,MAAM;oBAAAC,QAAA,gBAC/PX,OAAA;sBAAM2B,CAAC,EAAC;oBAAW;sBAAAf,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC,eAC3Bf,OAAA;sBAAM2B,CAAC,EAAC;oBAAwB;sBAAAf,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACrC,CAAC,eACNf,OAAA;oBAAMU,SAAS,EAAC,yEAAyE;oBAAAC,QAAA,EAAC;kBAAY;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1G,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAENf,OAAA;cAAKU,SAAS,EAAC,6FAA6F;cAAAC,QAAA,eAC1GX,OAAA;gBAAKU,SAAS,EAAC,qCAAqC;gBAAAC,QAAA,gBAClDX,OAAA;kBAAKU,SAAS,EAAC,6CAA6C;kBAAAC,QAAA,eAC1DX,OAAA;oBAAGU,SAAS,EAAC,2EAA2E;oBAAAC,QAAA,EAAC;kBAAsB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChH,CAAC,eACNf,OAAA;kBAAGU,SAAS,EAAC,8FAA8F;kBAAAC,QAAA,EAAC;gBAAO;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC,eACvHf,OAAA;kBAAKU,SAAS,EAAC,yBAAyB;kBAAAC,QAAA,gBACtCX,OAAA;oBAAKkB,KAAK,EAAC,4BAA4B;oBAACC,KAAK,EAAC,IAAI;oBAACC,MAAM,EAAC,IAAI;oBAACC,OAAO,EAAC,WAAW;oBAACC,IAAI,EAAC,MAAM;oBAACC,MAAM,EAAC,cAAc;oBAACC,WAAW,EAAC,GAAG;oBAACC,aAAa,EAAC,OAAO;oBAACC,cAAc,EAAC,OAAO;oBAAChB,SAAS,EAAC,kDAAkD;oBAAC,eAAY,MAAM;oBAAAC,QAAA,gBAC/PX,OAAA;sBAAM2B,CAAC,EAAC;oBAAW;sBAAAf,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC,eAC3Bf,OAAA;sBAAM2B,CAAC,EAAC;oBAAwB;sBAAAf,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACrC,CAAC,eACNf,OAAA;oBAAMU,SAAS,EAAC,yEAAyE;oBAAAC,QAAA,EAAC;kBAAY;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1G,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAENf,OAAA;cAAKU,SAAS,EAAC,6FAA6F;cAAAC,QAAA,eAC1GX,OAAA;gBAAKU,SAAS,EAAC,qCAAqC;gBAAAC,QAAA,gBAClDX,OAAA;kBAAKU,SAAS,EAAC,6CAA6C;kBAAAC,QAAA,gBAC1DX,OAAA;oBAAGU,SAAS,EAAC,2EAA2E;oBAAAC,QAAA,EAAC;kBAAgC;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC,eAC7Hf,OAAA;oBAAKkB,KAAK,EAAC,4BAA4B;oBAACC,KAAK,EAAC,IAAI;oBAACC,MAAM,EAAC,IAAI;oBAACC,OAAO,EAAC,WAAW;oBAACC,IAAI,EAAC,MAAM;oBAACC,MAAM,EAAC,cAAc;oBAACC,WAAW,EAAC,GAAG;oBAACC,aAAa,EAAC,OAAO;oBAACC,cAAc,EAAC,OAAO;oBAAChB,SAAS,EAAC,6CAA6C;oBAAC,eAAY,MAAM;oBAAAC,QAAA,gBAC1PX,OAAA;sBAAQ4B,EAAE,EAAC,IAAI;sBAACC,EAAE,EAAC,IAAI;sBAACC,CAAC,EAAC;oBAAI;sBAAAlB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAS,CAAC,eACxCf,OAAA;sBAAM2B,CAAC,EAAC;oBAAW;sBAAAf,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC,eAC3Bf,OAAA;sBAAM2B,CAAC,EAAC;oBAAW;sBAAAf,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACxB,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACNf,OAAA;kBAAGU,SAAS,EAAC,8FAA8F;kBAAAC,QAAA,EAAC;gBAAM;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC,eACtHf,OAAA;kBAAKU,SAAS,EAAC,yBAAyB;kBAAAC,QAAA,gBACtCX,OAAA;oBAAKkB,KAAK,EAAC,4BAA4B;oBAACC,KAAK,EAAC,IAAI;oBAACC,MAAM,EAAC,IAAI;oBAACC,OAAO,EAAC,WAAW;oBAACC,IAAI,EAAC,MAAM;oBAACC,MAAM,EAAC,cAAc;oBAACC,WAAW,EAAC,GAAG;oBAACC,aAAa,EAAC,OAAO;oBAACC,cAAc,EAAC,OAAO;oBAAChB,SAAS,EAAC,kDAAkD;oBAAC,eAAY,MAAM;oBAAAC,QAAA,gBAC/PX,OAAA;sBAAM2B,CAAC,EAAC;oBAAW;sBAAAf,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC,eAC3Bf,OAAA;sBAAM2B,CAAC,EAAC;oBAAwB;sBAAAf,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACrC,CAAC,eACNf,OAAA;oBAAMU,SAAS,EAAC,yEAAyE;oBAAAC,QAAA,EAAC;kBAAY;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1G,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGNf,OAAA;YAAKU,SAAS,EAAC,wFAAwF;YAAAC,QAAA,eACrGX,OAAA;cAAKU,SAAS,EAAC,0CAA0C;cAAAC,QAAA,gBACvDX,OAAA;gBAAKU,SAAS,EAAC,wEAAwE;gBAAAC,QAAA,gBACrFX,OAAA;kBAAKU,SAAS,EAAC,gCAAgC;kBAAAC,QAAA,gBAC7CX,OAAA;oBAAKU,SAAS,EAAC,uBAAuB;oBAACM,GAAG,EAAC,cAAc;oBAACC,GAAG,EAAC;kBAAkE;oBAAAL,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAC,CAAC,eAClIf,OAAA;oBAAKU,SAAS,EAAC,wCAAwC;oBAAAC,QAAA,gBACrDX,OAAA;sBAAGU,SAAS,EAAC,wFAAwF;sBAAAC,QAAA,EAAC;oBAAY;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG,CAAC,eACtHf,OAAA;sBAAGU,SAAS,EAAC,0FAA0F;sBAAAC,QAAA,gBACrGX,OAAA;wBAAAW,QAAA,EAAM;sBAAQ;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,eACrBf,OAAA;wBAAMU,SAAS,EAAC,SAAS;wBAAAC,QAAA,EAAC;sBAAG;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACnC,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACD,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACNf,OAAA;kBAAKU,SAAS,EAAC,yBAAyB;kBAAAC,QAAA,gBACtCX,OAAA;oBAAKkB,KAAK,EAAC,4BAA4B;oBAACC,KAAK,EAAC,IAAI;oBAACC,MAAM,EAAC,IAAI;oBAACC,OAAO,EAAC,WAAW;oBAACC,IAAI,EAAC,MAAM;oBAACC,MAAM,EAAC,cAAc;oBAACC,WAAW,EAAC,GAAG;oBAACC,aAAa,EAAC,OAAO;oBAACC,cAAc,EAAC,OAAO;oBAAChB,SAAS,EAAC,kDAAkD;oBAAC,eAAY,MAAM;oBAAAC,QAAA,gBAC/PX,OAAA;sBAAM2B,CAAC,EAAC;oBAAW;sBAAAf,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC,eAC3Bf,OAAA;sBAAM2B,CAAC,EAAC;oBAAwB;sBAAAf,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACrC,CAAC,eACNf,OAAA;oBAAMU,SAAS,EAAC,yEAAyE;oBAAAC,QAAA,EAAC;kBAAY;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1G,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAENf,OAAA;gBAAAW,QAAA,gBACEX,OAAA;kBAAKU,SAAS,EAAC,gDAAgD;kBAACM,GAAG,EAAC,MAAM;kBAACC,GAAG,EAAC;gBAAsD;kBAAAL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAC,CAAC,eACvIf,OAAA;kBAAK,oBAAiB,YAAY;kBAAC+B,IAAI,EAAC,MAAM;kBAACrB,SAAS,EAAC;gBAAwC;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrG,CAAC,eAENf,OAAA;gBAAKU,SAAS,EAAC,wEAAwE;gBAAAC,QAAA,gBACrFX,OAAA;kBAAKU,SAAS,EAAC,gCAAgC;kBAAAC,QAAA,gBAC7CX,OAAA;oBAAKU,SAAS,EAAC,uBAAuB;oBAACM,GAAG,EAAC,iBAAiB;oBAACC,GAAG,EAAC;kBAAgE;oBAAAL,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAC,CAAC,eACnIf,OAAA;oBAAKU,SAAS,EAAC,wCAAwC;oBAAAC,QAAA,gBACrDX,OAAA;sBAAGU,SAAS,EAAC,wFAAwF;sBAAAC,QAAA,EAAC;oBAAe;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG,CAAC,eACzHf,OAAA;sBAAGU,SAAS,EAAC,0FAA0F;sBAAAC,QAAA,gBACrGX,OAAA;wBAAAW,QAAA,EAAM;sBAAS;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,eACtBf,OAAA;wBAAMU,SAAS,EAAC,SAAS;wBAAAC,QAAA,EAAC;sBAAG;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACnC,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACD,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACNf,OAAA;kBAAKU,SAAS,EAAC,yBAAyB;kBAAAC,QAAA,gBACtCX,OAAA;oBAAKkB,KAAK,EAAC,4BAA4B;oBAACC,KAAK,EAAC,IAAI;oBAACC,MAAM,EAAC,IAAI;oBAACC,OAAO,EAAC,WAAW;oBAACC,IAAI,EAAC,MAAM;oBAACC,MAAM,EAAC,cAAc;oBAACC,WAAW,EAAC,GAAG;oBAACC,aAAa,EAAC,OAAO;oBAACC,cAAc,EAAC,OAAO;oBAAChB,SAAS,EAAC,kDAAkD;oBAAC,eAAY,MAAM;oBAAAC,QAAA,gBAC/PX,OAAA;sBAAM2B,CAAC,EAAC;oBAAW;sBAAAf,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC,eAC3Bf,OAAA;sBAAM2B,CAAC,EAAC;oBAAwB;sBAAAf,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACrC,CAAC,eACNf,OAAA;oBAAMU,SAAS,EAAC,yEAAyE;oBAAAC,QAAA,EAAC;kBAAY;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1G,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAENf,OAAA;gBAAAW,QAAA,gBACEX,OAAA;kBAAKU,SAAS,EAAC,gDAAgD;kBAACM,GAAG,EAAC,MAAM;kBAACC,GAAG,EAAC;gBAAsD;kBAAAL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAC,CAAC,eACvIf,OAAA;kBAAK,oBAAiB,YAAY;kBAAC+B,IAAI,EAAC,MAAM;kBAACrB,SAAS,EAAC;gBAAwC;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrG,CAAC,eAENf,OAAA;gBAAKU,SAAS,EAAC,wEAAwE;gBAAAC,QAAA,gBACrFX,OAAA;kBAAKU,SAAS,EAAC,gCAAgC;kBAAAC,QAAA,gBAC7CX,OAAA;oBAAKU,SAAS,EAAC,uBAAuB;oBAACM,GAAG,EAAC,eAAe;oBAACC,GAAG,EAAC;kBAAqD;oBAAAL,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAC,CAAC,eACtHf,OAAA;oBAAKU,SAAS,EAAC,wCAAwC;oBAAAC,QAAA,gBACrDX,OAAA;sBAAGU,SAAS,EAAC,wFAAwF;sBAAAC,QAAA,EAAC;oBAAa;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG,CAAC,eACvHf,OAAA;sBAAGU,SAAS,EAAC,0FAA0F;sBAAAC,QAAA,gBACrGX,OAAA;wBAAAW,QAAA,EAAM;sBAAQ;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,eACrBf,OAAA;wBAAMU,SAAS,EAAC,SAAS;wBAAAC,QAAA,EAAC;sBAAI;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACpC,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACD,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACNf,OAAA;kBAAKU,SAAS,EAAC,yBAAyB;kBAAAC,QAAA,gBACtCX,OAAA;oBAAKU,SAAS,EAAC,SAAS;oBAACM,GAAG,EAAC,YAAY;oBAACC,GAAG,EAAC;kBAA4D;oBAAAL,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAC,CAAC,eAC5Gf,OAAA;oBAAMU,SAAS,EAAC,yEAAyE;oBAAAC,QAAA,EAAC;kBAAY;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1G,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAENf,OAAA;gBAAAW,QAAA,gBACEX,OAAA;kBAAKU,SAAS,EAAC,gDAAgD;kBAACM,GAAG,EAAC,MAAM;kBAACC,GAAG,EAAC;gBAAsD;kBAAAL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAC,CAAC,eACvIf,OAAA;kBAAK,oBAAiB,YAAY;kBAAC+B,IAAI,EAAC,MAAM;kBAACrB,SAAS,EAAC;gBAAwC;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrG,CAAC,eAENf,OAAA;gBAAKU,SAAS,EAAC,wEAAwE;gBAAAC,QAAA,gBACrFX,OAAA;kBAAKU,SAAS,EAAC,gCAAgC;kBAAAC,QAAA,gBAC7CX,OAAA;oBAAKU,SAAS,EAAC,uBAAuB;oBAACM,GAAG,EAAC,eAAe;oBAACC,GAAG,EAAC;kBAAuD;oBAAAL,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAC,CAAC,eACxHf,OAAA;oBAAKU,SAAS,EAAC,wCAAwC;oBAAAC,QAAA,gBACrDX,OAAA;sBAAGU,SAAS,EAAC,wFAAwF;sBAAAC,QAAA,EAAC;oBAAa;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG,CAAC,eACvHf,OAAA;sBAAGU,SAAS,EAAC,0FAA0F;sBAAAC,QAAA,gBACrGX,OAAA;wBAAAW,QAAA,EAAM;sBAAQ;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,eACrBf,OAAA;wBAAMU,SAAS,EAAC,SAAS;wBAAAC,QAAA,EAAC;sBAAI;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACpC,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACD,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACNf,OAAA;kBAAKU,SAAS,EAAC,yBAAyB;kBAAAC,QAAA,gBACtCX,OAAA;oBAAKU,SAAS,EAAC,SAAS;oBAACM,GAAG,EAAC,YAAY;oBAACC,GAAG,EAAC;kBAA4D;oBAAAL,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAC,CAAC,eAC5Gf,OAAA;oBAAMU,SAAS,EAAC,yEAAyE;oBAAAC,QAAA,EAAC;kBAAY;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1G,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENf,OAAA;UAAKU,SAAS,EAAC,oGAAoG;UAACM,GAAG,EAAC,SAAS;UAACC,GAAG,EAAC;QAA0D;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAC,CAAC,eAElMf,OAAA;UAAKU,SAAS,EAAC,yCAAyC;UAAAC,QAAA,eACtDX,OAAA;YAAKU,SAAS,EAAC,0DAA0D;YAAAC,QAAA,gBACvEX,OAAA;cAAQU,SAAS,EAAC,ubAAub;cAAAC,QAAA,GAAC,mBAExc,eAAAX,OAAA;gBAAKkB,KAAK,EAAC,4BAA4B;gBAACC,KAAK,EAAC,IAAI;gBAACC,MAAM,EAAC,IAAI;gBAACC,OAAO,EAAC,WAAW;gBAACC,IAAI,EAAC,MAAM;gBAACC,MAAM,EAAC,cAAc;gBAACC,WAAW,EAAC,GAAG;gBAACC,aAAa,EAAC,OAAO;gBAACC,cAAc,EAAC,OAAO;gBAAChB,SAAS,EAAC,mCAAmC;gBAAC,eAAY,MAAM;gBAAAC,QAAA,gBAChPX,OAAA;kBAAM2B,CAAC,EAAC;gBAA2F;kBAAAf,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC3Gf,OAAA;kBAAM2B,CAAC,EAAC;gBAAiG;kBAAAf,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACjHf,OAAA;kBAAM2B,CAAC,EAAC;gBAAwC;kBAAAf,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACxDf,OAAA;kBAAM2B,CAAC,EAAC;gBAAyC;kBAAAf,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC,eACTf,OAAA;cAAQU,SAAS,EAAC,ofAAof;cAAAC,QAAA,GAAC,qBAErgB,eAAAX,OAAA;gBAAKkB,KAAK,EAAC,4BAA4B;gBAACC,KAAK,EAAC,IAAI;gBAACC,MAAM,EAAC,IAAI;gBAACC,OAAO,EAAC,WAAW;gBAACC,IAAI,EAAC,MAAM;gBAACC,MAAM,EAAC,cAAc;gBAACC,WAAW,EAAC,GAAG;gBAACC,aAAa,EAAC,OAAO;gBAACC,cAAc,EAAC,OAAO;gBAAChB,SAAS,EAAC,wCAAwC;gBAAC,eAAY,MAAM;gBAAAC,QAAA,gBACrPX,OAAA;kBAAM2B,CAAC,EAAC;gBAAU;kBAAAf,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC1Bf,OAAA;kBAAM2B,CAAC,EAAC;gBAAe;kBAAAf,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5B,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGVf,OAAA;MAASU,SAAS,EAAC,iBAAiB;MAAAC,QAAA,gBAClCX,OAAA;QAAKU,SAAS,EAAC;MAAmK;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eACzLf,OAAA;QAAKU,SAAS,EAAC,iCAAiC;QAAAC,QAAA,gBAC9CX,OAAA;UAAKU,SAAS,EAAC;QAAsG;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAE5Hf,OAAA;UAASU,SAAS,EAAC,sEAAsE;UAAAC,QAAA,gBACvFX,OAAA;YAAKU,SAAS,EAAC,0CAA0C;YAAAC,QAAA,gBACvDX,OAAA;cAAKU,SAAS,EAAC,uDAAuD;cAAAC,QAAA,gBACpEX,OAAA;gBAAIU,SAAS,EAAC,oGAAoG;gBAAAC,QAAA,EAAC;cAAuB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC/If,OAAA;gBAAGU,SAAS,EAAC,gGAAgG;gBAAAC,QAAA,EAAC;cAAsC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrJ,CAAC,eAENf,OAAA;cAAKU,SAAS,EAAC,0BAA0B;cAAAC,QAAA,gBACvCX,OAAA;gBAAKU,SAAS,EAAC,wCAAwC;gBAAAC,QAAA,gBACrDX,OAAA;kBAAKU,SAAS,EAAC,+GAA+G;kBAAAC,QAAA,eAC5HX,OAAA;oBAAIU,SAAS,EAAC,6FAA6F;oBAAAC,QAAA,EAAC;kBAAkB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChI,CAAC,eACNf,OAAA;kBAAKU,SAAS,EAAC,uMAAuM;kBAAAC,QAAA,eACpNX,OAAA;oBAAMU,SAAS,EAAC,wHAAwH;oBAAAC,QAAA,EAAC;kBAAmB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChK,CAAC,eACNf,OAAA;kBAAKU,SAAS,EAAC,uMAAuM;kBAAAC,QAAA,eACpNX,OAAA;oBAAMU,SAAS,EAAC,wHAAwH;oBAAAC,QAAA,EAAC;kBAAkB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC/J,CAAC,eACNf,OAAA;kBAAKU,SAAS,EAAC,uMAAuM;kBAAAC,QAAA,eACpNX,OAAA;oBAAMU,SAAS,EAAC,wHAAwH;oBAAAC,QAAA,EAAC;kBAAiB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC9J,CAAC,eACNf,OAAA;kBAAKU,SAAS,EAAC,qEAAqE;kBAAAC,QAAA,eAClFX,OAAA;oBAAMU,SAAS,EAAC,wHAAwH;oBAAAC,QAAA,EAAC;kBAAmB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAENf,OAAA;gBAAKU,SAAS,EAAC,wCAAwC;gBAAAC,QAAA,gBACrDX,OAAA;kBAAKU,SAAS,EAAC,0OAA0O;kBAAAC,QAAA,eACvPX,OAAA;oBAAIU,SAAS,EAAC,6FAA6F;oBAAAC,QAAA,EAAC;kBAAI;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClH,CAAC,eACNf,OAAA;kBAAKU,SAAS,EAAC,4KAA4K;kBAAAC,QAAA,eACzLX,OAAA;oBAAMU,SAAS,EAAC,wHAAwH;oBAAAC,QAAA,EAAC;kBAA2B;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxK,CAAC,eACNf,OAAA;kBAAKU,SAAS,EAAC,4KAA4K;kBAAAC,QAAA,eACzLX,OAAA;oBAAMU,SAAS,EAAC,wHAAwH;oBAAAC,QAAA,EAAC;kBAAqB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClK,CAAC,eACNf,OAAA;kBAAKU,SAAS,EAAC,4KAA4K;kBAAAC,QAAA,eACzLX,OAAA;oBAAMU,SAAS,EAAC,wHAAwH;oBAAAC,QAAA,EAAC;kBAAoB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjK,CAAC,eACNf,OAAA;kBAAKU,SAAS,EAAC,sFAAsF;kBAAAC,QAAA,eACnGX,OAAA;oBAAMU,SAAS,EAAC,wHAAwH;oBAAAC,QAAA,EAAC;kBAAuB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENf,OAAA;YAAKU,SAAS,EAAC,EAAE;YAAAC,QAAA,eACfX,OAAA;cAAKU,SAAS,EAAC,4BAA4B;cAAAC,QAAA,gBACzCX,OAAA;gBAAQU,SAAS,EAAC,waAAwa;gBAAAC,QAAA,GAAC,mBAEzb,eAAAX,OAAA;kBAAKU,SAAS,EAAC,SAAS;kBAACM,GAAG,EAAC,mBAAmB;kBAACC,GAAG,EAAC;gBAAqG;kBAAAL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtJ,CAAC,eACTf,OAAA;gBAAQU,SAAS,EAAC,weAAwe;gBAAAC,QAAA,EAAC;cAE3f;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACP,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGVf,OAAA;MAASU,SAAS,EAAC,iBAAiB;MAAAC,QAAA,eAClCX,OAAA;QAASU,SAAS,EAAC,qIAAqI;QAAAC,QAAA,eACtJX,OAAA;UAAKU,SAAS,EAAC,kBAAkB;UAAAC,QAAA,gBAC/BX,OAAA;YAAKU,SAAS,EAAC;UAA8H;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACpJf,OAAA;YAAKU,SAAS,EAAC;UAA0K;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAChMf,OAAA;YAAKU,SAAS,EAAC;UAAgL;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACtMf,OAAA;YAAKU,SAAS,EAAC;UAAiL;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAEvMf,OAAA;YAAIU,SAAS,EAAC,4FAA4F;YAAAC,QAAA,EAAC;UAA0B;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAE1If,OAAA;YAAKU,SAAS,EAAC,WAAW;YAAAC,QAAA,EACvBJ,OAAO,CAACyB,GAAG,CAAC,CAACC,GAAG,EAAE3B,KAAK,kBACtBN,OAAA;cAAiBU,SAAS,EAAC,wEAAwE;cAAAC,QAAA,gBACjGX,OAAA;gBACEU,SAAS,EAAC,yEAAyE;gBACnF,iBAAeP,aAAa,KAAKG,KAAM;gBACvC,iBAAe,qBAAqBA,KAAK,EAAG;gBAC5C4B,OAAO,EAAEA,CAAA,KAAM7B,eAAe,CAACC,KAAK,CAAE;gBAAAK,QAAA,gBAEtCX,OAAA;kBAAMU,SAAS,EAAC,uCAAuC;kBAAAC,QAAA,GACpDL,KAAK,GAAG,CAAC,EAAC,IAAE,EAAC2B,GAAG,CAACzB,QAAQ;gBAAA;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtB,CAAC,eACPf,OAAA;kBAAMU,SAAS,EAAC,0DAA0D;kBAAAC,QAAA,eACxEX,OAAA;oBACEkB,KAAK,EAAC,4BAA4B;oBAClCC,KAAK,EAAC,IAAI;oBACVC,MAAM,EAAC,IAAI;oBACXC,OAAO,EAAC,WAAW;oBACnBC,IAAI,EAAC,MAAM;oBACXC,MAAM,EAAC,cAAc;oBACrBC,WAAW,EAAC,GAAG;oBACfC,aAAa,EAAC,OAAO;oBACrBC,cAAc,EAAC,OAAO;oBACtBhB,SAAS,EAAE,iFAAiFP,aAAa,KAAKG,KAAK,GAAG,WAAW,GAAG,EAAE,EAAG;oBACzI,eAAY,MAAM;oBAAAK,QAAA,gBAElBX,OAAA;sBAAM2B,CAAC,EAAC;oBAAU;sBAAAf,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC,eAC1Bf,OAAA;sBAAM2B,CAAC,EAAC;oBAAU;sBAAAf,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACvB;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CAAC,eACTf,OAAA;gBACEmC,EAAE,EAAE,qBAAqB7B,KAAK,EAAG;gBACjCyB,IAAI,EAAC,QAAQ;gBACb,mBAAiB,oBAAoBzB,KAAK,EAAG;gBAC7CI,SAAS,EAAE,gDAAgDP,aAAa,KAAKG,KAAK,GAAG,6BAA6B,GAAG,2BAA2B,EAAG;gBAAAK,QAAA,eAEnJX,OAAA;kBAAKU,SAAS,EAAC,iBAAiB;kBAAAC,QAAA,eAC9BX,OAAA;oBAAKU,SAAS,EAAC,wDAAwD;oBAAAC,QAAA,EACpEsB,GAAG,CAACxB;kBAAM;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACR;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA,GAxCET,KAAK;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAyCV,CACN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAENf,OAAA;YAAKU,SAAS,EAAC,mEAAmE;YAAAC,QAAA,gBAChFX,OAAA;cAAQU,SAAS,EAAC,oJAAoJ;cAAAC,QAAA,GAAC,mBAErK,eAAAX,OAAA;gBAAKkB,KAAK,EAAC,4BAA4B;gBAACC,KAAK,EAAC,IAAI;gBAACC,MAAM,EAAC,IAAI;gBAACC,OAAO,EAAC,WAAW;gBAACC,IAAI,EAAC,MAAM;gBAACC,MAAM,EAAC,cAAc;gBAACC,WAAW,EAAC,GAAG;gBAACC,aAAa,EAAC,OAAO;gBAACC,cAAc,EAAC,OAAO;gBAAChB,SAAS,EAAC,mCAAmC;gBAAC,eAAY,MAAM;gBAAAC,QAAA,gBAChPX,OAAA;kBAAM2B,CAAC,EAAC;gBAA2F;kBAAAf,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC3Gf,OAAA;kBAAM2B,CAAC,EAAC;gBAAiG;kBAAAf,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACjHf,OAAA;kBAAM2B,CAAC,EAAC;gBAAwC;kBAAAf,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACxDf,OAAA;kBAAM2B,CAAC,EAAC;gBAAyC;kBAAAf,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC,eACTf,OAAA;cAAQU,SAAS,EAAC,2KAA2K;cAAAC,QAAA,EAAC;YAE9L;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN,CAAC;AAEX,CAAC;AAACb,EAAA,CA1ZID,KAAe;AAAAmC,EAAA,GAAfnC,KAAe;AA4ZrB,eAAeA,KAAK;AAAC,IAAAmC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}