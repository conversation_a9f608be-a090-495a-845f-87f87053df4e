[{"C:\\My Web Sites\\esvc\\esvc-react\\src\\index.tsx": "1", "C:\\My Web Sites\\esvc\\esvc-react\\src\\reportWebVitals.ts": "2", "C:\\My Web Sites\\esvc\\esvc-react\\src\\App.tsx": "3", "C:\\My Web Sites\\esvc\\esvc-react\\src\\pages\\Index.tsx": "4", "C:\\My Web Sites\\esvc\\esvc-react\\src\\components\\Footer.tsx": "5", "C:\\My Web Sites\\esvc\\esvc-react\\src\\pages\\Overview.tsx": "6", "C:\\My Web Sites\\esvc\\esvc-react\\src\\components\\Header.tsx": "7", "C:\\My Web Sites\\esvc\\esvc-react\\src\\pages\\Login.tsx": "8", "C:\\My Web Sites\\esvc\\esvc-react\\src\\pages\\TradeChallenge.tsx": "9"}, {"size": 554, "mtime": 1754426282406, "results": "10", "hashOfConfig": "11"}, {"size": 425, "mtime": 1754425301441, "results": "12", "hashOfConfig": "11"}, {"size": 813, "mtime": 1754425705239, "results": "13", "hashOfConfig": "11"}, {"size": 32238, "mtime": 1754425945446, "results": "14", "hashOfConfig": "11"}, {"size": 10661, "mtime": 1754425691667, "results": "15", "hashOfConfig": "11"}, {"size": 984, "mtime": 1754425986235, "results": "16", "hashOfConfig": "11"}, {"size": 5614, "mtime": 1754425592073, "results": "17", "hashOfConfig": "11"}, {"size": 4596, "mtime": 1754425972498, "results": "18", "hashOfConfig": "11"}, {"size": 2265, "mtime": 1754426001613, "results": "19", "hashOfConfig": "11"}, {"filePath": "20", "messages": "21", "suppressedMessages": "22", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1vjx6fa", {"filePath": "23", "messages": "24", "suppressedMessages": "25", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "26", "messages": "27", "suppressedMessages": "28", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "29", "messages": "30", "suppressedMessages": "31", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "32", "messages": "33", "suppressedMessages": "34", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 9, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "35", "messages": "36", "suppressedMessages": "37", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "38", "messages": "39", "suppressedMessages": "40", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "41", "messages": "42", "suppressedMessages": "43", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "44", "messages": "45", "suppressedMessages": "46", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\My Web Sites\\esvc\\esvc-react\\src\\index.tsx", [], [], "C:\\My Web Sites\\esvc\\esvc-react\\src\\reportWebVitals.ts", [], [], "C:\\My Web Sites\\esvc\\esvc-react\\src\\App.tsx", [], [], "C:\\My Web Sites\\esvc\\esvc-react\\src\\pages\\Index.tsx", ["47", "48", "49", "50"], [], "C:\\My Web Sites\\esvc\\esvc-react\\src\\components\\Footer.tsx", ["51", "52", "53", "54", "55", "56", "57", "58", "59"], [], "C:\\My Web Sites\\esvc\\esvc-react\\src\\pages\\Overview.tsx", [], [], "C:\\My Web Sites\\esvc\\esvc-react\\src\\components\\Header.tsx", [], [], "C:\\My Web Sites\\esvc\\esvc-react\\src\\pages\\Login.tsx", [], [], "C:\\My Web Sites\\esvc\\esvc-react\\src\\pages\\TradeChallenge.tsx", [], [], {"ruleId": "60", "severity": 1, "message": "61", "line": 74, "column": 9, "nodeType": "62", "endLine": 74, "endColumn": 272}, {"ruleId": "60", "severity": 1, "message": "61", "line": 75, "column": 9, "nodeType": "62", "endLine": 75, "endColumn": 273}, {"ruleId": "60", "severity": 1, "message": "61", "line": 76, "column": 9, "nodeType": "62", "endLine": 76, "endColumn": 292}, {"ruleId": "60", "severity": 1, "message": "61", "line": 77, "column": 9, "nodeType": "62", "endLine": 77, "endColumn": 270}, {"ruleId": "63", "severity": 1, "message": "64", "line": 28, "column": 13, "nodeType": "62", "endLine": 28, "endColumn": 86}, {"ruleId": "63", "severity": 1, "message": "64", "line": 33, "column": 13, "nodeType": "62", "endLine": 33, "endColumn": 86}, {"ruleId": "63", "severity": 1, "message": "64", "line": 38, "column": 13, "nodeType": "62", "endLine": 38, "endColumn": 86}, {"ruleId": "63", "severity": 1, "message": "64", "line": 43, "column": 13, "nodeType": "62", "endLine": 43, "endColumn": 86}, {"ruleId": "63", "severity": 1, "message": "64", "line": 48, "column": 13, "nodeType": "62", "endLine": 48, "endColumn": 86}, {"ruleId": "63", "severity": 1, "message": "64", "line": 56, "column": 9, "nodeType": "62", "endLine": 56, "endColumn": 138}, {"ruleId": "63", "severity": 1, "message": "64", "line": 61, "column": 9, "nodeType": "62", "endLine": 61, "endColumn": 138}, {"ruleId": "63", "severity": 1, "message": "64", "line": 66, "column": 9, "nodeType": "62", "endLine": 66, "endColumn": 138}, {"ruleId": "63", "severity": 1, "message": "64", "line": 83, "column": 9, "nodeType": "62", "endLine": 83, "endColumn": 138}, "jsx-a11y/img-redundant-alt", "Redundant alt attribute. Screen-readers already announce `img` tags as an image. You don’t need to use the words `image`, `photo,` or `picture` (or any specified custom words) in the alt prop.", "JSXOpeningElement", "jsx-a11y/anchor-is-valid", "The href attribute requires a valid value to be accessible. Provide a valid, navigable address as the href value. If you cannot provide a valid href, but still need the element to resemble a link, use a button and change it with appropriate styles. Learn more: https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/blob/HEAD/docs/rules/anchor-is-valid.md"]