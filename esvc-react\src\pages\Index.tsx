import React, { useState } from 'react';

const Index: React.FC = () => {
  const [openAccordion, setOpenAccordion] = useState<number | null>(null);

  const toggleAccordion = (index: number) => {
    setOpenAccordion(openAccordion === index ? null : index);
  };

  const faqData = [
    {
      question: "What is ESVC?",
      answer: "ESVC is a decentralized staking protocol that allows users to earn rewards by staking their ESVC tokens. It leverages the Solana blockchain for high-speed and low-cost transactions."
    },
    {
      question: "How do I stake ESVC?",
      answer: "Simply click \"Start Staking Now\", create an account, choose how much you want to stake (in USD), and deposit using either Solana (SOL) or USDC. Your ESVC tokens will be automatically purchased and staked for a 6 or 12-month period."
    },
    {
      question: "What's the minimum amount I can stake?",
      answer: "The minimum staking amount is 50 USD to ensure efficient transaction processing and reward distribution within the protocol."
    },
    {
      question: "How much ROI can I earn?",
      answer: "Expected ROI varies based on staking period and current network conditions. Typically, annual returns range from 8% to 15%. Detailed projections are available after you log in."
    },
    {
      question: "When can I withdraw my ROI?",
      answer: "Your earned ROI can be withdrawn at the end of your chosen staking period (6 or 12 months). Partial withdrawals during the staking period are not allowed to maintain network stability."
    }
  ];

  return (
    <main className="w-full bg-neutral-900" data-model-id="1:21">
      {/* Hero Section */}
      <section className="relative w-full">
        <div className="absolute w-[239px] h-[239px] top-0 lg:right-0 bg-[#d19049] rounded-[119.5px] blur-[100px] opacity-30"></div>
        <div className="absolute w-[239px] h-[239px] top-[232px] lg:left-[22px] bg-[#cc6754] rounded-[119.5px] blur-[100px] opacity-30"></div>
        
        <section className="flex flex-col items-center gap-8 w-full max-w-4xl mx-auto mb-20 py-5 px-5 lg:px-0">
          <div className="flex flex-col items-center gap-4 w-full text-center relative">
            <div className="inline-flex items-center justify-center gap-2.5 px-4 rounded-full"></div>
            <div className="border border-neutral-700 rounded-full p-3 bg-gradient-to-r from-[#BF4129] to-[#D19049] bg-clip-text text-transparent">
              <h3>Earn 20% annual returns</h3>
            </div>
            <h1 className="font-['Montserrat'] font-bold md:text-[47px] text-[33px]">
              <span className="text-[#cc6754]">Grow</span>
              <span className="text-neutral-100"> Your Wealth. </span>
              <span className="text-[#d19049]">Get</span>
              <span className="text-neutral-100"> Funded. <br/> Stake and Earn .</span>
            </h1>
            <p className="max-w-[627px] font-['Montserrat',Helvetica] font-normal text-neutral-300 text-base leading-6">
              Stake your ESVC tokens and earn daily ROI. <br/>
              Unlock exclusive opportunities to pitch your startup ideas for funding/get funded trade capital. <br/>
              Earn from our Bitcoin/Crypto Treasury
            </p>
            <img 
              className="top-[220px] w-[200px] h-[13px] absolute md:top-[210px] md:w-[300px] right-1 md:right-1 md:-translate-x-1/2" 
              alt="Decorative line" 
              src="https://c.animaapp.com/mc62dpc6QTKBF1/img/vector-1.svg"
            />
            <img 
              className="absolute w-44 h-[50px] top-[179px] left-[20px] md:w-44 md:h-[75px] md:top-[150px] md:left-[220px]" 
              alt="Vector graphic circle" 
              src="https://c.animaapp.com/mc62dpc6QTKBF1/img/vector.svg"
            />
          </div>
          <button className="whitespace-nowrap text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0 text-primary-foreground shadow h-14 bg-[#bf4129] hover:bg-[#a83a25] rounded-full px-4 py-2.5 flex items-center justify-center gap-3">
            <span className="font-['Montserrat',Helvetica] font-semibold text-neutral-50 text-lg">Start Staking Now</span>
            <img className="w-6 h-6" alt="Rocket launch" src="https://c.animaapp.com/mc62dpc6QTKBF1/img/rocket-launch-24dp-00000-fill0-wght300-grad0-opsz24-1.svg"/>
          </button>
        </section>

        <img className="absolute w-[106px] top-[401px] right-[-10px] md:w-[296px] md:h-[296px] md:top-[187px] md:right-[5px] object-cover" alt="Decorative coin image gold" src="https://c.animaapp.com/mc62dpc6QTKBF1/img/whatsapp-image-2025-06-13-at-6-11-51-pm--2--1.png"/>
        <img className="w-[106px] h-[106px] top-[401px] left-0 md:w-[370px] md:h-[370px] md:top-[179px] md:left-0 absolute object-cover" alt="Decorative coin image red big" src="https://c.animaapp.com/mc62dpc6QTKBF1/img/whatsapp-image-2025-06-13-at-6-11-51-pm--1--2.png"/>
        <img className="w-[78px] h-[78px] top-[492px] right-[20px] md:w-[156px] md:h-[156px] md:top-[352px] md:right-[256px] absolute object-cover" alt="Decorative coin image red small right" src="https://c.animaapp.com/mc62dpc6QTKBF1/img/whatsapp-image-2025-06-13-at-6-11-51-pm--1--2.png"/>
        <img className="absolute w-[94px] h-[94px] top-[492px] left-[10px] md:w-[188px] md:h-[188px] md:top-[352px] md:left-[276px] object-cover" alt="Decorative coin image" src="https://c.animaapp.com/mc62dpc6QTKBF1/img/whatsapp-image-2025-06-13-at-6-11-51-pm-1.png"/>
      </section>

      {/* Treasury Dashboard Section */}
      <section className="w-full p-10 bg-neutral-800">
        <div className="container max-w-[1200px] mx-auto relative">
          <div className="absolute w-[239px] h-[239px] top-[73px] lg:left-[41px] md:left-[481px] rounded-[119.5px] blur-[100px] bg-[linear-gradient(55deg,rgba(209,144,73,1)_30%,rgba(191,65,41,1)_100%)] opacity-20"></div>

          <div className="flex flex-col items-center gap-4 mb-10 relative">
            <h2 className="text-[40px] font-semibold text-neutral-100 text-center [font-family:'Montserrat',Helvetica]">Treasury Dashboard</h2>
            <p className="max-w-[670px] font-normal text-neutral-300 text-base text-center leading-6 [font-family:'Montserrat',Helvetica]">
              Live insights into how funds are held, ROI is distributed, and startups are supported. Empowering you to stake with trust.
            </p>
            <img className="hidden absolute w-[154px] h-[18px] top-[43px] left-[407px]" alt="Vector" src="https://c.animaapp.com/mc62dpc6QTKBF1/img/vector-2.svg"/>
          </div>

          <div className="md:flex md:flex-col w-full gap-6">
            <div className="md:flex items-center gap-6 w-full">
              <div className="rounded-xl border text-card-foreground shadow flex-1 bg-transparent border-neutral-700 mb-4">
                <div className="flex flex-col items-start gap-4 p-4">
                  <div className="flex items-center gap-2 w-full mt-[-1.00px]">
                    <p className="font-medium text-neutral-300 text-sm [font-family:'Montserrat',Helvetica]">Total Capital Invested</p>
                  </div>
                  <p className="self-stretch font-semibold text-neutral-100 text-[28px] [font-family:'Montserrat',Helvetica]">$266,500</p>
                  <div className="flex items-center gap-2">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="lucide lucide-trending-up w-4 h-4 text-[#7cca8d]" aria-hidden="true">
                      <path d="M16 7h6v6"></path>
                      <path d="m22 7-8.5 8.5-5-5L2 17"></path>
                    </svg>
                    <span className="font-normal text-xs [font-family:'Montserrat',Helvetica] text-[#7cca8d]">+ 4.8% Today</span>
                  </div>
                </div>
              </div>

              <div className="rounded-xl border text-card-foreground shadow flex-1 bg-transparent border-neutral-700 mb-4">
                <div className="flex flex-col items-start gap-4 p-4">
                  <div className="flex items-center gap-2 w-full mt-[-1.00px]">
                    <p className="font-medium text-neutral-300 text-sm [font-family:'Montserrat',Helvetica]">Total Profit Generated</p>
                  </div>
                  <p className="self-stretch font-semibold text-neutral-100 text-[28px] [font-family:'Montserrat',Helvetica]">$43,700</p>
                  <div className="flex items-center gap-2">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="lucide lucide-trending-up w-4 h-4 text-[#7cca8d]" aria-hidden="true">
                      <path d="M16 7h6v6"></path>
                      <path d="m22 7-8.5 8.5-5-5L2 17"></path>
                    </svg>
                    <span className="font-normal text-xs [font-family:'Montserrat',Helvetica] text-[#7cca8d]">+ 4.8% Today</span>
                  </div>
                </div>
              </div>

              <div className="rounded-xl border text-card-foreground shadow flex-1 bg-transparent border-neutral-700 mb-4">
                <div className="flex flex-col items-start gap-4 p-4">
                  <div className="flex items-center gap-2 w-full mt-[-1.00px]">
                    <p className="font-medium text-neutral-300 text-sm [font-family:'Montserrat',Helvetica]">Funds Available to Fund Startups</p>
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="lucide lucide-info w-5 h-5 text-neutral-300" aria-hidden="true">
                      <circle cx="12" cy="12" r="10"></circle>
                      <path d="M12 16v-4"></path>
                      <path d="M12 8h.01"></path>
                    </svg>
                  </div>
                  <p className="self-stretch font-semibold text-neutral-100 text-[28px] [font-family:'Montserrat',Helvetica]">$2,185</p>
                  <div className="flex items-center gap-2">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="lucide lucide-trending-up w-4 h-4 text-[#7cca8d]" aria-hidden="true">
                      <path d="M16 7h6v6"></path>
                      <path d="m22 7-8.5 8.5-5-5L2 17"></path>
                    </svg>
                    <span className="font-normal text-xs [font-family:'Montserrat',Helvetica] text-[#7cca8d]">+ 4.8% Today</span>
                  </div>
                </div>
              </div>
            </div>

            {/* Crypto Holdings Section */}
            <div className="rounded-xl border text-card-foreground shadow w-full bg-transparent border-neutral-700">
              <div className="p-6 pt-0 md:flex md:items-start md:gap-4">
                <div className="flex-1 flex flex-col items-start gap-3 p-4 rounded-2xl overflow-hidden">
                  <div className="flex items-center gap-3 w-full">
                    <img className="w-11 h-11 rounded-4xl" alt="BTC HOLDINGS" src="https://c.animaapp.com/mc62dpc6QTKBF1/img/bitcoin-symbol-png.png"/>
                    <div className="flex flex-col items-start gap-3 flex-1">
                      <p className="self-stretch font-medium text-neutral-300 text-sm [font-family:'Montserrat',Helvetica]">BTC HOLDINGS</p>
                      <p className="self-stretch font-semibold text-neutral-100 text-xl [font-family:'Montserrat',Helvetica]">
                        <span>$91,000 </span>
                        <span className="text-sm">BTC</span>
                      </p>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="lucide lucide-trending-up w-4 h-4 text-[#7cca8d]" aria-hidden="true">
                      <path d="M16 7h6v6"></path>
                      <path d="m22 7-8.5 8.5-5-5L2 17"></path>
                    </svg>
                    <span className="font-normal text-xs [font-family:'Montserrat',Helvetica] text-[#7cca8d]">+ 4.8% Today</span>
                  </div>
                </div>

                <div>
                  <img className="self-stretch object-cover py-2 hidden md:block" alt="Line" src="https://c.animaapp.com/mc62dpc6QTKBF1/img/line-1.svg"/>
                  <div data-orientation="horizontal" role="none" className="shrink-0 h-[1px] w-full bg-neutral-700"></div>
                </div>

                <div className="flex-1 flex flex-col items-start gap-3 p-4 rounded-2xl overflow-hidden">
                  <div className="flex items-center gap-3 w-full">
                    <img className="w-11 h-11 rounded-4xl" alt="SOLANA HOLDINGS" src="https://c.animaapp.com/mc62dpc6QTKBF1/img/solana-icon-jpeg.png"/>
                    <div className="flex flex-col items-start gap-3 flex-1">
                      <p className="self-stretch font-medium text-neutral-300 text-sm [font-family:'Montserrat',Helvetica]">SOLANA HOLDINGS</p>
                      <p className="self-stretch font-semibold text-neutral-100 text-xl [font-family:'Montserrat',Helvetica]">
                        <span>$124,000 </span>
                        <span className="text-sm">SOL</span>
                      </p>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="lucide lucide-trending-up w-4 h-4 text-[#7cca8d]" aria-hidden="true">
                      <path d="M16 7h6v6"></path>
                      <path d="m22 7-8.5 8.5-5-5L2 17"></path>
                    </svg>
                    <span className="font-normal text-xs [font-family:'Montserrat',Helvetica] text-[#7cca8d]">+ 4.8% Today</span>
                  </div>
                </div>

                <div>
                  <img className="self-stretch object-cover py-2 hidden md:block" alt="Line" src="https://c.animaapp.com/mc62dpc6QTKBF1/img/line-1.svg"/>
                  <div data-orientation="horizontal" role="none" className="shrink-0 h-[1px] w-full bg-neutral-700"></div>
                </div>

                <div className="flex-1 flex flex-col items-start gap-3 p-4 rounded-2xl overflow-hidden">
                  <div className="flex items-center gap-3 w-full">
                    <img className="w-11 h-11 rounded-4xl" alt="USDC HOLDINGS" src="https://c.animaapp.com/mc62dpc6QTKBF1/img/image.png"/>
                    <div className="flex flex-col items-start gap-3 flex-1">
                      <p className="self-stretch font-medium text-neutral-300 text-sm [font-family:'Montserrat',Helvetica]">USDC HOLDINGS</p>
                      <p className="self-stretch font-semibold text-neutral-100 text-xl [font-family:'Montserrat',Helvetica]">
                        <span>$51,500 </span>
                        <span className="text-sm">USDC</span>
                      </p>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    <img className="w-4 h-4" alt="Trend icon" src="https://c.animaapp.com/mc62dpc6QTKBF1/img/trend-icon-1.svg"/>
                    <span className="font-normal text-xs [font-family:'Montserrat',Helvetica] text-[#f58f8f]">- 0.9% Today</span>
                  </div>
                </div>

                <div>
                  <img className="self-stretch object-cover py-2 hidden md:block" alt="Line" src="https://c.animaapp.com/mc62dpc6QTKBF1/img/line-1.svg"/>
                  <div data-orientation="horizontal" role="none" className="shrink-0 h-[1px] w-full bg-neutral-700"></div>
                </div>

                <div className="flex-1 flex flex-col items-start gap-3 p-4 rounded-2xl overflow-hidden">
                  <div className="flex items-center gap-3 w-full">
                    <img className="w-11 h-11 rounded-4xl" alt="ESVC RESERVES" src="https://c.animaapp.com/mc62dpc6QTKBF1/img/image-1.png"/>
                    <div className="flex flex-col items-start gap-3 flex-1">
                      <p className="self-stretch font-medium text-neutral-300 text-sm [font-family:'Montserrat',Helvetica]">ESVC RESERVES</p>
                      <p className="self-stretch font-semibold text-neutral-100 text-xl [font-family:'Montserrat',Helvetica]">
                        <span>$51,500 </span>
                        <span className="text-sm">ESVC</span>
                      </p>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    <img className="w-4 h-4" alt="Trend icon" src="https://c.animaapp.com/mc62dpc6QTKBF1/img/trend-icon-1.svg"/>
                    <span className="font-normal text-xs [font-family:'Montserrat',Helvetica] text-[#f58f8f]">- 0.9% Today</span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <img className="absolute w-[39px] md:w-[78px] md:h-[52px] bottom-17 right-[-20px] md:bottom-[0px] md:right-[250px]" alt="Element" src="https://c.animaapp.com/mc62dpc6QTKBF1/img/element-09.svg"/>

          <div className="flex justify-center items-center w-full">
            <div className="md:flex md:items-center md:justify-center md:gap-6 mt-10">
              <button className="inline-flex items-center justify-center gap-2 whitespace-nowrap transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0 text-primary-foreground shadow mb-5 md:mb-0 h-14 w-full px-4 py-2.5 bg-[#bf4129] hover:bg-[#a83a25] rounded-[999px] text-lg font-semibold [font-family:'Montserrat',Helvetica]">
                Start Staking Now
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="lucide lucide-rocket w-6 h-6 ml-3" aria-hidden="true">
                  <path d="M4.5 16.5c-1.5 1.26-2 5-2 5s3.74-.5 5-2c.71-.84.7-2.13-.09-2.91a2.18 2.18 0 0 0-2.91-.09z"></path>
                  <path d="m12 15-3-3a22 22 0 0 1 2-3.95A12.88 12.88 0 0 1 22 2c0 2.72-.78 7.5-6 11a22.35 22.35 0 0 1-4 2z"></path>
                  <path d="M9 12H4s.55-3.03 2-4c1.62-1.08 5 0 5 0"></path>
                  <path d="M12 15v5s3.03-.55 4-2c1.08-1.62 0-5 0-5"></path>
                </svg>
              </button>
              <button className="inline-flex items-center justify-center gap-2 whitespace-nowrap transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0 border bg-background shadow-sm hover:bg-accent hover:text-accent-foreground h-14 w-full px-4 py-2.5 rounded-[999px] border-neutral-700 text-neutral-100 text-lg font-semibold [font-family:'Montserrat',Helvetica] hover:border-neutral-500">
                View Full Breakdown
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="lucide lucide-arrow-right w-6 h-6 ml-3" aria-hidden="true">
                  <path d="M5 12h14"></path>
                  <path d="m12 5 7 7-7 7"></path>
                </svg>
              </button>
            </div>
          </div>
        </div>
      </section>

      {/* Comparison Section */}
      <section className="relative w-full">
        <div className="absolute w-[239px] h-[239px] top-0 lg:right-0 rounded-[119.5px] blur-[100px] bg-[linear-gradient(55deg,rgba(209,144,73,1)_30%,rgba(191,65,41,1)_100%)] opacity-20"></div>
        <div className="mx-auto max-w-[1082px] relative">
          <div className="absolute w-[381px] h-[381px] top-20 left-0 bg-[#d19049] rounded-[190.5px] blur-[159.41px] opacity-20"></div>

          <section className="flex flex-col w-full max-w-[880px] items-center gap-10 mx-auto my-10">
            <div className="flex flex-col items-center gap-10 w-full">
              <div className="flex flex-col w-full max-w-[517px] items-center gap-4">
                <h2 className="w-full [font-family:'Montserrat',Helvetica] font-semibold text-neutral-100 text-[40px] text-center">What Makes Us Different</h2>
                <p className="w-full [font-family:'Montserrat',Helvetica] font-normal text-neutral-300 text-base text-center">ESVC Staking Versus Traditional Crypto</p>
              </div>

              <div className="flex w-full px-5 md:px-0">
                <div className="flex flex-col items-start gap-1 flex-1">
                  <div className="flex items-center justify-center gap-2.5 md:px-[76px] py-2.5 w-full bg-neutral-800 rounded-[16px_0px_0px_0px]">
                    <h3 className="[font-family:'Montserrat',Helvetica] font-semibold text-neutral-100 md:text-2xl text-center">Traditional Crypto</h3>
                  </div>
                  <div className="md:flex items-center gap-3 px-5 py-2.5 w-full border border-solid border-transparent [border-image:linear-gradient(249deg,rgba(255,255,255,1)_0%,rgba(255,255,255,0)_54%,rgba(255,255,255,1)_100%)_1]">
                    <span className="[font-family:'Montserrat',Helvetica] font-normal text-neutral-300 text-[10px] md:text-xl text-center whitespace-nowrap">Speculative Trading</span>
                  </div>
                  <div className="md:flex items-center gap-3 px-5 py-2.5 w-full border border-solid border-transparent [border-image:linear-gradient(249deg,rgba(255,255,255,1)_0%,rgba(255,255,255,0)_54%,rgba(255,255,255,1)_100%)_1]">
                    <span className="[font-family:'Montserrat',Helvetica] font-normal text-neutral-300 text-[10px] md:text-xl text-center whitespace-nowrap">No revenue backing</span>
                  </div>
                  <div className="md:flex items-center gap-3 px-5 py-2.5 w-full border border-solid border-transparent [border-image:linear-gradient(249deg,rgba(255,255,255,1)_0%,rgba(255,255,255,0)_54%,rgba(255,255,255,1)_100%)_1]">
                    <span className="[font-family:'Montserrat',Helvetica] font-normal text-neutral-300 text-[10px] md:text-xl text-center whitespace-nowrap">Opaque Tokenomics</span>
                  </div>
                  <div className="md:flex items-center gap-3 px-5 py-2.5 w-full rounded-bl-2xl border">
                    <span className="[font-family:'Montserrat',Helvetica] font-normal text-neutral-300 text-[10px] md:text-xl text-center whitespace-nowrap">Low staking rewards</span>
                  </div>
                </div>

                <div className="flex flex-col items-start gap-1 flex-1">
                  <div className="flex items-center justify-center gap-2.5 md:px-[76px] py-2.5 w-full rounded-[0px_16px_0px_0px] bg-[linear-gradient(38deg,rgba(209,144,73,1)_36%,rgba(191,65,41,1)_100%),linear-gradient(0deg,rgba(38,38,38,1)_0%,rgba(38,38,38,1)_100%)]">
                    <h3 className="[font-family:'Montserrat',Helvetica] font-semibold text-neutral-100 md:text-2xl text-center">ESVC</h3>
                  </div>
                  <div className="md:flex items-center gap-3 px-5 py-2.5 w-full border border-solid border-transparent [border-image:linear-gradient(38deg,rgba(209,144,73,1)_36%,rgba(191,65,41,1)_100%)_1]">
                    <span className="[font-family:'Montserrat',Helvetica] font-normal text-neutral-300 text-[10px] md:text-xl text-center whitespace-nowrap">Token-gated startup funding</span>
                  </div>
                  <div className="md:flex items-center gap-3 px-5 py-2.5 w-full border border-solid border-transparent [border-image:linear-gradient(38deg,rgba(209,144,73,1)_36%,rgba(191,65,41,1)_100%)_1]">
                    <span className="[font-family:'Montserrat',Helvetica] font-normal text-neutral-300 text-[10px] md:text-xl text-center whitespace-nowrap">Backed by real assets</span>
                  </div>
                  <div className="md:flex items-center gap-3 px-5 py-2.5 w-full border border-solid border-transparent [border-image:linear-gradient(38deg,rgba(209,144,73,1)_36%,rgba(191,65,41,1)_100%)_1]">
                    <span className="[font-family:'Montserrat',Helvetica] font-normal text-neutral-300 text-[10px] md:text-xl text-center whitespace-nowrap">Transparent treasury</span>
                  </div>
                  <div className="md:flex items-center gap-3 px-5 py-2.5 w-full rounded-br-2xl border border-[#D19049]">
                    <span className="[font-family:'Montserrat',Helvetica] font-normal text-neutral-300 text-[10px] md:text-xl text-center whitespace-nowrap">High-yield ESVC staking</span>
                  </div>
                </div>
              </div>
            </div>

            <div className="">
              <div className="md:flex items-center gap-6">
                <button className="justify-center whitespace-nowrap transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0 shadow h-14 w-full px-4 py-2.5 bg-[#bf4129] hover:bg-[#a83a25] rounded-[999px] [font-family:'Montserrat',Helvetica] font-semibold text-neutral-50 text-lg flex items-center gap-3 mb-5 md:mb-0">
                  Start Staking Now
                  <img className="w-6 h-6" alt="RocketIcon launch" src="https://c.animaapp.com/mc62dpc6QTKBF1/img/rocket-launch-24dp-00000-fill0-wght300-grad0-opsz24-1.svg"/>
                </button>
                <button className="inline-flex items-center justify-center gap-2 whitespace-nowrap transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0 bg-background shadow-sm hover:bg-accent hover:text-accent-foreground w-full h-14 px-4 py-2.5 rounded-[999px] border border-solid border-neutral-700 [font-family:'Montserrat',Helvetica] font-semibold text-neutral-100 text-lg">
                  See How it Works
                </button>
              </div>
            </div>
          </section>
        </div>
      </section>

      {/* FAQ Section */}
      <section className="relative w-full">
        <section className="min-h-screen flex flex-col items-center justify-center bg-dark p-4 sm:p-6 lg:p-8 relative w-full bg-[#1d1104] py-20 overflow-hidden">
          <div className="w-full max-w-2xl">
            <div className="absolute w-[234px] h-[234px] top-[141px] lg:left-1/2 -translate-x-1/2 bg-[#d19049] rounded-[117px] blur-[97.91px] opacity-20"></div>
            <div className="absolute w-[239px] h-[239px] top-0 lg:right-[-120px] rounded-[119.5px] blur-[100px] bg-[linear-gradient(55deg,rgba(209,144,73,1)_30%,rgba(191,65,41,1)_100%)] opacity-20"></div>
            <div className="absolute w-[79px] h-[79px] bottom-[80px] lg:right-[-40px] rounded-[39.5px] blur-[33.05px] bg-[linear-gradient(55deg,rgba(209,144,73,1)_30%,rgba(191,65,41,1)_100%)] opacity-20"></div>
            <div className="absolute w-[239px] h-[239px] bottom-[40px] lg:left-[-120px] rounded-[119.5px] blur-[100px] bg-[linear-gradient(55deg,rgba(209,144,73,1)_30%,rgba(191,65,41,1)_100%)] opacity-20"></div>

            <h2 className="text-center font-['Montserrat',Helvetica] font-semibold text-neutral-100 text-[40px] mb-12">Frequently Asked Questions</h2>

            <div className="space-y-4">
              {faqData.map((faq, index) => (
                <div key={index} className="border border-neutral-700 rounded-lg overflow-hidden mb-4 bg-[#281705]">
                  <button
                    className="w-full text-left p-2 flex justify-between items-center text-neutral-100"
                    aria-expanded={openAccordion === index}
                    aria-controls={`accordion-content-${index}`}
                    onClick={() => toggleAccordion(index)}
                  >
                    <span className="text-lg font-semibold pr-4 text-white">
                      {index + 1}. {faq.question}
                    </span>
                    <span className="flex-shrink-0 border rounded-lg p-1.5 border-neutral-700">
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        width="24"
                        height="24"
                        viewBox="0 0 24 24"
                        fill="none"
                        stroke="currentColor"
                        strokeWidth="2"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        className={`lucide lucide-plus w-6 h-6 text-neutral-700 transition-transform duration-200 ${openAccordion === index ? 'rotate-45' : ''}`}
                        aria-hidden="true"
                      >
                        <path d="M5 12h14"></path>
                        <path d="M12 5v14"></path>
                      </svg>
                    </span>
                  </button>
                  <div
                    id={`accordion-content-${index}`}
                    role="region"
                    aria-labelledby={`accordion-button-${index}`}
                    className={`grid transition-all duration-300 ease-in-out ${openAccordion === index ? 'grid-rows-[1fr] opacity-100' : 'grid-rows-[0fr] opacity-0'}`}
                  >
                    <div className="overflow-hidden">
                      <div className="p-5 pt-0 text-textMuted leading-relaxed text-[#d4d4d4]">
                        {faq.answer}
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>

            <div className="flex flex-col sm:flex-row justify-center items-center gap-4 mt-10">
              <button className="flex items-center bg-[#bf4129] hover:bg-[#a83a25] text-white py-3 px-6 rounded-full font-semibold text-lg transition-colors duration-200 shadow-lg">
                Start Staking Now
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="lucide lucide-rocket ml-2 w-5 h-5" aria-hidden="true">
                  <path d="M4.5 16.5c-1.5 1.26-2 5-2 5s3.74-.5 5-2c.71-.84.7-2.13-.09-2.91a2.18 2.18 0 0 0-2.91-.09z"></path>
                  <path d="m12 15-3-3a22 22 0 0 1 2-3.95A12.88 12.88 0 0 1 22 2c0 2.72-.78 7.5-6 11a22.35 22.35 0 0 1-4 2z"></path>
                  <path d="M9 12H4s.55-3.03 2-4c1.62-1.08 5 0 5 0"></path>
                  <path d="M12 15v5s3.03-.55 4-2c1.08-1.62 0-5 0-5"></path>
                </svg>
              </button>
              <button className="bg-transparent text-textLight border border-textLight py-3 px-6 rounded-full font-semibold text-lg hover:border-primary hover:text-primary transition-colors duration-200">
                See How It Works
              </button>
            </div>
          </div>
        </section>
      </section>
    </main>
  );
};

export default Index;
