{"ast": null, "code": "var _jsxFileName = \"C:\\\\My Web Sites\\\\esvc\\\\esvc-react\\\\src\\\\pages\\\\Overview.tsx\";\nimport React from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Overview = () => {\n  return /*#__PURE__*/_jsxDEV(\"main\", {\n    className: \"w-full bg-neutral-900 min-h-screen\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"container mx-auto px-4 py-20\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"text-4xl font-bold text-white mb-8\",\n          children: \"Overview\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 8,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-neutral-300 text-lg mb-8\",\n          children: \"This page is currently under development. Please check back soon for detailed overview information.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 9,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-neutral-800 rounded-lg p-8 max-w-2xl mx-auto\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-2xl font-semibold text-white mb-4\",\n            children: \"Coming Soon\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 13,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-neutral-300\",\n            children: \"The overview page will provide comprehensive insights into your ESVC staking portfolio, including performance metrics, earnings history, and detailed analytics.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 14,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 12,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 7,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 6,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 5,\n    columnNumber: 5\n  }, this);\n};\n_c = Overview;\nexport default Overview;\nvar _c;\n$RefreshReg$(_c, \"Overview\");", "map": {"version": 3, "names": ["React", "jsxDEV", "_jsxDEV", "Overview", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/My Web Sites/esvc/esvc-react/src/pages/Overview.tsx"], "sourcesContent": ["import React from 'react';\n\nconst Overview: React.FC = () => {\n  return (\n    <main className=\"w-full bg-neutral-900 min-h-screen\">\n      <div className=\"container mx-auto px-4 py-20\">\n        <div className=\"text-center\">\n          <h1 className=\"text-4xl font-bold text-white mb-8\">Overview</h1>\n          <p className=\"text-neutral-300 text-lg mb-8\">\n            This page is currently under development. Please check back soon for detailed overview information.\n          </p>\n          <div className=\"bg-neutral-800 rounded-lg p-8 max-w-2xl mx-auto\">\n            <h2 className=\"text-2xl font-semibold text-white mb-4\">Coming Soon</h2>\n            <p className=\"text-neutral-300\">\n              The overview page will provide comprehensive insights into your ESVC staking portfolio, \n              including performance metrics, earnings history, and detailed analytics.\n            </p>\n          </div>\n        </div>\n      </div>\n    </main>\n  );\n};\n\nexport default Overview;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1B,MAAMC,QAAkB,GAAGA,CAAA,KAAM;EAC/B,oBACED,OAAA;IAAME,SAAS,EAAC,oCAAoC;IAAAC,QAAA,eAClDH,OAAA;MAAKE,SAAS,EAAC,8BAA8B;MAAAC,QAAA,eAC3CH,OAAA;QAAKE,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1BH,OAAA;UAAIE,SAAS,EAAC,oCAAoC;UAAAC,QAAA,EAAC;QAAQ;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAChEP,OAAA;UAAGE,SAAS,EAAC,+BAA+B;UAAAC,QAAA,EAAC;QAE7C;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACJP,OAAA;UAAKE,SAAS,EAAC,iDAAiD;UAAAC,QAAA,gBAC9DH,OAAA;YAAIE,SAAS,EAAC,wCAAwC;YAAAC,QAAA,EAAC;UAAW;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACvEP,OAAA;YAAGE,SAAS,EAAC,kBAAkB;YAAAC,QAAA,EAAC;UAGhC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACF,CAAC;AAEX,CAAC;AAACC,EAAA,GApBIP,QAAkB;AAsBxB,eAAeA,QAAQ;AAAC,IAAAO,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}